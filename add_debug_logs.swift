#!/usr/bin/env swift

import Foundation

let filePath = "LanguageLearningApp/Features/Evaluation/Services/EvaluationService.swift"
let fileContent = try String(contentsOfFile: filePath, encoding: .utf8)

let oldCode = """
                // 尝试解析JSON并手动构建Evaluation
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    print("尝试手动解析JSON")

                    // 尝试将整个JSON转换为EvaluationAPIResponse
                    let jsonData = try JSONSerialization.data(withJSONObject: json)
                    if let apiResponse = try? decoder.decode(EvaluationAPIResponse.self, from: jsonData) {
                        print("成功解码为EvaluationAPIResponse")
                        if let apiEvaluation = apiResponse.data {
                            let evaluation = apiEvaluation.toEvaluation()
                            print("评估包含 \\(evaluation.sections.count) 个部分，总共 \\(evaluation.totalQuestions) 个问题")
                            return evaluation
                        } else {
                            print("EvaluationAPIResponse中data字段为nil")
                        }
                    } else {
                        print("无法解码为EvaluationAPIResponse")
                    }

                    // 检查是否有data字段
                    if let dataDict = json["data"] as? [String: Any] {
                        print("找到data字段，尝试解析")

                        // 尝试将data字段转换为JSON数据
                        let dataData = try JSONSerialization.data(withJSONObject: dataDict)

                        // 尝试解码data字段为Evaluation
                        if let evaluation = try? decoder.decode(Evaluation.self, from: dataData) {
                            print("成功从data字段解码为Evaluation")
                            print("评估包含 \\(evaluation.sections.count) 个部分，总共 \\(evaluation.totalQuestions) 个问题")
                            return evaluation
                        }
                    }
                }"""

let newCode = """
                // 尝试解析JSON并手动构建Evaluation
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    print("尝试手动解析JSON")
                    print("JSON顶层键: \\(json.keys.joined(separator: ", "))")
                    
                    // 打印JSON结构以便调试
                    if let jsonString = String(data: try JSONSerialization.data(withJSONObject: json, options: .prettyPrinted), encoding: .utf8) {
                        print("JSON结构: \\n\\(jsonString)")
                    }

                    // 尝试将整个JSON转换为EvaluationAPIResponse
                    let jsonData = try JSONSerialization.data(withJSONObject: json)
                    do {
                        let apiResponse = try decoder.decode(EvaluationAPIResponse.self, from: jsonData)
                        print("成功解码为EvaluationAPIResponse")
                        print("API响应: success=\\(apiResponse.success), message=\\(apiResponse.message)")
                        
                        if let apiEvaluation = apiResponse.data {
                            print("API数据: id=\\(apiEvaluation.id), title=\\(apiEvaluation.title)")
                            print("API数据: sections数量=\\(apiEvaluation.sections.count)")
                            
                            // 打印每个部分的信息
                            for (index, section) in apiEvaluation.sections.enumerated() {
                                print("部分\\(index+1): skill=\\(section.skill ?? "unknown"), title=\\(section.title ?? "未知")")
                                print("部分\\(index+1): 问题数量=\\(section.questions?.count ?? 0)")
                            }
                            
                            let evaluation = apiEvaluation.toEvaluation()
                            print("评估包含 \\(evaluation.sections.count) 个部分，总共 \\(evaluation.totalQuestions) 个问题")
                            return evaluation
                        } else {
                            print("EvaluationAPIResponse中data字段为nil")
                        }
                    } catch {
                        print("解码EvaluationAPIResponse失败: \\(error.localizedDescription)")
                        if let decodingError = error as? DecodingError {
                            switch decodingError {
                            case .typeMismatch(let type, let context):
                                print("类型不匹配: 期望\\(type)，位置: \\(context.codingPath)")
                            case .valueNotFound(let type, let context):
                                print("值未找到: 期望\\(type)，位置: \\(context.codingPath)")
                            case .keyNotFound(let key, let context):
                                print("键未找到: \\(key.stringValue)，位置: \\(context.codingPath)")
                            case .dataCorrupted(let context):
                                print("数据损坏: \\(context)")
                            @unknown default:
                                print("未知解码错误")
                            }
                        }
                        print("无法解码为EvaluationAPIResponse")
                    }

                    // 检查是否有data字段
                    if let dataDict = json["data"] as? [String: Any] {
                        print("找到data字段，尝试解析")
                        print("data字段键: \\(dataDict.keys.joined(separator: ", "))")
                        
                        // 检查data字段中是否有sections
                        if let sections = dataDict["sections"] as? [[String: Any]] {
                            print("找到sections字段，包含\\(sections.count)个部分")
                            
                            // 打印每个部分的信息
                            for (index, section) in sections.enumerated() {
                                print("部分\\(index+1)键: \\(section.keys.joined(separator: ", "))")
                                if let questions = section["questions"] as? [[String: Any]] {
                                    print("部分\\(index+1)包含\\(questions.count)个问题")
                                }
                            }
                        }

                        // 尝试将data字段转换为JSON数据
                        let dataData = try JSONSerialization.data(withJSONObject: dataDict)

                        // 尝试解码data字段为Evaluation
                        do {
                            let evaluation = try decoder.decode(Evaluation.self, from: dataData)
                            print("成功从data字段解码为Evaluation")
                            print("评估包含 \\(evaluation.sections.count) 个部分，总共 \\(evaluation.totalQuestions) 个问题")
                            return evaluation
                        } catch {
                            print("从data字段解码Evaluation失败: \\(error.localizedDescription)")
                            if let decodingError = error as? DecodingError {
                                switch decodingError {
                                case .typeMismatch(let type, let context):
                                    print("类型不匹配: 期望\\(type)，位置: \\(context.codingPath)")
                                case .valueNotFound(let type, let context):
                                    print("值未找到: 期望\\(type)，位置: \\(context.codingPath)")
                                case .keyNotFound(let key, let context):
                                    print("键未找到: \\(key.stringValue)，位置: \\(context.codingPath)")
                                case .dataCorrupted(let context):
                                    print("数据损坏: \\(context)")
                                @unknown default:
                                    print("未知解码错误")
                                }
                            }
                        }
                        
                        // 尝试手动构建Evaluation
                        print("尝试手动构建Evaluation")
                        if let id = dataDict["id"] as? String,
                           let userId = dataDict["userId"] as? String,
                           let sections = dataDict["sections"] as? [[String: Any]] {
                            
                            print("找到必要字段: id, userId, sections")
                            // 这里可以添加更多的手动构建逻辑
                        }
                    }
                }"""

let updatedContent = fileContent.replacingOccurrences(of: oldCode, with: newCode)

try updatedContent.write(toFile: filePath, atomically: true, encoding: .utf8)
print("File updated successfully")
