import Foundation
import SwiftUI
import Combine

@MainActor
class GrammarViewModel: ObservableObject {
    @Published var exercises: [GrammarExercise] = []
    @Published var currentExercise: GrammarExercise?
    @Published var selectedAnswer: String?
    @Published var selectedAnswerIndex: Int = -1
    @Published var correctAnswerIndex: Int = -1
    @Published var isAnswerSubmitted: Bool = false
    @Published var isAnswerCorrect: Bool = false
    @Published var hasAnswered: Bool = false
    @Published var progress: Double = 0.0
    @Published var showExplanation: Bool = false

    private let errorManager = ErrorManager.shared

    init() {
        // 先加载本地示例数据
        loadExercises()

        // 然后异步加载API数据
        Task {
            await loadExercisesFromAPI()
        }
    }

    private func loadExercises() {
        // 本地 fallback
        exercises = GrammarExercise.sampleExercises
        currentExercise = exercises.first
        updateProgress()
    }

    func loadExercisesFromAPI() async {
        do {
            let apiExercises: [GrammarExercise] = try await NetworkService.shared.request(.grammarExercises)
            DispatchQueue.main.async {
                self.exercises = apiExercises
                self.currentExercise = apiExercises.first
                self.selectedAnswer = nil
                self.isAnswerSubmitted = false
                self.showExplanation = false
                self.updateProgress()
            }
        } catch {
            print("Failed to load exercises from API: \(error)")
            // fallback to local
            DispatchQueue.main.async {
                self.loadExercises()
            }
        }
    }

    // 提交答案到API
    func submitAnswerToAPI() async {
        guard let exercise = currentExercise,
              let answer = selectedAnswer else { return }

        do {
            // 提交答案
            let result: APIResponse = try await NetworkService.shared.request(.submitGrammarAnswer(exerciseID: exercise.id, answer: answer))

            DispatchQueue.main.async {
                // 更新UI
                if let isCorrect = result.isCorrect {
                    self.isAnswerCorrect = isCorrect
                }

                // 更新用户统计信息
                if let user = UserManager.shared.currentUser {
                    let points = result.points ?? (self.isAnswerCorrect ? 10 : 2)
                    UserManager.shared.updateUserPoints(user.stats?.points ?? 0 + points)
                }

                // 通知练习完成
                NotificationCenter.default.post(
                    name: .exerciseCompleted,
                    object: nil,
                    userInfo: [
                        "isCorrect": self.isAnswerCorrect
                    ]
                )
            }
        } catch {
            print("Failed to submit answer: \(error)")
            errorManager.showError(.networkError("提交答案失败，请重试"))
        }
    }

    func selectAnswer(_ answer: String) {
        guard !isAnswerSubmitted else { return }
        selectedAnswer = answer
        checkAnswer()
    }

    func checkAnswer(selectedIndex: Int) {
        guard let exercise = currentExercise,
              !hasAnswered,
              selectedIndex >= 0,
              selectedIndex < exercise.options.count else { return }

        selectedAnswerIndex = selectedIndex
        selectedAnswer = exercise.options[selectedIndex]

        // Find correct answer index
        if let correctIndex = exercise.options.firstIndex(of: exercise.correctAnswer) {
            correctAnswerIndex = correctIndex
        }

        // Check if answer is correct
        isAnswerCorrect = selectedAnswer == exercise.correctAnswer
        isAnswerSubmitted = true
        hasAnswered = true
        showExplanation = true

        // 异步提交答案到API
        Task {
            await submitAnswerToAPI()
        }
    }

    private func checkAnswer() {
        guard let exercise = currentExercise,
              let selectedAnswer = selectedAnswer else { return }

        // Find the index of the selected answer
        if let index = exercise.options.firstIndex(of: selectedAnswer) {
            checkAnswer(selectedIndex: index)
        }
    }

    func nextExercise() {
        if let currentIndex = exercises.firstIndex(where: { $0.id == currentExercise?.id }),
           currentIndex < exercises.count - 1 {
            currentExercise = exercises[currentIndex + 1]
            selectedAnswer = nil
            selectedAnswerIndex = -1
            correctAnswerIndex = -1
            isAnswerSubmitted = false
            isAnswerCorrect = false
            hasAnswered = false
            showExplanation = false
            updateProgress()
        }
    }

    func previousExercise() {
        if let currentIndex = exercises.firstIndex(where: { $0.id == currentExercise?.id }),
           currentIndex > 0 {
            currentExercise = exercises[currentIndex - 1]
            selectedAnswer = nil
            selectedAnswerIndex = -1
            correctAnswerIndex = -1
            isAnswerSubmitted = false
            isAnswerCorrect = false
            hasAnswered = false
            showExplanation = false
            updateProgress()
        }
    }

    private func updateProgress() {
        guard let currentIndex = exercises.firstIndex(where: { $0.id == currentExercise?.id }) else { return }
        progress = Double(currentIndex + 1) / Double(exercises.count)
    }
}