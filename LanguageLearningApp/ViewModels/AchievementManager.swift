import Foundation
import Combine
import SwiftUI

@MainActor
class AchievementManager: ObservableObject, @preconcurrency AchievementManagerProtocol {
    static let shared = AchievementManager()

    @Published var achievements: [Achievement] = []
    @Published var userAchievements: [UserAchievement] = []

    private var cancellables = Set<AnyCancellable>()
    private let userManager: any UserManagerProtocol
    private let lessonManager: any LessonManagerProtocol
    private let storageManager: StorageManagerProtocol
    private let networkService: NetworkServiceProtocol
    private let errorManager: ErrorManager

    // 公开初始化方法，允许依赖注入
    init(
        userManager: any UserManagerProtocol = DependencyContainer.shared.resolve(UserManager.self),
        lessonManager: any LessonManagerProtocol = DependencyContainer.shared.resolve(LessonManager.self),
        storageManager: StorageManagerProtocol = DependencyContainer.shared.resolve(StorageManagerProtocol.self),
        networkService: NetworkServiceProtocol = DependencyContainer.shared.resolve(NetworkServiceProtocol.self),
        errorManager: ErrorManager = ErrorManager.shared
    ) {
        self.userManager = userManager
        self.lessonManager = lessonManager
        self.storageManager = storageManager
        self.networkService = networkService
        self.errorManager = errorManager

        loadAchievements()
        loadUserAchievements()
        setupNotifications()
        setupSubscriptions()

        // 异步加载API数据
        Task {
            await loadAchievementsFromAPI(completion: nil)
            await loadUserAchievementsFromAPI(completion: nil)
        }
    }

    private func setupSubscriptions() {
        // 当成就数据变化时，自动保存
        $achievements
            .dropFirst() // 忽略初始值
            .debounce(for: .seconds(0.5), scheduler: RunLoop.main) // 防止频繁保存
            .sink { [weak self] achievements in
                self?.saveAchievements()
            }
            .store(in: &cancellables)

        // 当用户成就数据变化时，自动保存
        $userAchievements
            .dropFirst()
            .debounce(for: .seconds(0.5), scheduler: RunLoop.main)
            .sink { [weak self] userAchievements in
                self?.saveUserAchievements()
            }
            .store(in: &cancellables)
    }

    private func loadAchievements() {
        // 从本地存储加载成就
        if let savedAchievements = storageManager.loadAchievements() {
            achievements = savedAchievements
        } else if AppEnvironment.current.useMockData {
            // 仅在开发环境且启用 Mock 数据时使用示例数据
            achievements = Achievement.sampleAchievements
            saveAchievements()
        }

        updateAchievementStatus()
    }

    private func loadUserAchievements() {
        // 从本地存储加载用户成就
        if let savedUserAchievements = storageManager.loadUserAchievements() {
            userAchievements = savedUserAchievements
        } else if AppEnvironment.current.useMockData {
            // 仅在开发环境且启用 Mock 数据时使用示例数据
            userAchievements = UserAchievement.sampleUserAchievements
            saveUserAchievements()
        }
    }

    func saveAchievements() {
        storageManager.saveAchievements(achievements)
    }

    func saveUserAchievements() {
        storageManager.saveUserAchievements(userAchievements)
    }

    private func setupNotifications() {
        NotificationCenter.default.publisher(for: .streakUpdated)
            .sink { [weak self] _ in
                self?.updateStreakAchievements()
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: .exerciseCompleted)
            .sink { [weak self] notification in
                if let userInfo = notification.userInfo,
                   let isCorrect = userInfo["isCorrect"] as? Bool {
                    self?.updateExerciseAchievements(isCorrect: isCorrect)
                }
            }
            .store(in: &cancellables)
    }

    private func updateStreakAchievements() {
        guard let user = userManager.currentUser,
              let stats = user.stats else { return }

        for (index, achievement) in achievements.enumerated() where achievement.type == .streak {
            if stats.streakDays >= achievement.requirement {
                achievements[index].isUnlocked = true
                achievements[index].unlockedDate = Date()
            }
            achievements[index].progress = stats.streakDays
        }
    }

    private func updateExerciseAchievements(isCorrect: Bool) {
        guard let user = userManager.currentUser,
              let stats = user.stats else { return }

        for (index, achievement) in achievements.enumerated() {
            switch achievement.type {
            case .vocabulary:
                achievements[index].progress = stats.vocabularyCount
                if stats.vocabularyCount >= achievement.requirement {
                    achievements[index].isUnlocked = true
                    achievements[index].unlockedDate = Date()
                }
            case .listening:
                achievements[index].progress = stats.listeningExerciseCount
                if stats.listeningExerciseCount >= achievement.requirement {
                    achievements[index].isUnlocked = true
                    achievements[index].unlockedDate = Date()
                }
            case .speaking:
                achievements[index].progress = stats.speakingExerciseCount
                if stats.speakingExerciseCount >= achievement.requirement {
                    achievements[index].isUnlocked = true
                    achievements[index].unlockedDate = Date()
                }
            default:
                break
            }
        }
    }

    func checkAchievementStatus() {
        updateAchievementStatus()
    }

    private func updateAchievementStatus() {
        guard let user = userManager.currentUser,
              let stats = user.stats else { return }

        for index in achievements.indices {
            var achievement = achievements[index]

            // 更新进度
            switch achievement.type {
            case .streak:
                achievement.progress = stats.streakDays
            case .vocabulary:
                achievement.progress = stats.vocabularyCount
            case .listening:
                achievement.progress = stats.listeningExerciseCount
            case .speaking:
                achievement.progress = stats.speakingExerciseCount
            case .lessons:
                achievement.progress = lessonManager.getCompletedLessons().count
            case .points:
                achievement.progress = stats.points
            case .challenges:
                achievement.progress = stats.completedChallenges
            case .social:
                achievement.progress = stats.helpedUsers
            }

            // 检查是否解锁
            if !achievement.isUnlocked && achievement.progress >= achievement.requirement {
                achievement.isUnlocked = true
                achievement.unlockedDate = Date()
                notifyAchievementUnlocked(achievement)
            }

            achievements[index] = achievement
        }
    }

    private func notifyAchievementUnlocked(_ achievement: Achievement) {
        // 显示成就解锁通知
        NotificationCenter.default.post(
            name: .achievementUnlocked,
            object: nil,
            userInfo: ["achievement": achievement]
        )

        // 发放奖励
        if let user = userManager.currentUser,
           let stats = user.stats {
            userManager.updateUserPoints(stats.points + achievement.reward)
        }
    }

    var inProgressAchievements: [Achievement] {
        achievements.filter { !$0.isUnlocked }
    }

    var unlockedAchievements: [Achievement] {
        achievements.filter { $0.isUnlocked }
    }

    func getAchievementProgress(for type: Achievement.AchievementType) -> Double {
        let typeAchievements = achievements.filter { $0.type == type }
        guard !typeAchievements.isEmpty else { return 0 }

        let totalProgress = typeAchievements.reduce(0) { $0 + $1.progress }
        let totalRequirement = typeAchievements.reduce(0) { $0 + $1.requirement }

        return Double(totalProgress) / Double(totalRequirement)
    }

    func getUnclaimedRewards() -> Int {
        achievements.filter { $0.isUnlocked && !$0.rewardClaimed }
            .reduce(0) { $0 + $1.reward }
    }

    func unlockAchievement(_ achievement: Achievement) {
        if let index = achievements.firstIndex(where: { $0.id == achievement.id }) {
            achievements[index].isUnlocked = true
            achievements[index].unlockedDate = Date()

            // 通知成就解锁
            notifyAchievementUnlocked(achievements[index])

            // 自动保存通过 setupSubscriptions() 中的订阅处理
        }
    }

    func claimReward(for achievement: Achievement) {
        guard let index = achievements.firstIndex(where: { $0.id == achievement.id }),
              achievement.isUnlocked && !achievement.rewardClaimed else { return }

        // 更新成就状态
        achievements[index].rewardClaimed = true

        // 更新用户积分
        if let user = userManager.currentUser,
           let stats = user.stats {
            userManager.updateUserPoints(stats.points + achievement.reward)
        }

        // 自动保存通过 setupSubscriptions() 中的订阅处理
    }

    // MARK: - 网络加载成就
    func loadAchievementsFromAPI(completion: (() -> Void)? = nil) async {
        do {
            let apiAchievements: [Achievement] = try await networkService.request(.achievements)
            DispatchQueue.main.async {
                self.achievements = apiAchievements
                self.saveAchievements()
                self.updateAchievementStatus()
                completion?()
            }
        } catch let error as AppError {
            print("Failed to load achievements from API: \(error)")
            // 显示错误信息但保留本地数据
            DispatchQueue.main.async {
                self.errorManager.showError(error)
                completion?()
            }
        } catch {
            print("Failed to load achievements from API: \(error)")
            // 显示通用错误信息但保留本地数据
            DispatchQueue.main.async {
                self.errorManager.showError(.networkError("Failed to load achievements"))
                completion?()
            }
        }
    }

    // 加载用户成就
    func loadUserAchievementsFromAPI(completion: (() -> Void)? = nil) async {
        do {
            let apiUserAchievements: [UserAchievement] = try await networkService.request(.userAchievements)
            DispatchQueue.main.async {
                self.userAchievements = apiUserAchievements
                self.saveUserAchievements()
                completion?()
            }
        } catch let error as AppError {
            print("Failed to load user achievements from API: \(error)")
            // 显示错误信息但保留本地数据
            DispatchQueue.main.async {
                self.errorManager.showError(error)
                completion?()
            }
        } catch {
            print("Failed to load user achievements from API: \(error)")
            // 显示通用错误信息但保留本地数据
            DispatchQueue.main.async {
                self.errorManager.showError(.networkError("Failed to load user achievements"))
                completion?()
            }
        }
    }

    // 领取成就奖励
    func claimAchievementRewardFromAPI(achievementID: UUID) async {
        do {
            let updatedAchievement: Achievement = try await networkService.request(.claimAchievementReward(achievementID: achievementID))

            DispatchQueue.main.async {
                // 更新本地成就列表
                if let index = self.achievements.firstIndex(where: { $0.id == achievementID }) {
                    self.achievements[index] = updatedAchievement
                }

                // 更新用户积分
                if let user = self.userManager.currentUser,
                   let stats = user.stats {
                    self.userManager.updateUserPoints(stats.points + updatedAchievement.reward)
                }
            }
        } catch {
            print("Failed to claim achievement reward: \(error)")
        }
    }

    // 实现 AchievementManagerProtocol 中的 updateAchievementStatusToAPI 方法
    func updateAchievementStatusToAPI(achievement: Achievement) async {
        do {
            // 创建一个专门的结构体来处理编码
            struct AchievementUpdate: Codable {
                let isUnlocked: Bool
                let progress: Int
            }

            let updateData = AchievementUpdate(
                isUnlocked: achievement.isUnlocked,
                progress: achievement.progress
            )

            // 使用 custom APIEndpoint 因为 updateAchievementStatus 可能不存在
            let endpoint: APIEndpoint = .custom(
                url: APIEndpoint.baseURL.appendingPathComponent("/achievements/\(achievement.id)/status"),
                method: "PATCH",
                headers: [:],
                bodyData: try JSONEncoder().encode(updateData)
            )

            let updatedAchievement: Achievement = try await networkService.request(endpoint)

            DispatchQueue.main.async {
                // 更新本地成就列表
                if let index = self.achievements.firstIndex(where: { $0.id == achievement.id }) {
                    self.achievements[index] = updatedAchievement
                }
            }
        } catch {
            print("Failed to update achievement status: \(error)")
        }
    }
}