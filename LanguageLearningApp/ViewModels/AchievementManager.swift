import Foundation
import Combine
import SwiftUI

@MainActor
class AchievementManager: ObservableObject, @preconcurrency AchievementManagerProtocol {
    static let shared = AchievementManager()

    @Published var achievements: [Achievement] = []
    @Published var userAchievements: [UserAchievement] = []

    private var cancellables = Set<AnyCancellable>()
    private let userManager: any UserManagerProtocol
    private let lessonManager: any LessonManagerProtocol
    private let storageManager: StorageManagerProtocol
    private let networkService: NetworkServiceProtocol
    private let errorManager: ErrorManager

    // 公开初始化方法，允许依赖注入
    init(
        userManager: (any UserManagerProtocol)? = nil,
        lessonManager: (any LessonManagerProtocol)? = nil,
        storageManager: StorageManagerProtocol? = nil,
        networkService: NetworkServiceProtocol? = nil,
        errorManager: ErrorManager? = nil
    ) {
        print("🏆 [AchievementManager] 初始化开始")

        // 延迟解析依赖项，避免在注册过程中触发解析
        if let userManager = userManager {
            self.userManager = userManager
        } else {
            // 尝试从容器解析，如果失败则使用默认值
            do {
                self.userManager = try DependencyContainer.shared.tryResolve(UserManager.self)
                print("🏆 [AchievementManager] 成功从容器解析 UserManager")
            } catch {
                print("🏆 [AchievementManager] 无法从容器解析 UserManager，使用 shared 实例: \(error)")
                self.userManager = UserManager.shared
            }
        }

        if let lessonManager = lessonManager {
            self.lessonManager = lessonManager
        } else {
            do {
                self.lessonManager = try DependencyContainer.shared.tryResolve(LessonManager.self)
                print("🏆 [AchievementManager] 成功从容器解析 LessonManager")
            } catch {
                print("🏆 [AchievementManager] 无法从容器解析 LessonManager，使用默认实例: \(error)")
                self.lessonManager = LessonManager()
            }
        }

        if let storageManager = storageManager {
            self.storageManager = storageManager
        } else {
            do {
                self.storageManager = try DependencyContainer.shared.tryResolve(StorageManagerProtocol.self)
                print("🏆 [AchievementManager] 成功从容器解析 StorageManager")
            } catch {
                print("🏆 [AchievementManager] 无法从容器解析 StorageManager，使用默认实例: \(error)")
                self.storageManager = StorageManager.shared
            }
        }

        if let networkService = networkService {
            self.networkService = networkService
        } else {
            do {
                self.networkService = try DependencyContainer.shared.tryResolve(NetworkServiceProtocol.self)
                print("🏆 [AchievementManager] 成功从容器解析 NetworkService")
            } catch {
                print("🏆 [AchievementManager] 无法从容器解析 NetworkService，使用默认实例: \(error)")
                self.networkService = NetworkService.shared
            }
        }

        self.errorManager = errorManager ?? ErrorManager.shared

        print("🏆 [AchievementManager] 初始化完成，开始加载数据")
        loadAchievements()
        loadUserAchievements()
        setupNotifications()
        setupSubscriptions()

        // 异步加载API数据
        Task {
            await loadAchievementsFromAPI(completion: nil)
            await loadUserAchievementsFromAPI(completion: nil)
        }
    }

    private func setupSubscriptions() {
        // 当成就数据变化时，自动保存
        $achievements
            .dropFirst() // 忽略初始值
            .debounce(for: .seconds(0.5), scheduler: RunLoop.main) // 防止频繁保存
            .sink { [weak self] achievements in
                self?.saveAchievements()
            }
            .store(in: &cancellables)

        // 当用户成就数据变化时，自动保存
        $userAchievements
            .dropFirst()
            .debounce(for: .seconds(0.5), scheduler: RunLoop.main)
            .sink { [weak self] userAchievements in
                self?.saveUserAchievements()
            }
            .store(in: &cancellables)
    }

    private func loadAchievements() {
        // 从本地存储加载成就
        if let savedAchievements = storageManager.loadAchievements() {
            achievements = savedAchievements
        } else if AppEnvironment.current.useMockData {
            // 仅在开发环境且启用 Mock 数据时使用示例数据
            achievements = Achievement.sampleAchievements
            saveAchievements()
        }

        updateAchievementStatus()
    }

    private func loadUserAchievements() {
        // 从本地存储加载用户成就
        if let savedUserAchievements = storageManager.loadUserAchievements() {
            userAchievements = savedUserAchievements
        } else if AppEnvironment.current.useMockData {
            // 仅在开发环境且启用 Mock 数据时使用示例数据
            userAchievements = UserAchievement.sampleUserAchievements
            saveUserAchievements()
        }
    }

    func saveAchievements() {
        storageManager.saveAchievements(achievements)
    }

    func saveUserAchievements() {
        storageManager.saveUserAchievements(userAchievements)
    }

    private func setupNotifications() {
        NotificationCenter.default.publisher(for: .streakUpdated)
            .sink { [weak self] _ in
                self?.updateStreakAchievements()
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: .exerciseCompleted)
            .sink { [weak self] notification in
                if let userInfo = notification.userInfo,
                   let isCorrect = userInfo["isCorrect"] as? Bool {
                    self?.updateExerciseAchievements(isCorrect: isCorrect)
                }
            }
            .store(in: &cancellables)
    }

    private func updateStreakAchievements() {
        guard let user = userManager.currentUser,
              let stats = user.stats else { return }

        for (index, achievement) in achievements.enumerated() where achievement.type == .streak {
            if stats.streakDays >= achievement.requirement {
                achievements[index].isUnlocked = true
                achievements[index].unlockedDate = Date()
            }
            achievements[index].progress = stats.streakDays
        }
    }

    private func updateExerciseAchievements(isCorrect: Bool) {
        guard let user = userManager.currentUser,
              let stats = user.stats else { return }

        for (index, achievement) in achievements.enumerated() {
            switch achievement.type {
            case .vocabulary:
                achievements[index].progress = stats.vocabularyCount
                if stats.vocabularyCount >= achievement.requirement {
                    achievements[index].isUnlocked = true
                    achievements[index].unlockedDate = Date()
                }
            case .listening:
                achievements[index].progress = stats.listeningExerciseCount
                if stats.listeningExerciseCount >= achievement.requirement {
                    achievements[index].isUnlocked = true
                    achievements[index].unlockedDate = Date()
                }
            case .speaking:
                achievements[index].progress = stats.speakingExerciseCount
                if stats.speakingExerciseCount >= achievement.requirement {
                    achievements[index].isUnlocked = true
                    achievements[index].unlockedDate = Date()
                }
            default:
                break
            }
        }
    }

    func checkAchievementStatus() {
        updateAchievementStatus()
    }

    private func updateAchievementStatus() {
        guard let user = userManager.currentUser,
              let stats = user.stats else { return }

        for index in achievements.indices {
            var achievement = achievements[index]

            // 更新进度
            switch achievement.type {
            case .streak:
                achievement.progress = stats.streakDays
            case .vocabulary:
                achievement.progress = stats.vocabularyCount
            case .listening:
                achievement.progress = stats.listeningExerciseCount
            case .speaking:
                achievement.progress = stats.speakingExerciseCount
            case .lessons:
                achievement.progress = lessonManager.getCompletedLessons().count
            case .points:
                achievement.progress = stats.points
            case .challenges:
                achievement.progress = stats.completedChallenges
            case .social:
                achievement.progress = stats.helpedUsers
            }

            // 检查是否解锁
            if !achievement.isUnlocked && achievement.progress >= achievement.requirement {
                achievement.isUnlocked = true
                achievement.unlockedDate = Date()
                notifyAchievementUnlocked(achievement)
            }

            achievements[index] = achievement
        }
    }

    private func notifyAchievementUnlocked(_ achievement: Achievement) {
        // 显示成就解锁通知
        NotificationCenter.default.post(
            name: .achievementUnlocked,
            object: nil,
            userInfo: ["achievement": achievement]
        )

        // 发放奖励
        if let user = userManager.currentUser,
           let stats = user.stats {
            Task {
                await userManager.updateUserPoints(stats.points + achievement.reward)
            }
        }
    }

    var inProgressAchievements: [Achievement] {
        achievements.filter { !$0.isUnlocked }
    }

    var unlockedAchievements: [Achievement] {
        achievements.filter { $0.isUnlocked }
    }

    func getAchievementProgress(for type: Achievement.AchievementType) -> Double {
        let typeAchievements = achievements.filter { $0.type == type }
        guard !typeAchievements.isEmpty else { return 0 }

        let totalProgress = typeAchievements.reduce(0) { $0 + $1.progress }
        let totalRequirement = typeAchievements.reduce(0) { $0 + $1.requirement }

        return Double(totalProgress) / Double(totalRequirement)
    }

    func getUnclaimedRewards() -> Int {
        achievements.filter { $0.isUnlocked && !$0.rewardClaimed }
            .reduce(0) { $0 + $1.reward }
    }

    func unlockAchievement(_ achievement: Achievement) {
        if let index = achievements.firstIndex(where: { $0.id == achievement.id }) {
            achievements[index].isUnlocked = true
            achievements[index].unlockedDate = Date()

            // 通知成就解锁
            notifyAchievementUnlocked(achievements[index])

            // 自动保存通过 setupSubscriptions() 中的订阅处理
        }
    }

    func claimReward(for achievement: Achievement) {
        guard let index = achievements.firstIndex(where: { $0.id == achievement.id }),
              achievement.isUnlocked && !achievement.rewardClaimed else { return }

        // 更新成就状态
        achievements[index].rewardClaimed = true

        // 更新用户积分
        if let user = userManager.currentUser,
           let stats = user.stats {
            Task {
                await userManager.updateUserPoints(stats.points + achievement.reward)
            }
        }

        // 自动保存通过 setupSubscriptions() 中的订阅处理
    }

    // MARK: - 网络加载成就
    func loadAchievementsFromAPI(completion: (() -> Void)? = nil) async {
        do {
            let apiAchievements: [Achievement] = try await networkService.request(.achievements)
            DispatchQueue.main.async {
                self.achievements = apiAchievements
                self.saveAchievements()
                self.updateAchievementStatus()
                completion?()
            }
        } catch {
            DispatchQueue.main.async {
                self.handleError(error, context: "加载成就列表")
                completion?()
            }
        }
    }

    // 加载用户成就
    func loadUserAchievementsFromAPI(completion: (() -> Void)? = nil) async {
        do {
            let apiUserAchievements: [UserAchievement] = try await networkService.request(.userAchievements)
            DispatchQueue.main.async {
                self.userAchievements = apiUserAchievements
                self.saveUserAchievements()
                completion?()
            }
        } catch {
            DispatchQueue.main.async {
                self.handleError(error, context: "加载用户成就")
                completion?()
            }
        }
    }

    // 领取成就奖励
    func claimAchievementRewardFromAPI(achievementID: UUID) async {
        do {
            let updatedAchievement: Achievement = try await networkService.request(.claimAchievementReward(achievementID: achievementID))

            DispatchQueue.main.async {
                // 更新本地成就列表
                if let index = self.achievements.firstIndex(where: { $0.id == achievementID }) {
                    self.achievements[index] = updatedAchievement
                }

                // 更新用户积分
                if let user = self.userManager.currentUser,
                   let stats = user.stats {
                    Task {
                        await self.userManager.updateUserPoints(stats.points + updatedAchievement.reward)
                    }
                }
            }
        } catch {
            print("Failed to claim achievement reward: \(error)")
        }
    }

    // 实现 AchievementManagerProtocol 中的 updateAchievementStatusToAPI 方法
    func updateAchievementStatusToAPI(achievement: Achievement) async {
        do {
            // 创建一个专门的结构体来处理编码
            struct AchievementUpdate: Codable {
                let isUnlocked: Bool
                let progress: Int
            }

            let updateData = AchievementUpdate(
                isUnlocked: achievement.isUnlocked,
                progress: achievement.progress
            )

            // 使用 custom APIEndpoint 因为 updateAchievementStatus 可能不存在
            let endpoint: APIEndpoint = .custom(
                url: APIEndpoint.baseURL.appendingPathComponent("/achievements/\(achievement.id)/status"),
                method: "PATCH",
                headers: [:],
                bodyData: try JSONEncoder().encode(updateData)
            )

            let updatedAchievement: Achievement = try await networkService.request(endpoint)

            DispatchQueue.main.async {
                // 更新本地成就列表
                if let index = self.achievements.firstIndex(where: { $0.id == achievement.id }) {
                    self.achievements[index] = updatedAchievement
                }
            }
        } catch {
            self.handleError(error, context: "更新成就状态")
        }
    }

    // MARK: - Smart Recommendation Algorithm

    /// 获取推荐的成就（基于智能算法）
    public func getRecommendedAchievements(limit: Int = 3) -> [Achievement] {
        // 计算所有未解锁成就的推荐优先级
        let achievementsWithPriority = inProgressAchievements.compactMap { achievement -> (Achievement, Double)? in
            let priority = calculateAchievementPriority(achievement: achievement)
            return (achievement, priority)
        }

        // 按优先级排序并返回前N个
        let sortedAchievements = achievementsWithPriority
            .sorted { $0.1 > $1.1 } // 优先级从高到低
            .prefix(limit)
            .map { $0.0 }

        return Array(sortedAchievements)
    }

    /// 计算成就推荐优先级
    private func calculateAchievementPriority(achievement: Achievement) -> Double {
        var priority: Double = 1.0

        // 1. 进度权重 - 接近完成的成就优先级更高
        let progressRatio = Double(achievement.progress) / Double(achievement.requirement)
        priority += progressRatio * 3.0 // 进度越高，优先级越高

        // 2. 类型权重 - 根据成就类型调整优先级
        let typeWeight = getAchievementTypeWeight(achievement.type)
        priority += typeWeight

        // 3. 奖励权重 - 奖励越高，优先级稍高
        let rewardWeight = Double(achievement.reward) / 1000.0 // 标准化奖励值
        priority += rewardWeight * 0.5

        // 4. 难度权重 - 容易完成的成就优先级更高
        let difficultyWeight = calculateDifficultyWeight(achievement: achievement)
        priority += difficultyWeight

        return priority
    }

    /// 获取成就类型权重
    private func getAchievementTypeWeight(_ type: Achievement.AchievementType) -> Double {
        switch type {
        case .streak: return 2.0 // 连续学习最重要
        case .vocabulary: return 1.5 // 词汇量很重要
        case .lessons: return 1.3 // 课程完成重要
        case .listening: return 1.2 // 听力练习
        case .speaking: return 1.2 // 口语练习
        case .points: return 1.0 // 积分
        case .challenges: return 0.8 // 挑战
        case .social: return 0.5 // 社交功能
        }
    }

    /// 计算难度权重
    private func calculateDifficultyWeight(achievement: Achievement) -> Double {
        let progressRatio = Double(achievement.progress) / Double(achievement.requirement)

        // 根据当前进度和要求计算难度
        if progressRatio >= 0.8 { // 80%以上进度，容易完成
            return 1.5
        } else if progressRatio >= 0.5 { // 50%以上进度，中等难度
            return 1.0
        } else if progressRatio >= 0.2 { // 20%以上进度，较难
            return 0.5
        } else { // 20%以下进度，很难
            return 0.2
        }
    }

    // MARK: - Batch Operations

    /// 批量解锁成就
    @MainActor
    public func batchUnlockAchievements(achievementIds: [UUID]) async {
        var successCount = 0
        var failedIds: [UUID] = []

        for achievementId in achievementIds {
            if let achievement = achievements.first(where: { $0.id == achievementId }) {
                do {
                    await updateAchievementStatusToAPI(achievement: achievement)
                    unlockAchievement(achievement)
                    successCount += 1
                } catch {
                    failedIds.append(achievementId)
                    print("Failed to unlock achievement \(achievementId): \(error)")
                }
            } else {
                failedIds.append(achievementId)
            }
        }

        if !failedIds.isEmpty {
            self.handleError(
                AppError.progressSyncFailed("批量解锁失败：\(failedIds.count) 个成就"),
                context: "批量解锁成就"
            )
        }

        print("批量解锁成就：成功 \(successCount)，失败 \(failedIds.count)")
    }

    /// 批量领取奖励
    @MainActor
    public func batchClaimRewards(achievementIds: [UUID]) async {
        var successCount = 0
        var failedIds: [UUID] = []

        for achievementId in achievementIds {
            if let achievement = achievements.first(where: { $0.id == achievementId && $0.isUnlocked && !$0.rewardClaimed }) {
                do {
                    await claimAchievementRewardFromAPI(achievementID: achievementId)
                    claimReward(for: achievement)
                    successCount += 1
                } catch {
                    failedIds.append(achievementId)
                    print("Failed to claim reward for achievement \(achievementId): \(error)")
                }
            } else {
                failedIds.append(achievementId)
            }
        }

        if !failedIds.isEmpty {
            self.handleError(
                AppError.progressSyncFailed("批量领取失败：\(failedIds.count) 个奖励"),
                context: "批量领取奖励"
            )
        }

        print("批量领取奖励：成功 \(successCount)，失败 \(failedIds.count)")
    }

    /// 同步所有成就数据
    @MainActor
    public func syncAllAchievementData() async {
        do {
            // 同步成就列表
            await loadAchievementsFromAPI()

            // 同步用户成就
            await loadUserAchievementsFromAPI()

            print("成就数据同步完成")
        } catch {
            self.handleError(error, context: "同步成就数据")
        }
    }

    // MARK: - Error Handling

    /// 统一错误处理方法
    @MainActor
    private func handleError(_ error: Error, context: String) {
        let appError = AppError.from(error)

        // 使用 ErrorHandler 进行统一处理
        ErrorHandler.shared.handle(appError, context: "AchievementManager - \(context)")

        // 显示错误信息
        errorManager.showError(appError)

        // 记录详细错误信息
        print("❌ [AchievementManager] \(context) 失败: \(appError.localizedDescription)")
    }

    /// 处理网络错误的重试逻辑
    @MainActor
    private func handleNetworkError(_ error: Error, retryAction: @escaping () async throws -> Void) async {
        let appError = AppError.from(error)

        // 如果是网络错误，可以考虑重试
        if case .networkError = appError, case .noInternetConnection = appError {
            // 网络错误，暂时不重试，直接处理
            handleError(error, context: "网络请求")
        } else {
            handleError(error, context: "网络请求")
        }
    }
}