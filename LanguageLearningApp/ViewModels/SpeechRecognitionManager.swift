import Foundation
import Speech
import AVFoundation

class SpeechRecognitionManager: ObservableObject {
    @Published var isRecording = false
    @Published var transcribedText = ""
    @Published var errorMessage: String?

    private var audioEngine = AVAudioEngine()
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))

    init() {
        requestAuthorization()
    }

    private func requestAuthorization() {
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    self?.errorMessage = nil
                case .denied:
                    self?.errorMessage = "语音识别权限被拒绝"
                case .restricted:
                    self?.errorMessage = "语音识别功能受限"
                case .notDetermined:
                    self?.errorMessage = "语音识别权限未确定"
                @unknown default:
                    self?.errorMessage = "未知错误"
                }
            }
        }
    }

    func startRecording() {
        guard !isRecording else { return }

        do {
            // 配置音频会话
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .default)
            try audioSession.setActive(true)

            // 创建识别请求
            recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
            guard let recognitionRequest = recognitionRequest,
                  let speechRecognizer = speechRecognizer else {
                throw NSError(domain: "SpeechRecognition", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法创建识别请求"])
            }

            recognitionRequest.shouldReportPartialResults = true

            // 配置音频输入
            let inputNode = audioEngine.inputNode

            // 确保音频格式有效
            guard let recordingFormat = inputNode.inputFormat(forBus: 0) as? AVAudioFormat,
                  recordingFormat.sampleRate > 0,
                  recordingFormat.channelCount > 0 else {
                throw NSError(domain: "SpeechRecognition", code: 2, userInfo: [NSLocalizedDescriptionKey: "无效的音频格式"])
            }

            // 开始识别任务
            recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
                guard let self = self else { return }

                var isFinal = false

                if let result = result {
                    self.transcribedText = result.bestTranscription.formattedString
                    isFinal = result.isFinal
                }

                if error != nil || isFinal {
                    self.stopRecording()
                }
            }

            // 安装音频输入 tap
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
                recognitionRequest.append(buffer)
            }

            // 启动音频引擎
            audioEngine.prepare()
            try audioEngine.start()

            isRecording = true
            errorMessage = nil
        } catch {
            errorMessage = "启动录音失败：\(error.localizedDescription)"
            print("启动录音失败：\(error)")
        }
    }

    func stopRecording() {
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        recognitionTask?.cancel()
        recognitionTask = nil

        isRecording = false
    }

    func reset() {
        stopRecording()
        transcribedText = ""
        errorMessage = nil
    }
}