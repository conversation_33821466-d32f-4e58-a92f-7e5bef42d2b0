import Foundation
import SwiftUI
import Combine

class DailyPracticeViewModel: ObservableObject {
    // Published properties
    @Published var cards: [PracticeCardModel] = []
    @Published var isLoading: Bool = false
    @Published var error: Error?

    private let apiDataSource = APIDataSourceManager.shared
    @Published var currentCardIndex: Int = 0
    @Published var confirmedCards: [PracticeCardModel] = []
    @Published var skippedCards: [PracticeCardModel] = []
    @Published var isRefreshing: Bool = false

    // Managers
    private let vocabularyManager: VocabularyManager
    private let errorManager: ErrorManager

    // 依赖注入的管理器
    private let practiceManager: PracticeManager

    // Cancellables for Combine subscriptions
    private var cancellables = Set<AnyCancellable>()

    init(
        vocabularyManager: VocabularyManager = VocabularyManager.shared,
        errorManager: ErrorManager = ErrorManager.shared,
        practiceManager: PracticeManager = DependencyContainer.shared.resolve(PracticeManager.self)
    ) {
        self.vocabularyManager = vocabularyManager
        self.errorManager = errorManager
        self.practiceManager = practiceManager

        // 根据环境加载数据
        if AppEnvironment.current.useMockData {
            // 仅在开发环境且启用 Mock 数据时加载示例卡片
            loadSampleCards()
        }

        // 总是尝试从 API 加载数据
        Task {
            await loadCardsFromAPI()
        }
    }

    // MARK: - Card Management

    /// Load sample cards for initial state (仅在开发环境使用)
    func loadSampleCards() {
        if AppEnvironment.current.useMockData {
            cards = PracticeCardModel.samples
        }
    }

    /// Load cards from API
    func loadCardsFromAPI() async {
        DispatchQueue.main.async {
            self.isLoading = true
            self.error = nil
        }

        do {
            // Create an array to hold all the cards
            var newCards: [PracticeCardModel] = []

            // Load word cards from vocabulary manager
            await vocabularyManager.loadWords()
            let words = vocabularyManager.words
            for word in words.prefix(3) { // Limit to 3 word cards
                newCards.append(createWordCard(from: word))
            }

            // Load practice sessions from practice manager and convert to cards
            let practiceCards = practiceManager.getRecommendedPractice()
            for session in practiceCards.prefix(3) { // Limit to 3 practice cards
                newCards.append(createPracticeCard(from: session))
            }

            // Shuffle the cards for variety
            newCards.shuffle()

            // Update the UI on the main thread
            DispatchQueue.main.async {
                self.cards = newCards
                self.isLoading = false
                self.isRefreshing = false
                self.currentCardIndex = 0
            }
        } catch {
            // Handle errors
            DispatchQueue.main.async {
                self.error = error
                self.isLoading = false
                self.isRefreshing = false
                // Use a simple error message instead of AppError
                self.errorManager.showError(message: "Failed to load practice cards: \(error.localizedDescription)")

                // 如果没有卡片且在开发环境，加载示例数据作为备选
                if self.cards.isEmpty && AppEnvironment.current.useMockData {
                    self.loadSampleCards()
                }
            }
        }
    }

    // MARK: - Card Creation Helpers

    /// Create a word card from a Word model
    private func createWordCard(from word: Word) -> PracticeCardModel {
        return PracticeCardModel(
            type: PracticeCardModel.PracticeCardType.word,
            title: word.text,
            description: "Learn the meaning and usage of '\(word.text)'",
            icon: PracticeCardModel.PracticeCardType.word.icon,
            color: PracticeCardModel.PracticeCardType.word.color,
            difficulty: PracticeCardModel.Difficulty.easy,
            content: PracticeCardContent.word(word)
        )
    }

    /// Create a practice card from a PracticeSession model
    private func createPracticeCard(from session: PracticeSession) -> PracticeCardModel {
        let cardType: PracticeCardModel.PracticeCardType
        let title: String
        let description: String

        switch session.type {
        case .vocabulary:
            cardType = .word
            title = "Vocabulary Practice"
            description = "Review vocabulary words"
        case .listening:
            cardType = .listening
            title = "Listening Practice"
            description = "Practice listening comprehension"
        case .speaking:
            cardType = .speaking
            title = "Speaking Practice"
            description = "Practice pronunciation and speaking"
        case .grammar:
            cardType = .grammar
            title = "Grammar Practice"
            description = "Practice grammar rules"
        case .reading:
            cardType = .word // Use word type for reading
            title = "Reading Practice"
            description = "Practice reading comprehension"
        case .writing:
            cardType = .grammar // Use grammar type for writing
            title = "Writing Practice"
            description = "Practice writing skills"
        case .pronunciation:
            cardType = .speaking // Use speaking type for pronunciation
            title = "Pronunciation Practice"
            description = "Practice pronunciation skills"
        case .comprehensive:
            cardType = .grammar // Use grammar type for comprehensive
            title = "Comprehensive Practice"
            description = "Practice multiple skills"
        }

        // Create a generic exercise for the practice session
        let exercise = Exercise(
            id: UUID(uuidString: session.id.uuidString) ?? UUID(),
            type: .multipleChoice,
            question: title,
            instruction: description,
            options: ["Option 1", "Option 2", "Option 3", "Option 4"],
            correctAnswer: "Option 1",
            explanation: "This is a practice exercise based on your previous session."
        )

        return PracticeCardModel(
            type: cardType,
            title: title,
            description: description,
            icon: cardType.icon,
            color: cardType.color,
            difficulty: .medium, // Default to medium difficulty
            content: .exercise(exercise)
        )
    }



    // MARK: - Card Actions

    /// Handle confirming (right swipe) the current card
    func confirmCurrentCard() {
        guard currentCardIndex < cards.count else { return }

        let card = cards[currentCardIndex]
        confirmedCards.append(card)

        // Move to next card
        moveToNextCard()

        // Record the confirmation
        Task {
            await recordCardAction(card: card, action: CardAction.confirm)
        }
    }

    /// Handle skipping (left swipe) the current card
    func skipCurrentCard() {
        guard currentCardIndex < cards.count else { return }

        let card = cards[currentCardIndex]
        skippedCards.append(card)

        // Move to next card
        moveToNextCard()

        // Record the skip
        Task {
            await recordCardAction(card: card, action: CardAction.skip)
        }
    }

    /// Move to the next card
    private func moveToNextCard() {
        withAnimation {
            currentCardIndex += 1
        }
    }

    // MARK: - API Interactions

    /// Record a card action (confirm or skip) to the API
    private func recordCardAction(card: PracticeCardModel, action: CardAction) async {
        // This would typically send the action to an API
        // For now, we'll just print it
        print("Recorded card action: \(action.rawValue) for card \(card.id)")
    }

    // MARK: - Utility Functions

    /// Refresh the cards
    func refreshCards() async {
        DispatchQueue.main.async {
            self.isRefreshing = true
        }

        await loadCardsFromAPI()
    }
}

// MARK: - Card Action Enum
enum CardAction: String {
    case confirm
    case skip
}


