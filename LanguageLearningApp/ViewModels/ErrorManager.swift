import Foundation
import SwiftUI
import Combine

/// 错误管理器，负责处理和显示应用程序错误
class ErrorManager: ObservableObject, ErrorManagerProtocol {
    static let shared = ErrorManager()

    @Published var currentError: AppError?
    @Published var showError: Bool = false
    @Published var errorLog: [ErrorLogEntry] = []

    private var cancellables = Set<AnyCancellable>()
    private let storageManager: StorageManagerProtocol

    private init(storageManager: StorageManagerProtocol = StorageManager.shared) {
        self.storageManager = storageManager

        // 监听错误状态变化
        $currentError
            .dropFirst()
            .sink { [weak self] error in
                // 记录错误
                if let error = error {
                    self?.recordError(error)
                }
            }
            .store(in: &cancellables)
        
        logInfo(AppError.customError("ErrorManager 初始化完成"))
    }

    /// 获取错误消息
    private func getErrorMessage(_ error: AppError) -> String {
        switch error {
        case .customError(let message):
            return message
        default:
            return "错误类型: \(String(describing: error))"
        }
    }

    /// 显示错误
    func showError(_ error: AppError) {
        currentError = error
        showError = true
        
        // 使用Logger记录错误
        let errorMessage = getErrorMessage(error)
        switch error.severity {
        case .info:
            logInfo(AppError.customError("显示信息: \(errorMessage)"))
        case .warning:
            logWarning(AppError.customError("显示警告: \(errorMessage)"))
        case .error:
            logError(AppError.customError("显示错误: \(errorMessage)"))
        }
    }

    /// 显示错误消息
    func showError(message: String, severity: ErrorSeverity = .warning) {
        let error = AppError.customError(message)
        currentError = error
        showError = true
        
        // 使用Logger记录错误消息
        switch severity {
        case .info:
            logInfo(AppError.customError("显示信息: \(message)"))
        case .warning:
            logWarning(AppError.customError("显示警告: \(message)"))
        case .error:
            logError(AppError.customError("显示错误: \(message)"))
        }
    }

    /// 清除当前错误
    func clearError() {
        if let error = currentError {
            let errorMessage = getErrorMessage(error)
            logDebug(AppError.customError("清除错误: \(errorMessage)"))
        }
        currentError = nil
        showError = false
    }

    /// 记录错误
    func logError(_ error: AppError) {
        recordError(error)
    }

    /// 内部错误记录方法
    private func recordError(_ error: AppError) {
        let entry = ErrorLogEntry(
            timestamp: Date(),
            error: error
        )
        errorLog.append(entry)
        
        // 使用Logger记录错误详情，避免递归调用
        let errorMessage = getErrorMessage(error)
        
        // 使用直接日志记录，避免递归
        switch error.severity {
        case .info:
            print("INFO: 记录信息: \(errorMessage)")
        case .warning:
            print("WARNING: 记录警告: \(errorMessage)")
        case .error:
            print("ERROR: 记录错误: \(errorMessage)")
        }

        // 限制日志大小
        if errorLog.count > 100 {
            errorLog.removeFirst()
            print("DEBUG: 错误日志超过100条，已移除最早的记录")
        }
    }

    /// 获取错误日志
    func getErrorLog() -> [ErrorLogEntry] {
        print("DEBUG: 获取错误日志，共\(errorLog.count)条")
        return errorLog
    }

    /// 清除错误日志
    func clearErrorLog() {
        print("INFO: 清除错误日志，共\(errorLog.count)条")
        errorLog.removeAll()
    }

    /// 导出错误日志
    func exportErrorLog() -> Data? {
        print("INFO: 请求导出错误日志")
        
        // 创建错误日志内容
        var logContent = "Time,Severity,Error\n"
        for entry in errorLog {
            let errorMessage = getErrorMessage(entry.error)
            logContent += "\(entry.formattedTimestamp),\(entry.error.severity),\"\(errorMessage)\"\n"
        }
        
        // 转换为数据
        guard let data = logContent.data(using: .utf8) else {
            print("ERROR: 导出错误日志失败：无法创建数据")
            return nil
        }
        
        print("INFO: 错误日志导出成功，共\(errorLog.count)条")
        return data
    }
}

/// 错误日志条目
struct ErrorLogEntry: Identifiable {
    let id = UUID()
    let timestamp: Date
    let error: AppError

    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: timestamp)
    }
}
