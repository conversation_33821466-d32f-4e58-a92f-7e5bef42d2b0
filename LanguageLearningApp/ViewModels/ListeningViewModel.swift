import Foundation
import SwiftUI
import AVFoundation

@MainActor
class ListeningViewModel: ObservableObject {
    @Published var currentExercise: ListeningExercise?
    @Published var currentQuestionIndex: Int = 0
    @Published var selectedAnswer: Int?
    @Published var isAnswerSubmitted: Bool = false
    @Published var isAnswerCorrect: Bool = false
    @Published var isPlaying: Bool = false
    @Published var showTranscript: Bool = false
    @Published var progress: Double = 0.0
    @Published var currentLanguage: String = "en-US"

    // 服务和管理器
    private let networkService: NetworkServiceProtocol
    private let errorManager: ErrorManager
    private let userManager: UserManager
    private let ttsManager: TTSManager

    private var audioPlayer: AVAudioPlayer?
    private var exercises: [ListeningExercise] = []

    init(
        networkService: NetworkServiceProtocol? = nil,
        errorManager: ErrorManager? = nil,
        userManager: UserManager? = nil,
        ttsManager: TTSManager? = nil
    ) {
        self.networkService = networkService ?? NetworkService.shared
        self.errorManager = errorManager ?? ErrorManager.shared
        self.userManager = userManager ?? UserManager.shared
        self.ttsManager = ttsManager ?? TTSManager.shared

        // 先加载本地示例数据
        loadLocalExercises()

        // 然后异步加载API数据
        Task {
            await loadListeningExercisesFromAPI()
        }

        // 监听TTS播放完成通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleTTSFinished),
            name: NSNotification.Name("TTSFinished"),
            object: nil
        )
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    @objc private func handleTTSFinished() {
        let workItem = DispatchWorkItem { [weak self] in
            self?.isPlaying = false
        }
        DispatchQueue.main.async(execute: workItem)
    }

    private func loadLocalExercises() {
        // 仅在开发环境且启用 Mock 数据时加载示例练习
        if AppEnvironment.current.useMockData {
            exercises = ListeningExercise.sampleExercises
            currentExercise = exercises.first
            updateProgress()
        }
    }

    func playAudio() {
        guard let exercise = currentExercise else {
            errorManager.showError(.dataNotFound)
            return
        }

        if isPlaying {
            ttsManager.stopSample()
            isPlaying = false
        } else {
            ttsManager.playSample(text: exercise.transcript, languageCode: currentLanguage) { error in
                if let error = error {
                    self.errorManager.showError(.audioPlaybackFailed(error.localizedDescription))
                    self.isPlaying = false
                } else {
                    self.isPlaying = true
                }
            }
        }
    }

    func stopAudio() {
        ttsManager.stopSample()
        isPlaying = false
    }

    func toggleTranscript() {
        showTranscript.toggle()
    }

    func selectAnswer(_ index: Int) {
        guard !isAnswerSubmitted else { return }
        selectedAnswer = index
        checkAnswer()
    }

    func setLanguage(_ language: String) {
        currentLanguage = language
    }

    func setCurrentExercise(_ exercise: ListeningExercise) {
        // Only update if it's a different exercise to avoid unnecessary resets
        if currentExercise?.id != exercise.id {
            currentExercise = exercise
            currentQuestionIndex = 0
            selectedAnswer = nil
            isAnswerSubmitted = false
            isAnswerCorrect = false
            updateProgress()
        }
    }

    private func checkAnswer() {
        guard let exercise = currentExercise,
              let selectedAnswer = selectedAnswer,
              currentQuestionIndex < exercise.questions.count else { return }

        // 本地检查答案（离线模式）
        let question = exercise.questions[currentQuestionIndex]
        isAnswerCorrect = selectedAnswer == question.correctAnswer
        isAnswerSubmitted = true

        // 通知练习完成
        NotificationCenter.default.post(
            name: .exerciseCompleted,
            object: nil,
            userInfo: [
                "isCorrect": isAnswerCorrect
            ]
        )

        // 异步提交答案到API
        Task {
            await submitAnswerToAPI()
        }
    }

    func nextQuestion() {
        guard let exercise = currentExercise else { return }

        if currentQuestionIndex < exercise.questions.count - 1 {
            currentQuestionIndex += 1
            selectedAnswer = nil
            isAnswerSubmitted = false
            updateProgress()
        }
    }

    func previousQuestion() {
        guard currentExercise != nil else { return }

        if currentQuestionIndex > 0 {
            currentQuestionIndex -= 1
            selectedAnswer = nil
            isAnswerSubmitted = false
            updateProgress()
        }
    }

    private func updateProgress() {
        guard let exercise = currentExercise else { return }
        progress = Double(currentQuestionIndex) / Double(exercise.questions.count)
    }

    // MARK: - 网络加载听力练习
    func loadListeningExercisesFromAPI() async {
        do {
            let apiExercises: [ListeningExercise] = try await networkService.request(.listeningExercises)
            DispatchQueue.main.async {
                self.exercises = apiExercises
                self.currentExercise = apiExercises.first
                self.currentQuestionIndex = 0
                self.selectedAnswer = nil
                self.isAnswerSubmitted = false
                self.isAnswerCorrect = false
                self.showTranscript = false
                self.updateProgress()
            }
        } catch {
            print("Failed to load listening exercises from API: \(error)")
            errorManager.showError(.networkError("加载听力练习失败，请检查网络连接"))
        }
    }

    // 提交听力答案到API
    func submitAnswerToAPI() async {
        guard let exercise = currentExercise,
              let selectedAnswer = selectedAnswer else { return }

        do {
            // 提交答案
            let result: APIResponse = try await networkService.request(
                .submitListeningAnswer(exerciseID: exercise.id, answerIndex: selectedAnswer)
            )

            let workItem = DispatchWorkItem { [weak self] in
                guard let self = self else { return }

                // 更新UI
                if let isCorrect = result.isCorrect {
                    self.isAnswerCorrect = isCorrect
                }

                // 更新用户统计信息
                Task {
                    if let user = self.userManager.currentUser {
                        // 更新听力练习次数
                        self.userManager.updateUserListeningExerciseCount(
                            (user.stats?.listeningExerciseCount ?? 0) + 1
                        )

                        // 更新积分
                        let pointsToAdd = result.points ?? (self.isAnswerCorrect ? 10 : 2)
                        Task {
                            await self.userManager.updateUserPoints(
                                (user.stats?.points ?? 0) + pointsToAdd
                            )
                        }
                    }
                }
            }
            DispatchQueue.main.async(execute: workItem)
        } catch {
            print("Failed to submit listening answer: \(error)")
            errorManager.showError(.networkError("提交答案失败，请重试"))
        }
    }
}