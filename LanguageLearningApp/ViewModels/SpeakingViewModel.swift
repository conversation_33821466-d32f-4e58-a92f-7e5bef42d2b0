import Foundation
import SwiftUI
import Speech
import AVFoundation
import Combine

@MainActor
class SpeakingViewModel: ObservableObject {
    @Published var exercises: [SpeakingExercise] = SpeakingExercise.sampleExercises
    @Published var currentExercise: SpeakingExercise?
    @Published var isRecording = false
    @Published var transcribedText = ""
    @Published var isPlaying = false
    @Published var showFeedback = false
    @Published var feedback = ""
    @Published var progress: Double = 0.0
    @Published var currentLanguage: String = "en-US" // 默认使用英语
    @Published var retryCount = 0
    private let maxRetries = 3

    // 服务和管理器
    private let networkService: NetworkServiceProtocol
    private let errorManager: ErrorManager
    private let userManager: UserManager
    private let storageManager: StorageManagerProtocol

    private let audioEngine = AVAudioEngine()
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?

    init(
        networkService: NetworkServiceProtocol = NetworkService.shared,
        errorManager: ErrorManager = ErrorManager.shared,
        userManager: UserManager = UserManager.shared,
        storageManager: StorageManagerProtocol = StorageManager.shared
    ) {
        self.networkService = networkService
        self.errorManager = errorManager
        self.userManager = userManager
        self.storageManager = storageManager

        // 先加载本地示例数据
        loadLocalExercises()

        // 请求语音识别权限
        requestAuthorization()
        updateSpeechRecognizer()

        // 然后异步加载API数据
        Task {
            await loadSpeakingExercisesFromAPI()
        }
    }

    private func updateSpeechRecognizer() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: currentLanguage))
    }

    func setLanguage(_ language: String) {
        currentLanguage = language
        updateSpeechRecognizer()
    }

    private func loadLocalExercises() {
        // 从本地加载示例练习（作为备选）
        exercises = SpeakingExercise.sampleExercises
        currentExercise = exercises.first
        updateProgress()
    }

    func requestAuthorization() {
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    print("语音识别权限已授权")
                case .denied:
                    self?.errorManager.showError(.microphonePermissionDenied)
                case .restricted:
                    self?.errorManager.showError(.speechRecognitionFailed("语音识别在此设备上不可用"))
                case .notDetermined:
                    self?.errorManager.showError(.speechRecognitionFailed("语音识别尚未授权"))
                @unknown default:
                    self?.errorManager.showError(.unknownError)
                }
            }
        }
    }

    private func resetSpeechRecognition() {
        stopRecording()
        recognitionRequest = nil
        recognitionTask = nil
        updateSpeechRecognizer()
    }

    func startRecording() {
        guard currentExercise != nil else { return }

        do {
            // 配置音频会话
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)

            // 创建识别请求
            recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
            guard let recognitionRequest = recognitionRequest,
                  let speechRecognizer = speechRecognizer else {
                errorManager.showError(.speechRecognitionFailed("无法创建语音识别请求或识别器"))
                return
            }

            // 检查语音识别器是否可用
            guard speechRecognizer.isAvailable else {
                if retryCount < maxRetries {
                    retryCount += 1
                    print("语音识别服务不可用，尝试重新初始化... (尝试 \(retryCount)/\(maxRetries))")
                    resetSpeechRecognition()
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                        self.startRecording()
                    }
                    return
                } else {
                    retryCount = 0
                    errorManager.showError(.speechRecognitionFailed("语音识别服务暂时不可用，请稍后再试"))
                    return
                }
            }

            recognitionRequest.shouldReportPartialResults = true

            // 创建识别任务
            recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
                guard let self = self else { return }

                var isFinal = false

                if let result = result {
                    DispatchQueue.main.async {
                        self.transcribedText = result.bestTranscription.formattedString
                        print("识别文本: \(self.transcribedText)")
                    }
                    isFinal = result.isFinal
                }

                if let error = error {
                    print("语音识别错误: \(error.localizedDescription)")
                    // 处理特定错误
                    let errorMessage: String
                    switch error._code {
                    case 216:
                        if self.retryCount < self.maxRetries {
                            self.retryCount += 1
                            print("语音识别服务错误，尝试重新初始化... (尝试 \(self.retryCount)/\(self.maxRetries))")
                            self.resetSpeechRecognition()
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                                self.startRecording()
                            }
                            return
                        } else {
                            self.retryCount = 0
                            errorMessage = "语音识别服务暂时不可用，请检查网络连接并稍后再试"
                        }
                    case 203:
                        errorMessage = "语音识别服务未授权，请在设置中允许应用使用语音识别"
                    case 201:
                        errorMessage = "无法识别音频，请确保麦克风正常工作"
                    default:
                        errorMessage = "语音识别失败: \(error.localizedDescription)"
                    }
                    self.errorManager.showError(.speechRecognitionFailed(errorMessage))
                }

                if error != nil || isFinal {
                    self.stopRecording()
                }
            }

            // 配置音频输入
            let inputNode = audioEngine.inputNode

            // 获取设备的实际输入格式
            let inputFormat = inputNode.inputFormat(forBus: 0)

            // 验证输入格式
            guard inputFormat.sampleRate > 0 && inputFormat.channelCount > 0 else {
                errorManager.showError(.audioRecordingFailed("设备输入格式无效"))
                return
            }

            // 创建转换器格式
            guard let converterFormat = AVAudioFormat(
                commonFormat: .pcmFormatFloat32,
                sampleRate: inputFormat.sampleRate,
                channels: 1,
                interleaved: false
            ) else {
                errorManager.showError(.audioRecordingFailed("无法创建转换器格式"))
                return
            }

            // 安装音频输入 tap
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: inputFormat) { buffer, _ in
                // 创建转换器
                guard let converter = AVAudioConverter(from: inputFormat, to: converterFormat) else {
                    print("无法创建音频转换器")
                    return
                }

                // 创建输出缓冲区
                guard let outputBuffer = AVAudioPCMBuffer(pcmFormat: converterFormat, frameCapacity: AVAudioFrameCount(buffer.frameLength)) else {
                    print("无法创建输出缓冲区")
                    return
                }

                // 转换音频
                var error: NSError?
                converter.convert(to: outputBuffer, error: &error) { inNumPackets, outStatus in
                    outStatus.pointee = .haveData
                    return buffer
                }

                if let error = error {
                    print("音频转换错误: \(error.localizedDescription)")
                    return
                }

                self.recognitionRequest?.append(outputBuffer)
            }

            // 启动音频引擎
            audioEngine.prepare()
            try audioEngine.start()
            isRecording = true
            print("开始录音")
        } catch {
            errorManager.showError(.audioRecordingFailed("启动录音失败：\(error.localizedDescription)"))
        }
    }

    func stopRecording() {
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        recognitionTask?.cancel()
        recognitionTask = nil
        isRecording = false
        retryCount = 0

        analyzeSpeech()
    }

    private func analyzeSpeech() {
        guard let exercise = currentExercise else { return }

        // 本地分析（离线模式）
        let accuracy = calculateAccuracy(transcribedText, exercise.targetPhrase)
        feedback = "发音准确度: \(Int(accuracy * 100))%"
        showFeedback = true

        // 保存录音并提交到API进行更准确的分析
        Task {
            // 创建临时文件保存录音
            let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let audioFilename = documentsDirectory.appendingPathComponent("recording_\(UUID().uuidString).m4a")

            // 保存录音（这里只是示例，实际应用中需要实现录音保存逻辑）
            // 在实际应用中，你需要在startRecording方法中设置录音保存

            // 提交到API
            await submitSpeakingAnswerToAPI(recordingURL: audioFilename)
        }
    }

    private func calculateAccuracy(_ transcribed: String, _ target: String) -> Double {
        // 简单的字符串相似度计算
        let transcribedWords = transcribed.lowercased().components(separatedBy: .whitespacesAndNewlines)
        let targetWords = target.lowercased().components(separatedBy: .whitespacesAndNewlines)

        var matches = 0
        for word in transcribedWords {
            if targetWords.contains(word) {
                matches += 1
            }
        }

        return Double(matches) / Double(targetWords.count)
    }

    func nextExercise() {
        if let currentIndex = exercises.firstIndex(where: { $0.id == currentExercise?.id }),
           currentIndex < exercises.count - 1 {
            currentExercise = exercises[currentIndex + 1]
            transcribedText = ""
            showFeedback = false
            updateProgress()
        }
    }

    private func updateProgress() {
        guard let currentIndex = exercises.firstIndex(where: { $0.id == currentExercise?.id }) else { return }
        progress = Double(currentIndex + 1) / Double(exercises.count)
    }

    func reset() {
        stopRecording()
        transcribedText = ""
    }

    // MARK: - 网络加载口语练习
    func loadSpeakingExercisesFromAPI() async {
        do {
            let apiExercises: [SpeakingExercise] = try await networkService.request(.speakingExercises)
            DispatchQueue.main.async {
                self.exercises = apiExercises
                self.currentExercise = apiExercises.first
                self.transcribedText = ""
                self.showFeedback = false
                self.updateProgress()
            }
        } catch {
            print("Failed to load speaking exercises from API: \(error)")
            errorManager.showError(.networkError("加载口语练习失败，请检查网络连接"))
        }
    }

    // 提交口语练习答案到API
    func submitSpeakingAnswerToAPI(recordingURL: URL) async {
        guard let exercise = currentExercise else { return }

        do {
            // 上传录音文件
            let result: APIResponse = try await networkService.request(
                .submitSpeakingAnswer(exerciseID: exercise.id, recordingURL: recordingURL)
            )

            DispatchQueue.main.async {
                // 更新UI
                if let accuracy = result.accuracy {
                    self.feedback = "发音准确度: \(Int(accuracy * 100))%"
                    self.showFeedback = true

                    // 更新用户统计信息
                    if let user = self.userManager.currentUser {
                        // 更新口语练习次数
                        self.userManager.updateUserSpeakingExerciseCount((user.stats?.speakingExerciseCount ?? 0) + 1)

                        // 根据准确度给予积分奖励
                        let points = result.points ?? Int(accuracy * 20) // 最高20分
                        self.userManager.updateUserPoints((user.stats?.points ?? 0) + points)
                    }
                }
            }
        } catch {
            print("Failed to submit speaking answer: \(error)")
            errorManager.showError(.networkError("提交口语答案失败，请重试"))
        }
    }

    private func updateUserStats() {
        guard let user = userManager.currentUser else { return }
        let updatedStats = UserStats(
            streakDays: user.stats?.streakDays ?? 0,
            vocabularyCount: user.stats?.vocabularyCount ?? 0,
            listeningExerciseCount: user.stats?.listeningExerciseCount ?? 0,
            speakingExerciseCount: (user.stats?.speakingExerciseCount ?? 0) + 1,
            points: user.stats?.points ?? 0,
            completedChallenges: user.stats?.completedChallenges ?? 0,
            helpedUsers: user.stats?.helpedUsers ?? 0,
            lastLoginDate: user.stats?.lastLoginDate
        )
        
        // Update individual stats using the available methods
        userManager.updateUserSpeakingExerciseCount(updatedStats.speakingExerciseCount)
        userManager.updateUserPoints(updatedStats.points)
    }
}