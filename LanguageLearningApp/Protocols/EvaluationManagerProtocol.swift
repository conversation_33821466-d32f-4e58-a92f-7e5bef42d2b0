import Foundation
import Combine

/// 评估管理器协议，定义评估管理相关功能
public protocol EvaluationManagerProtocol: ObservableObject {
    /// 可用的评估
    var availableEvaluations: [Evaluation] { get }
    
    /// 当前评估
    var currentEvaluation: Evaluation? { get }
    
    /// 评估结果
    var evaluationResults: [EvaluationResult] { get }
    
    /// 当前评估进度
    var currentProgress: Double { get }
    
    /// 是否正在进行评估
    var isEvaluating: Bool { get }
    
    /// 获取可用评估列表
    /// - Returns: 异步操作结果
    func getAvailableEvaluations() async throws -> [Evaluation]
    
    /// 获取评估详情
    /// - Parameter id: 评估ID
    /// - Returns: 异步操作结果
    func getEvaluationDetails(id: UUID) async throws -> Evaluation
    
    /// 开始评估
    /// - Parameter evaluationId: 评估ID
    /// - Returns: 异步操作结果
    func startEvaluation(id: UUID) async throws
    
    /// 提交答案
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 答案
    /// - Returns: 异步操作结果
    func submitAnswer(evaluationId: UUID, questionId: UUID, answer: String) async throws
    
    /// 完成评估
    /// - Parameter evaluationId: 评估ID
    /// - Returns: 异步操作结果
    func completeEvaluation(id: UUID) async throws -> EvaluationResult
    
    /// 获取评估结果
    /// - Parameter resultId: 结果ID
    /// - Returns: 异步操作结果
    func getEvaluationResult(id: UUID) async throws -> EvaluationResult
    
    /// 获取用户评估历史
    /// - Returns: 异步操作结果
    func getUserEvaluationHistory() async throws -> [EvaluationResult]
    
    /// 保存评估结果
    /// - Parameter result: 评估结果
    func saveEvaluationResult(_ result: EvaluationResult)
    
    /// 更新评估进度
    /// - Parameter progress: 进度（0-1）
    func updateProgress(_ progress: Double)
    
    /// 重置当前评估
    func resetCurrentEvaluation()
    
    /// 暂停评估
    func pauseEvaluation()
    
    /// 恢复评估
    func resumeEvaluation()
    
    /// 取消评估
    func cancelEvaluation()
}
