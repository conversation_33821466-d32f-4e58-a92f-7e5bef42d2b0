import Foundation
import Combine

/// 成就管理器协议，定义成就管理相关功能
protocol AchievementManagerProtocol: ObservableObject {
    /// 所有成就
    var achievements: [Achievement] { get }
    
    /// 用户成就
    var userAchievements: [UserAchievement] { get }
    
    /// 进行中的成就
    var inProgressAchievements: [Achievement] { get }
    
    /// 已解锁的成就
    var unlockedAchievements: [Achievement] { get }
    
    /// 保存成就
    func saveAchievements()
    
    /// 保存用户成就
    func saveUserAchievements()
    
    /// 解锁成就
    /// - Parameter achievement: 成就
    func unlockAchievement(_ achievement: Achievement)
    
    /// 领取奖励
    /// - Parameter achievement: 成就
    func claimReward(for achievement: Achievement)
    
    /// 检查成就状态
    func checkAchievementStatus()
    
    /// 从API加载成就
    /// - Parameter completion: 完成回调
    func loadAchievementsFromAPI(completion: (() -> Void)?) async
    
    /// 从API加载用户成就
    /// - Parameter completion: 完成回调
    func loadUserAchievementsFromAPI(completion: (() -> Void)?) async
    
    /// 更新成就状态到API
    /// - Parameter achievement: 成就
    func updateAchievementStatusToAPI(achievement: Achievement) async
}
