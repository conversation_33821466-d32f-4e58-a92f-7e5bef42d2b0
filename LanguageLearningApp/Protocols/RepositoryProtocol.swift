import Foundation
import Combine

/// 通用数据仓库协议
/// - 泛型参数T: 仓库管理的实体类型
/// - 泛型参数ID: 实体标识符类型
public protocol RepositoryProtocol<T, ID> {
    /// 关联的实体类型
    associatedtype T
    
    /// 实体标识符类型
    associatedtype ID: Hashable
    
    /// 获取所有实体
    /// - Returns: 包含所有实体的发布者
    func getAll() -> AnyPublisher<[T], Error>
    
    /// 根据ID获取单个实体
    /// - Parameter id: 实体标识符
    /// - Returns: 包含实体的发布者
    func getById(_ id: ID) -> AnyPublisher<T?, Error>
    
    /// 保存单个实体
    /// - Parameter entity: 要保存的实体
    /// - Returns: 包含保存后实体的发布者
    func save(_ entity: T) -> AnyPublisher<T, Error>
    
    /// 保存多个实体
    /// - Parameter entities: 要保存的实体列表
    /// - Returns: 包含保存后实体列表的发布者
    func saveAll(_ entities: [T]) -> AnyPublisher<[T], Error>
    
    /// 删除单个实体
    /// - Parameter id: 要删除的实体标识符
    /// - Returns: 包含删除成功标志的发布者
    func delete(_ id: ID) -> AnyPublisher<Bool, Error>
    
    /// 异步获取所有实体
    /// - Returns: 实体列表
    func getAllAsync() async throws -> [T]
    
    /// 异步根据ID获取单个实体
    /// - Parameter id: 实体标识符
    /// - Returns: 实体（如存在）
    func getByIdAsync(_ id: ID) async throws -> T?
    
    /// 异步保存单个实体
    /// - Parameter entity: 要保存的实体
    /// - Returns: 保存后的实体
    func saveAsync(_ entity: T) async throws -> T
    
    /// 异步保存多个实体
    /// - Parameter entities: 要保存的实体列表
    /// - Returns: 保存后的实体列表
    func saveAllAsync(_ entities: [T]) async throws -> [T]
    
    /// 异步删除单个实体
    /// - Parameter id: 要删除的实体标识符
    /// - Returns: 删除是否成功
    func deleteAsync(_ id: ID) async throws -> Bool
}

/// 本地数据源协议
/// - 泛型参数T: 数据源管理的实体类型
/// - 泛型参数ID: 实体标识符类型
public protocol LocalDataSourceProtocol<T, ID> {
    /// 关联的实体类型
    associatedtype T
    
    /// 实体标识符类型
    associatedtype ID: Hashable
    
    /// 获取所有实体
    /// - Returns: 所有实体的列表
    func getAll() throws -> [T]
    
    /// 根据ID获取单个实体
    /// - Parameter id: 实体标识符
    /// - Returns: 实体（如存在）
    func getById(_ id: ID) throws -> T?
    
    /// 保存单个实体
    /// - Parameter entity: 要保存的实体
    /// - Returns: 保存后的实体
    func save(_ entity: T) throws -> T
    
    /// 保存多个实体
    /// - Parameter entities: 要保存的实体列表
    /// - Returns: 保存后的实体列表
    func saveAll(_ entities: [T]) throws -> [T]
    
    /// 删除单个实体
    /// - Parameter id: 要删除的实体标识符
    /// - Returns: 删除是否成功
    func delete(_ id: ID) throws -> Bool
}

/// 远程数据源协议
/// - 泛型参数T: 数据源管理的实体类型
/// - 泛型参数ID: 实体标识符类型
public protocol RemoteDataSourceProtocol<T, ID> {
    /// 关联的实体类型
    associatedtype T
    
    /// 实体标识符类型
    associatedtype ID: Hashable
    
    /// 获取所有实体
    /// - Returns: 包含所有实体的发布者
    func getAll() -> AnyPublisher<[T], Error>
    
    /// 根据ID获取单个实体
    /// - Parameter id: 实体标识符
    /// - Returns: 包含实体的发布者
    func getById(_ id: ID) -> AnyPublisher<T, Error>
    
    /// 保存单个实体
    /// - Parameter entity: 要保存的实体
    /// - Returns: 包含保存后实体的发布者
    func save(_ entity: T) -> AnyPublisher<T, Error>
    
    /// 保存多个实体
    /// - Parameter entities: 要保存的实体列表
    /// - Returns: 包含保存后实体列表的发布者
    func saveAll(_ entities: [T]) -> AnyPublisher<[T], Error>
    
    /// 删除单个实体
    /// - Parameter id: 要删除的实体标识符
    /// - Returns: 包含删除成功标志的发布者
    func delete(_ id: ID) -> AnyPublisher<Bool, Error>
    
    /// 异步获取所有实体
    /// - Returns: 实体列表
    func getAllAsync() async throws -> [T]
    
    /// 异步根据ID获取单个实体
    /// - Parameter id: 实体标识符
    /// - Returns: 实体
    func getByIdAsync(_ id: ID) async throws -> T
    
    /// 异步保存单个实体
    /// - Parameter entity: 要保存的实体
    /// - Returns: 保存后的实体
    func saveAsync(_ entity: T) async throws -> T
    
    /// 异步保存多个实体
    /// - Parameter entities: 要保存的实体列表
    /// - Returns: 保存后的实体列表
    func saveAllAsync(_ entities: [T]) async throws -> [T]
    
    /// 异步删除单个实体
    /// - Parameter id: 要删除的实体标识符
    /// - Returns: 删除是否成功
    func deleteAsync(_ id: ID) async throws -> Bool
} 