import Foundation
import Combine

/// 本地化管理器协议，定义多语言支持相关功能
public protocol LocalizationManagerProtocol: ObservableObject {
    /// 当前语言代码
    var currentLanguage: String { get set }

    /// 可用的语言列表
    var availableLanguages: [String] { get }

    /// 语言显示名称映射
    var languageDisplayNames: [String: String] { get }

    /// 设置当前语言
    /// - Parameter languageCode: 语言代码
    func setLanguage(_ languageCode: String)

    /// 获取本地化字符串
    /// - Parameter key: 本地化键
    /// - Returns: 本地化后的字符串
    func localizedString(_ key: String) -> String

    /// 获取本地化字符串（带参数）
    /// - Parameters:
    ///   - key: 本地化键
    ///   - arguments: 格式化参数
    /// - Returns: 本地化后的字符串
    func localizedString(_ key: String, arguments: CVarArg...) -> String

    /// 获取语言显示名称
    /// - Parameter languageCode: 语言代码
    /// - Returns: 语言显示名称
    func getLanguageDisplayName(for languageCode: String) -> String

    /// 检查是否支持指定语言
    /// - Parameter languageCode: 语言代码
    /// - Returns: 是否支持
    func isLanguageSupported(_ languageCode: String) -> Bool

    /// 获取系统首选语言
    /// - Returns: 系统首选语言代码
    func getSystemPreferredLanguage() -> String

    /// 保存语言设置
    func saveLanguageSettings()

    /// 加载语言设置
    func loadLanguageSettings()

    /// 重置为系统语言
    func resetToSystemLanguage()
}
