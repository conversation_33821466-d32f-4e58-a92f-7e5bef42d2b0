import Foundation
import SwiftUI

/// 主题管理器协议，定义主题管理相关功能
public protocol ThemeManagerProtocol: ObservableObject {
    /// 当前主题名称
    var currentThemeName: String { get set }

    /// 是否使用暗黑模式
    var isDarkMode: Bool { get set }

    /// 是否跟随系统主题
    var followSystemTheme: Bool { get set }

    /// 可用的主题名称列表
    var availableThemeNames: [String] { get }

    /// 设置主题
    /// - Parameter themeName: 要设置的主题名称
    func setTheme(_ themeName: String)

    /// 切换暗黑模式
    func toggleDarkMode()

    /// 设置是否跟随系统主题
    /// - Parameter follow: 是否跟随系统主题
    func setFollowSystemTheme(_ follow: Bool)

    /// 获取当前颜色方案
    /// - Returns: 当前颜色方案
    func getCurrentColorScheme() -> ColorScheme?

    /// 保存主题设置
    func saveThemeSettings()

    /// 加载主题设置
    func loadThemeSettings()

    /// 重置为默认主题
    func resetToDefaultTheme()
}
