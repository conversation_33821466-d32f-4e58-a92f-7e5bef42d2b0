import Foundation

/// 存储管理器协议，定义数据存储相关功能
public protocol StorageManagerProtocol {
    // MARK: - 用户相关

    /// 保存当前用户
    /// - Parameter user: 用户对象
    func saveCurrentUser(_ user: User)

    /// 加载当前用户
    /// - Returns: 用户对象（如果存在）
    func loadCurrentUser() -> User?

    /// 保存登录状态
    /// - Parameter isLoggedIn: 是否已登录
    func saveLoginState(_ isLoggedIn: Bool)

    /// 加载登录状态
    /// - Returns: 是否已登录
    func loadLoginState() -> Bool

    /// 获取上次登录日期
    /// - Returns: 上次登录日期（如果存在）
    func getLastLoginDate() -> Date?

    // MARK: - 课程相关

    /// 保存课程
    /// - Parameter lessons: 课程数组
    func saveLessons(_ lessons: [Lesson])

    /// 加载课程
    /// - Returns: 课程数组
    func loadLessons() -> [Lesson]

    /// 保存课程进度
    /// - Parameter progress: 课程进度字典 (Key: LessonID as String)
    func saveLessonProgress(_ progress: [String: LessonProgress])

    /// 加载课程进度
    /// - Returns: 课程进度字典 (Key: LessonID as String)
    func loadLessonProgress() -> [String: LessonProgress]

    /// 保存收藏课程
    /// - Parameter favorites: 收藏课程ID数组
    func saveFavoriteLessons(_ favorites: [String])

    /// 加载收藏课程
    /// - Returns: 收藏课程ID数组
    func loadFavoriteLessons() -> [String]

    // MARK: - 成就相关

    /// 保存成就
    /// - Parameter achievements: 成就数组
    func saveAchievements(_ achievements: [Achievement])

    /// 加载成就
    /// - Returns: 成就数组
    func loadAchievements() -> [Achievement]?

    /// 保存用户成就
    /// - Parameter userAchievements: 用户成就数组
    func saveUserAchievements(_ userAchievements: [UserAchievement])

    /// 加载用户成就
    /// - Returns: 用户成就数组
    func loadUserAchievements() -> [UserAchievement]?

    // MARK: - 练习相关

    /// 保存练习会话
    /// - Parameter sessions: 练习会话数组
    func savePracticeSessions(_ sessions: [PracticeSession])

    /// 加载练习会话
    /// - Returns: 练习会话数组
    func loadPracticeSessions() -> [PracticeSession]

    // MARK: - 设置相关

    /// 保存应用设置
    /// - Parameter settings: 应用设置
    func saveAppSettings(_ settings: UserSettings)

    /// 加载应用设置
    /// - Returns: 应用设置
    func loadAppSettings() -> UserSettings?

    /// 保存TTS设置
    /// - Parameter settings: TTS设置
    func saveTTSSettings(_ settings: TTSSettings)

    /// 加载TTS设置
    /// - Returns: TTS设置
    func loadTTSSettings() -> TTSSettings?

    // MARK: - 通用方法

    /// 清除所有数据
    func clearAllData()

    /// 清除特定类型的数据
    /// - Parameter type: 数据类型
    func clearData(ofType type: StorageDataType)
}
