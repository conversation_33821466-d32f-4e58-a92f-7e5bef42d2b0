import Foundation
import Speech
import AVFoundation

/// 语音识别管理器协议，定义语音识别相关功能
@MainActor
public protocol SpeechRecognitionManagerProtocol: ObservableObject {
    /// 是否正在录音
    var isRecording: Bool { get }
    
    /// 是否可用
    var isAvailable: Bool { get }
    
    /// 识别的文本
    var recognizedText: String { get }
    
    /// 音频功率级别
    var audioLevel: Float { get }
    
    /// 录音时长
    var recordingDuration: TimeInterval { get }
    
    /// 语音识别权限状态
    var authorizationStatus: SFSpeechRecognizerAuthorizationStatus { get }
    
    /// 开始录音和语音识别
    /// - Parameters:
    ///   - locale: 语言区域设置
    ///   - completion: 完成回调
    func startRecording(locale: Locale?, completion: @escaping (Result<String, Error>) -> Void)
    
    /// 停止录音和语音识别
    func stopRecording()
    
    /// 取消录音和语音识别
    func cancelRecording()
    
    /// 请求语音识别权限
    /// - Returns: 异步操作结果
    func requestSpeechRecognitionPermission() async -> Bool
    
    /// 请求麦克风权限
    /// - Returns: 异步操作结果
    func requestMicrophonePermission() async -> Bool
    
    /// 检查权限状态
    /// - Returns: 是否有必要的权限
    func checkPermissions() -> Bool
    
    /// 设置音频会话
    /// - Returns: 异步操作结果
    func setupAudioSession() async throws
    
    /// 获取支持的语言
    /// - Returns: 支持的语言区域设置数组
    func getSupportedLocales() -> [Locale]
    
    /// 检查语言是否支持
    /// - Parameter locale: 语言区域设置
    /// - Returns: 是否支持
    func isLocaleSupported(_ locale: Locale) -> Bool
    
    /// 重置识别状态
    func reset()
}
