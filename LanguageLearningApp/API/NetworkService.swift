import Foundation
import SwiftUI
import Network

/// 网络服务实现，处理底层网络请求
public class NetworkService: NetworkServiceProtocol {
    public static let shared = NetworkService()

    // 自定義 URLSession，用於處理自簽名證書
    private let session: URLSession

    // 网络监视器
    private let networkMonitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "NetworkMonitor")

    // 网络状态
    private var isConnected = true

    public init(session: URLSession? = nil) {
        // 如果提供了自定义会话，使用它；否则创建新的
        if let session = session {
            self.session = session
        } else {
            // 創建自定義 URLSession 配置
            let sessionDelegate = CustomURLSessionDelegate()
            self.session = URLSession(configuration: .default, delegate: sessionDelegate, delegateQueue: nil)
        }

        // 輸出當前環境信息，便於調試
        logInfo("NetworkService 初始化，当前环境: \(AppEnvironment.current.rawValue)")
        logInfo("API 基础 URL: \(AppEnvironment.current.baseURL.absoluteString)")

        // 檢查 ATS 設置
        checkATSSettings()

        // 设置网络监视器
        setupNetworkMonitor()
    }

    /// 设置网络监视器
    private func setupNetworkMonitor() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            self?.isConnected = path.status == .satisfied
            if path.status == .satisfied {
                logInfo("网络连接已恢复")
            } else {
                logWarning("网络连接已断开")
            }
        }
        networkMonitor.start(queue: monitorQueue)
        logInfo("网络监视器已启动")
    }

    /// 检查网络连接状态
    /// - Returns: 是否有网络连接
    public func isNetworkAvailable() -> Bool {
        return isConnected
    }

    /// 檢查 App Transport Security 設置
    private func checkATSSettings() {
        guard let infoPlistPath = Bundle.main.path(forResource: "Info", ofType: "plist"),
              let infoPlist = NSDictionary(contentsOfFile: infoPlistPath) as? [String: Any] else {
            logWarning("无法读取 Info.plist")
            return
        }

        logDebug("检查 App Transport Security 设置:")

        if let ats = infoPlist["NSAppTransportSecurity"] as? [String: Any] {
            if let allowsArbitraryLoads = ats["NSAllowsArbitraryLoads"] as? Bool {
                logDebug("- NSAllowsArbitraryLoads: \(allowsArbitraryLoads)")
            } else {
                logDebug("- NSAllowsArbitraryLoads 未设置")
            }

            if let exceptionDomains = ats["NSExceptionDomains"] as? [String: Any] {
                logDebug("- NSExceptionDomains:")
                for (domain, settings) in exceptionDomains {
                    logDebug("  - \(domain):")
                    if let domainSettings = settings as? [String: Any] {
                        for (key, value) in domainSettings {
                            logDebug("    - \(key): \(value)")
                        }
                    }
                }
            } else {
                logDebug("- NSExceptionDomains 未设置")
            }
        } else {
            logDebug("- NSAppTransportSecurity 未设置，使用默认安全设置")
        }
    }

    // 标准请求方法，返回解码后的对象
    public func request<T: Decodable>(_ endpoint: APIEndpoint) async throws -> T {
        logInfo("🌐 请求: \(endpoint.method) \(endpoint.url.absoluteString)")

        let data = try await performRequest(endpoint)

        // 打印 JSON 响应以便调试
        if let jsonString = String(data: data, encoding: .utf8) {
            logDebug("🌐 API 响应 JSON: \(jsonString)")
        }

        // 尝试解析为字典以便查看字段
        if let jsonDict = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
            logDebug("🌐 API 响应字段: \(jsonDict.keys)")

            // 打印完整的响应结构
            do {
                let prettyJsonData = try JSONSerialization.data(withJSONObject: jsonDict, options: .prettyPrinted)
                if let prettyPrintedString = String(data: prettyJsonData, encoding: .utf8) {
                    logVerbose("🌐 API 响应详细内容:\n\(prettyPrintedString)")
                }
            } catch {
                logWarning("无法格式化JSON: \(error)")
            }
        }

        // 特殊处理课程列表响应
        if String(describing: T.self).contains("LessonListResponse") {
            logInfo("检测到请求课程列表，使用特殊处理")
            let decoder = JSONDecoder()

            // 使用自定义日期解码策略
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSZ"
            decoder.dateDecodingStrategy = .formatted(dateFormatter)

            do {
                // 直接尝试解析为课程列表响应
                return try decoder.decode(T.self, from: data)
            } catch {
                logWarning("特殊处理课程列表失败: \(error)")
            }
        }

        let decoder = JSONDecoder()

        // 使用自定义日期解码策略，支持多种日期格式
        let dateFormatter = DateFormatter()
        var dateFormats = [
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSZ",  // 带纳秒的格式
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ",   // 带微秒的格式
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSZ",    // 带毫秒的格式
            "yyyy-MM-dd'T'HH:mm:ss.SSSSZ",     // 带百微秒的格式
            "yyyy-MM-dd'T'HH:mm:ss.SSSZ",      // 带毫秒的格式
            "yyyy-MM-dd'T'HH:mm:ss.SSZ",       // 带厘秒的格式
            "yyyy-MM-dd'T'HH:mm:ssZ",          // 标准ISO8601格式
            "yyyy-MM-dd'T'HH:mm:ss",           // 无时区信息的格式
            "yyyy-MM-dd"                       // 仅日期格式
        ]
        decoder.dateDecodingStrategy = .custom { decoder in
            let container = try decoder.singleValueContainer()
            let string = try container.decode(String.self)

            // 尝试使用多种日期格式解析
            for format in dateFormats {
                dateFormatter.dateFormat = format
                if let date = dateFormatter.date(from: string) {
                    return date
                }
            }

            // 如果所有格式都失败，抛出错误
            throw DecodingError.dataCorruptedError(
                in: container,
                debugDescription: "Cannot decode date string \(string)"
            )
        }

        do {
            // 首先尝试解析为包装的响应
            let wrapper = try decoder.decode(APIResponseWrapper<T>.self, from: data)

            // 检查响应是否成功
            if !wrapper.success {
                let errorMessage = wrapper.error ?? "Unknown API error"
                logError("🌐 API 请求成功但业务失败: \(errorMessage)")
                throw AppError.apiError(errorMessage)
            }

            // 确保数据存在
            guard let responseData = wrapper.data else {
                logWarning("🌐 API 响应中无数据")
                throw AppError.dataNotFound
            }

            logInfo("🌐 API 请求成功完成: \(endpoint.method) \(endpoint.url.absoluteString)")
            return responseData
        } catch let wrapperError as DecodingError {
            logWarning("尝试解析为包装响应失败，尝试直接解析为目标类型")
            logDebug("包装响应解析错误: \(wrapperError)")

            // 如果解析包装响应失败，尝试从JSON中提取data字段，然后解析
            do {
                // 先尝试解析为字典
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    // 检查是否有data字段
                    if let dataField = json["data"] as? [String: Any] {
                        // 如果data是一个对象，尝试解析data字段
                        let dataData = try JSONSerialization.data(withJSONObject: dataField, options: [])
                        return try decoder.decode(T.self, from: dataData)
                    } else if let dataArray = json["data"] as? [[String: Any]] {
                        // 如果data是一个数组，尝试特殊处理
                        logInfo("检测到data字段是数组，尝试特殊处理")

                        // 对于课程列表响应类型，直接解析整个响应
                        if String(describing: T.self).contains("LessonListResponse") {
                            return try decoder.decode(T.self, from: data)
                        }

                        // 对于其他类型，尝试将数组转换为JSON并解析
                        let dataData = try JSONSerialization.data(withJSONObject: dataArray, options: [])
                        return try decoder.decode(T.self, from: dataData)
                    } else {
                        // 如果没有data字段，尝试直接解析整个JSON
                        logInfo("响应中没有data字段，尝试直接解析整个JSON")
                        return try decoder.decode(T.self, from: data)
                    }
                } else {
                    // 如果不是字典，尝试直接解析
                    logInfo("响应不是JSON对象，尝试直接解析")
                    return try decoder.decode(T.self, from: data)
                }
            } catch let decodingError as DecodingError {
                logError("🌐 JSON 解码错误: \(decodingError)")

                // 如果是键不存在错误，打印更多信息
                if case .keyNotFound(let key, _) = decodingError {
                    logError("缺少键: \(key.stringValue)")
                    logError("期望的模型类型: \(T.self)")
                }

                // 如果是类型不匹配错误，打印更多信息
                if case .typeMismatch(let type, let context) = decodingError {
                    logError("类型不匹配: 期望 \(type)，上下文: \(context)")

                    // 特殊处理课程列表响应
                    if String(describing: T.self).contains("LessonListResponse") {
                        logInfo("尝试特殊处理课程列表响应")
                        // 对于课程列表响应，继续抛出错误而不是创建空响应
                    }
                }

                throw AppError.decodingError(decodingError)
            }
        }
    }

    /// 发送请求并返回原始数据
    /// - Parameter endpoint: API端点
    /// - Returns: 响应数据
    public func requestRawData(_ endpoint: APIEndpoint) async throws -> Data {
        logInfo("🌐 请求原始数据: \(endpoint.method) \(endpoint.url.absoluteString)")

        let data = try await performRequest(endpoint)

        // 打印 JSON 响应以便调试
        if let jsonString = String(data: data, encoding: .utf8) {
            logDebug("🌐 API 响应 JSON: \(jsonString)")
        }

        return data
    }

    /// 执行请求
    /// - Parameter endpoint: API端点
    /// - Returns: 响应数据
    private func performRequest(_ endpoint: APIEndpoint) async throws -> Data {
        guard isNetworkAvailable() else {
            throw AppError.noInternetConnection
        }

        let request = try createRequest(for: endpoint)
        logRequest(request)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw AppError.invalidResponse
        }

        logResponse(httpResponse, data: data)

        switch httpResponse.statusCode {
        case 200...299:
            return data
        case 400:
            throw AppError.badRequest("Bad request")
        case 401:
            throw AppError.unauthorized
        case 403:
            throw AppError.forbidden
        case 404:
            throw AppError.notFound
        case 409:
            throw AppError.conflict
        case 429:
            throw AppError.tooManyRequests
        case 500:
            throw AppError.internalServerError
        case 502:
            throw AppError.badGateway
        case 503:
            throw AppError.serviceUnavailable
        case 504:
            throw AppError.gatewayTimeout
        default:
            throw AppError.unknown
        }
    }

    // 上传文件的方法
    func uploadFile(to endpoint: APIEndpoint, fileURL: URL, mimeType: String) async throws -> Data {
        let boundary = UUID().uuidString
        logInfo("🌐 开始上传文件: \(fileURL.lastPathComponent) 到 \(endpoint.url.absoluteString)")

        var request = URLRequest(url: endpoint.url)
        request.httpMethod = "POST"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")

        // 添加认证头
        for (key, value) in endpoint.headers where key != "Content-Type" {
            request.setValue(value, forHTTPHeaderField: key)
        }

        // 准备文件数据
        let fileData = try Data(contentsOf: fileURL)
        let fileName = fileURL.lastPathComponent
        logInfo("🌐 准备上传文件: \(fileName), 大小: \(fileData.count) 字节, 类型: \(mimeType)")

        // 创建multipart表单数据
        var body = Data()

        // 添加文件
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: \(mimeType)\r\n\r\n".data(using: .utf8)!)
        body.append(fileData)
        body.append("\r\n".data(using: .utf8)!)

        // 结束边界
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)

        request.httpBody = body

        do {
            logDebug("🌐 开始文件上传请求")
            // 使用自定義 URLSession 而不是 URLSession.shared
            let (data, response) = try await session.data(for: request)
            logInfo("🌐 文件上传请求已完成")

            // 打印上传响应体
            if let responseString = String(data: data, encoding: .utf8) {
                logDebug("🌐 文件上传 API 响应体: \(responseString)")
            } else {
                logDebug("🌐 文件上传 API 响应体: 无法解码为字符串")
                // 尝试打印十六进制
                logVerbose("🌐 文件上传 API 响应体 (十六进制): \(data.map { String(format: "%02x", $0) }.joined())")
            }

            guard let httpResponse = response as? HTTPURLResponse, (200..<300).contains(httpResponse.statusCode) else {
                let statusCode = (response as? HTTPURLResponse)?.statusCode ?? -1
                logError("🌐 文件上传失败，状态码: \(statusCode)")
                throw AppError.networkError("Upload failed with status: \(statusCode)")
            }

            logInfo("🌐 文件 \(fileName) 上传成功")
            return data
        } catch {
            logError("🌐 文件上传失败: \(error.localizedDescription)")
            throw AppError.networkError("Upload failed: \(error.localizedDescription)")
        }
    }

    /// 发送请求但不期望响应数据
    /// - Parameter endpoint: API端点
    public func requestNoResponse(_ endpoint: APIEndpoint) async throws {
        _ = try await requestRawData(endpoint)
    }

    /// 创建URLRequest
    /// - Parameter endpoint: API端点
    /// - Returns: URLRequest对象
    private func createRequest(for endpoint: APIEndpoint) throws -> URLRequest {
        var request = URLRequest(url: endpoint.url)
        request.httpMethod = endpoint.method

        // 设置请求头
        for (key, value) in endpoint.headers {
            request.setValue(value, forHTTPHeaderField: key)
        }

        // 设置请求体
        if let bodyData = endpoint.body() {
            request.httpBody = bodyData
        }

        return request
    }

    /// 记录请求信息
    /// - Parameter request: URLRequest对象
    private func logRequest(_ request: URLRequest) {
        logInfo("🌐 发送请求: \(request.httpMethod ?? "UNKNOWN") \(request.url?.absoluteString ?? "NO URL")")

        // 记录请求头
        if let headers = request.allHTTPHeaderFields {
            logDebug("🌐 请求头:")
            for (key, value) in headers {
                logDebug("  \(key): \(value)")
            }
        }

        // 记录请求体
        if let body = request.httpBody,
           let bodyString = String(data: body, encoding: .utf8) {
            logDebug("🌐 请求体: \(bodyString)")
        }
    }

    /// 记录响应信息
    /// - Parameters:
    ///   - response: HTTPURLResponse对象
    ///   - data: 响应数据
    private func logResponse(_ response: HTTPURLResponse, data: Data) {
        logInfo("🌐 收到响应: \(response.statusCode) \(response.url?.absoluteString ?? "NO URL")")

        // 记录响应头
        logDebug("🌐 响应头:")
        for (key, value) in response.allHeaderFields {
            logDebug("  \(key): \(value)")
        }

        // 记录响应体
        if let bodyString = String(data: data, encoding: .utf8) {
            logDebug("🌐 响应体: \(bodyString)")
        }
    }
}

// 扩展Data类型，方便构建multipart表单数据
extension Data {
    mutating func append(_ string: String) {
        if let data = string.data(using: .utf8) {
            append(data)
        }
    }
}