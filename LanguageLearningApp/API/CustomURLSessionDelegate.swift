import Foundation

/// 自定義 URLSessionDelegate，用於處理開發環境中的自簽名證書
class CustomURLSessionDelegate: NSObject, URLSessionDelegate {

    /// 開發環境中允許自簽名證書的主機列表
    private let allowedDevelopmentHosts = [
        "api.backend.orb.local",
        "api.languagelearningapp-backend.orb.local",
        "dev-api.languagelearningapp.com",
        "localhost",
        "127.0.0.1"
    ]

    /// 檢查主機是否在允許的開發環境主機列表中
    private func isAllowedDevelopmentHost(_ host: String) -> Bool {
        return allowedDevelopmentHosts.contains { host.contains($0) }
    }

    /// 打印證書信息，用於調試
    private func printCertificateInfo(_ serverTrust: SecTrust) {
        // 獲取證書數量
        let certificateCount = SecTrustGetCertificateCount(serverTrust)
        print("證書鏈中的證書數量: \(certificateCount)")

        // 遍歷所有證書
        for i in 0..<certificateCount {
            guard let certificate = SecTrustGetCertificateAtIndex(serverTrust, i) else {
                print("無法獲取索引 \(i) 處的證書")
                continue
            }

            // 獲取證書主題
            if let subject = SecCertificateCopySubjectSummary(certificate) {
                print("證書 \(i) 主題: \(subject)")
            }

            // 獲取證書數據
            if let certificateData = SecCertificateCopyData(certificate) as Data? {
                print("證書 \(i) 數據長度: \(certificateData.count) 字節")
            }
        }
    }

    /// 處理 SSL 證書驗證挑戰
    func urlSession(_ session: URLSession, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
        let host = challenge.protectionSpace.host
        let authMethod = challenge.protectionSpace.authenticationMethod

        print("收到證書驗證挑戰:")
        print("- 主機: \(host)")
        print("- 驗證方法: \(authMethod)")
        print("- 當前環境: \(AppEnvironment.current.rawValue)")

        // 只在開發環境中接受自簽名證書
        if AppEnvironment.current == .development {
            // 檢查主機名是否為允許的開發環境主機
            if isAllowedDevelopmentHost(host) {
                print("開發環境: 接受來自 \(host) 的自簽名證書")

                // 確保服務器信任對象存在
                guard let serverTrust = challenge.protectionSpace.serverTrust else {
                    print("錯誤: 無法獲取服務器信任對象")
                    completionHandler(.cancelAuthenticationChallenge, nil)
                    return
                }

                // 打印證書詳細信息
                printCertificateInfo(serverTrust)

                // 檢查證書信任狀態
                var secResult = SecTrustResultType.invalid
                let status = SecTrustEvaluate(serverTrust, &secResult)

                print("證書評估結果: \(status == errSecSuccess ? "成功" : "失敗") (狀態碼: \(status))")
                print("證書信任結果: \(secResult == .proceed ? "可信任" : "不可信任") (結果碼: \(secResult.rawValue))")

                // 創建證書憑證並接受
                let credential = URLCredential(trust: serverTrust)
                print("已創建證書憑證，接受連接")
                completionHandler(.useCredential, credential)
                return
            }
        }

        // 對於其他環境或主機，使用默認處理
        print("使用默認證書驗證處理方式")
        completionHandler(.performDefaultHandling, nil)
    }
}
