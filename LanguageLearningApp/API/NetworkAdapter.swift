import Foundation
import Combine

/// 网络适配器
public class NetworkAdapter: NetworkProtocol {
    public static let shared = NetworkAdapter()
    
    private let networkService: NetworkServiceProtocol
    private var currentTask: URLSessionDataTask?
    
    /// 初始化方法
    /// - Parameter networkService: 原有的网络服务实现
    public init(networkService: NetworkServiceProtocol = NetworkService.shared) {
        self.networkService = networkService
    }
    
    /// 检查网络连接状态
    /// - Returns: 是否有网络连接
    public func isNetworkAvailable() -> Bool {
        return networkService.isNetworkAvailable()
    }
    
    /// 发送请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - method: 请求方法
    ///   - headers: 请求头
    ///   - body: 请求体
    /// - Returns: 响应数据
    public func request(url: URL, method: String, headers: [String: String], body: Data?) async throws -> Data {
        // 创建API端点
        let endpoint = APIEndpoint.custom(url: url, method: method, headers: headers, bodyData: body)
        
        // 发送请求
        return try await networkService.requestRawData(endpoint)
    }
    
    /// 取消所有请求
    public func cancelAllRequests() {
        currentTask?.cancel()
        currentTask = nil
    }
    
    // MARK: - Private Methods
    
    /// 获取默认请求头
    private func getDefaultHeaders() -> [String: String] {
        var headers = [
            "Content-Type": "application/json",
            "Accept": "application/json"
        ]
        
        // 如果有token，添加到请求头
        if let token = UserDefaults.standard.string(forKey: "authToken") {
            headers["Authorization"] = "Bearer \(token)"
        }
        
        return headers
    }
} 