import Foundation
import Combine
import Network

/// API客户端，提供基于Combine的API请求接口
/// 这是一个包装NetworkService的类，用于提供基于Combine的API接口
public class APIClient: APIClientProtocol {
    public static let shared = APIClient()

    // 网络服务
    private let networkService: NetworkServiceProtocol
    private var currentTask: Task<Void, Error>?

    /// 是否有网络连接
    public var isNetworkAvailable: Bool {
        return networkService.isNetworkAvailable()
    }

    public init(networkService: NetworkServiceProtocol = NetworkService.shared) {
        self.networkService = networkService
    }

    /// 发送请求
    /// - Parameter endpoint: API端点
    /// - Returns: 包含响应数据的发布者
    public func request(endpoint: APIEndpointProtocol) -> AnyPublisher<Data, Error> {
        return Future<Data, Error> { promise in
            Task {
                do {
                    let data = try await self.networkService.requestRawData(endpoint as! APIEndpoint)
                    promise(.success(data))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    /// 发送请求并解码响应为指定类型
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - type: 解码类型
    /// - Returns: 包含解码对象的发布者
    public func request<T: Decodable>(endpoint: APIEndpointProtocol, as type: T.Type) -> AnyPublisher<T, Error> {
        return Future<T, Error> { promise in
            Task {
                do {
                    let result: T = try await self.networkService.request(endpoint as! APIEndpoint)
                    promise(.success(result))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    /// 取消所有请求
    public func cancelAllRequests() {
        currentTask?.cancel()
        currentTask = nil
    }

    /// 发送请求并返回Publisher（自定义URL）
    /// - Parameters:
    ///   - url: 请求URL
    ///   - method: HTTP方法
    ///   - headers: HTTP头
    ///   - body: 请求体
    /// - Returns: 包含响应数据的Publisher
    public func request(url: URL, method: String, headers: [String: String], body: Data?) -> AnyPublisher<Data, Error> {
        return Future<Data, Error> { promise in
            Task {
                do {
                    // 创建自定义APIEndpoint
                    let customEndpoint = APIEndpoint.custom(url: url, method: method, headers: headers, bodyData: body)

                    // 使用NetworkService发送请求
                    let data = try await self.networkService.requestRawData(customEndpoint)
                    promise(.success(data))
                } catch {
                    promise(.failure(AppError.from(error)))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    /// 发送GET请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - headers: 请求头
    /// - Returns: 响应数据
    public func get(_ url: URL, headers: [String: String]) async throws -> Data {
        let endpoint = APIEndpoint.custom(
            url: url,
            method: "GET",
            headers: headers,
            bodyData: nil
        )
        return try await networkService.requestRawData(endpoint)
    }

    /// 发送POST请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - headers: 请求头
    ///   - body: 请求体
    /// - Returns: 响应数据
    public func post(_ url: URL, headers: [String: String], body: Data? = nil) async throws -> Data {
        let endpoint = APIEndpoint.custom(
            url: url,
            method: "POST",
            headers: headers,
            bodyData: body
        )
        return try await networkService.requestRawData(endpoint)
    }

    /// 发送DELETE请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - headers: 请求头
    ///   - body: 请求体
    /// - Returns: 响应数据
    public func delete(_ url: URL, headers: [String: String], body: Data? = nil) async throws -> Data {
        let endpoint = APIEndpoint.custom(
            url: url,
            method: "DELETE",
            headers: headers,
            bodyData: body
        )
        return try await networkService.requestRawData(endpoint)
    }

    // MARK: - Async/Await API

    /// 异步发送GET请求
    /// - Parameter endpoint: API端点路径（相对于baseURL）
    /// - Returns: 响应数据
    func get(_ endpoint: String) async throws -> Any {
        // 构建完整URL
        let baseURLString = AppEnvironment.current.baseURL.absoluteString
        let fullURLString = baseURLString + (endpoint.hasPrefix("/") ? endpoint : "/" + endpoint)

        guard let url = URL(string: fullURLString) else {
            throw AppError.invalidResponse
        }

        // 创建自定义APIEndpoint
        let apiEndpoint = APIEndpoint.custom(
            url: url,
            method: "GET",
            headers: [:],
            bodyData: nil
        )

        // 使用NetworkService发送请求
        let data = try await networkService.requestRawData(apiEndpoint)

        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                return json
            } else if let jsonArray = try JSONSerialization.jsonObject(with: data) as? [Any] {
                return jsonArray
            } else {
                throw AppError.invalidResponse
            }
        } catch {
            throw AppError.decodingError(error)
        }
    }

    /// 异步发送POST请求
    /// - Parameters:
    ///   - endpoint: API端点路径（相对于baseURL）
    ///   - parameters: 请求参数
    /// - Returns: 响应数据
    func post(_ endpoint: String, parameters: [String: Any]?) async throws -> Any {
        // 构建完整URL
        let baseURLString = AppEnvironment.current.baseURL.absoluteString
        let fullURLString = baseURLString + (endpoint.hasPrefix("/") ? endpoint : "/" + endpoint)

        guard let url = URL(string: fullURLString) else {
            throw AppError.invalidResponse
        }

        // 准备请求体
        var body: Data? = nil
        if let parameters = parameters {
            do {
                body = try JSONSerialization.data(withJSONObject: parameters)
            } catch {
                throw AppError.badRequest("Invalid parameters")
            }
        }

        // 创建自定义APIEndpoint
        let apiEndpoint = APIEndpoint.custom(
            url: url,
            method: "POST",
            headers: [:],
            bodyData: body
        )

        // 使用NetworkService发送请求
        let data = try await networkService.requestRawData(apiEndpoint)

        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                return json
            } else if let jsonArray = try JSONSerialization.jsonObject(with: data) as? [Any] {
                return jsonArray
            } else {
                throw AppError.invalidResponse
            }
        } catch {
            throw AppError.decodingError(error)
        }
    }

    /// 异步发送GET请求并返回解码后的对象
    /// - Parameters:
    ///   - endpoint: API端点路径（相对于baseURL）
    ///   - type: 解码类型
    /// - Returns: 解码后的对象
    func get<T: Decodable>(_ endpoint: String, as type: T.Type) async throws -> T {
        // 构建完整URL
        let baseURLString = AppEnvironment.current.baseURL.absoluteString
        let fullURLString = baseURLString + (endpoint.hasPrefix("/") ? endpoint : "/" + endpoint)

        guard let url = URL(string: fullURLString) else {
            throw AppError.invalidResponse
        }

        // 创建自定义APIEndpoint
        let apiEndpoint = APIEndpoint.custom(
            url: url,
            method: "GET",
            headers: [:],
            bodyData: nil
        )

        // 使用NetworkService发送请求
        return try await networkService.request(apiEndpoint)
    }

    /// 异步发送POST请求并返回解码后的对象
    /// - Parameters:
    ///   - endpoint: API端点路径（相对于baseURL）
    ///   - parameters: 请求参数
    ///   - type: 解码类型
    /// - Returns: 解码后的对象
    func post<T: Decodable>(_ endpoint: String, parameters: [String: Any]?, as type: T.Type) async throws -> T {
        // 构建完整URL
        let baseURLString = AppEnvironment.current.baseURL.absoluteString
        let fullURLString = baseURLString + (endpoint.hasPrefix("/") ? endpoint : "/" + endpoint)

        guard let url = URL(string: fullURLString) else {
            throw AppError.invalidResponse
        }

        // 准备请求体
        var body: Data? = nil
        if let parameters = parameters {
            do {
                body = try JSONSerialization.data(withJSONObject: parameters)
            } catch {
                throw AppError.badRequest("Invalid parameters")
            }
        }

        // 创建自定义APIEndpoint
        let apiEndpoint = APIEndpoint.custom(
            url: url,
            method: "POST",
            headers: [:],
            bodyData: body
        )

        // 使用NetworkService发送请求
        return try await networkService.request(apiEndpoint)
    }
}
