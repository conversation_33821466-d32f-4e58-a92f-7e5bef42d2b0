import Foundation
import Combine

/// 网络服务协议，定义网络请求的基本方法
public protocol NetworkServiceProtocol {
    /// 发送请求并返回解码后的对象
    /// - Parameter endpoint: API端点
    /// - Returns: 解码后的对象
    func request<T: Decodable>(_ endpoint: APIEndpoint) async throws -> T

    /// 发送请求，不需要返回值
    /// - Parameter endpoint: API端点
    func requestNoResponse(_ endpoint: APIEndpoint) async throws

    /// 发送请求并返回原始数据
    /// - Parameter endpoint: API端点
    /// - Returns: 原始数据
    func requestRawData(_ endpoint: APIEndpoint) async throws -> Data

    /// 检查网络连接状态
    /// - Returns: 是否有网络连接
    func isNetworkAvailable() -> Bool
}
