import Foundation
import Combine

/// 进度追踪视图模型，处理学习进度展示逻辑
class ProgressTrackingViewModel: ObservableObject {
    private let personalizedLearningService: PersonalizedLearningServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    // 状态
    enum ProgressState: Equatable {
        case loading
        case loaded
        case error(String)

        static func == (lhs: ProgressState, rhs: ProgressState) -> Bool {
            switch (lhs, rhs) {
            case (.loading, .loading), (.loaded, .loaded):
                return true
            case (.error(let lhsError), .error(let rhsError)):
                return lhsError == rhsError
            default:
                return false
            }
        }
    }

    // 发布的属性
    @Published var state: ProgressState = .loading
    @Published var practiceHistory: [PracticeResult] = []
    @Published var learningStatus: [String: Any]?
    @Published var errorMessage: String?

    // 模拟数据，实际应用中应该从API获取
    private var mockLearningStatus: [String: Any] {
        return [
            "hasActivePath": true,
            "activePath": [
                "id": UUID().uuidString,
                "userID": UUID().uuidString,
                "title": "中文初级学习路径",
                "description": "为初学者设计的中文学习路径，包含基础词汇、语法和日常对话",
                "status": "active",
                "evaluationID": UUID().uuidString,
                "level": "beginner",
                "focusAreas": ["词汇", "语法", "听力"],
                "estimatedDuration": 30,
                "progress": 35,
                "startDate": Date().addingTimeInterval(-86400 * 7), // 一周前
                "completedDate": nil,
                "lessons": [
                    [
                        "id": UUID().uuidString,
                        "learningPathID": UUID().uuidString,
                        "lessonID": "lesson1",
                        "order": 1,
                        "isRequired": true,
                        "isCompleted": true,
                        "completedDate": Date().addingTimeInterval(-86400 * 5)
                    ],
                    [
                        "id": UUID().uuidString,
                        "learningPathID": UUID().uuidString,
                        "lessonID": "lesson2",
                        "order": 2,
                        "isRequired": true,
                        "isCompleted": true,
                        "completedDate": Date().addingTimeInterval(-86400 * 3)
                    ],
                    [
                        "id": UUID().uuidString,
                        "learningPathID": UUID().uuidString,
                        "lessonID": "lesson3",
                        "order": 3,
                        "isRequired": true,
                        "isCompleted": false,
                        "completedDate": nil
                    ]
                ],
                "createdAt": Date().addingTimeInterval(-86400 * 7),
                "updatedAt": Date()
            ],
            "completedPathsCount": 0,
            "overallCompletionPercentage": 35.0,
            "currentLevel": "beginner",
            "nextAssessmentDate": Date().addingTimeInterval(86400 * 30) // 30天后
        ]
    }

    // 图表数据
    @Published var weeklyScores: [Double] = []
    @Published var skillScores: [String: Double] = [:]

    /// 初始化
    /// - Parameter personalizedLearningService: 个性化学习服务
    init(personalizedLearningService: PersonalizedLearningServiceProtocol = DependencyContainer.shared.resolve(PersonalizedLearningServiceProtocol.self)) {
        self.personalizedLearningService = personalizedLearningService
        loadData()
    }

    /// 加载数据
    func loadData() {
        state = .loading

        Task {
            do {
                async let historyData = personalizedLearningService.getPracticeHistory()
                async let statusData = personalizedLearningService.getPersonalizedLearningStatus()

                let (resolvedHistoryData, resolvedStatusData) = try await (historyData, statusData)

                await MainActor.run {
                    print("收到学习状态数据: \(resolvedStatusData)")

                    // 处理练习历史数据
                    if let historyArray = resolvedHistoryData as? [[String: Any]] {
                        self.practiceHistory = historyArray.compactMap { dict -> PracticeResult? in
                            guard let idString = dict["id"] as? String,
                                  let id = UUID(uuidString: idString),
                                  let practiceIDString = dict["practiceID"] as? String,
                                  let practiceID = UUID(uuidString: practiceIDString),
                                  let userIDString = dict["userID"] as? String,
                                  let userID = UUID(uuidString: userIDString),
                                  let typeString = dict["practiceType"] as? String,
                                  let type = DailyPracticeType(rawValue: typeString),
                                  let score = dict["score"] as? Int,
                                  let totalPoints = dict["totalPoints"] as? Int,
                                  let correctCount = dict["correctCount"] as? Int,
                                  let totalCount = dict["totalCount"] as? Int,
                                  let completedAtString = dict["completedAt"] as? String,
                                  let completedAt = ISO8601DateFormatter().date(from: completedAtString),
                                  let duration = dict["duration"] as? Int,
                                  let feedback = dict["feedback"] as? String,
                                  let recommendations = dict["recommendations"] as? [String],
                                  let createdAtString = dict["createdAt"] as? String,
                                  let createdAt = ISO8601DateFormatter().date(from: createdAtString) else {
                                return nil
                            }

                            return PracticeResult(
                                id: id,
                                practiceID: practiceID,
                                userID: userID,
                                practiceType: type,
                                score: score,
                                totalPoints: totalPoints,
                                correctCount: correctCount,
                                totalCount: totalCount,
                                completedAt: completedAt,
                                duration: duration,
                                feedback: feedback,
                                recommendations: recommendations,
                                createdAt: createdAt
                            )
                        }
                    } else {
                        self.practiceHistory = []
                    }

                    // 处理学习状态数据
                    if let statusDict = resolvedStatusData as? [String: Any] {
                        self.learningStatus = statusDict
                    }

                    self.processData()
                    self.state = .loaded
                }
            } catch {
                await MainActor.run {
                    print("加载数据失败: \(error.localizedDescription)")
                    self.state = .error(error.localizedDescription)
                    self.errorMessage = error.localizedDescription
                    self.useMockData() // Fallback to mock data on error
                }
            }
        }
    }

    /// 使用模拟数据（当API调用失败时）
    private func useMockData() {
        // 设置模拟数据
        self.learningStatus = self.mockLearningStatus

        // 设置模拟练习历史
        self.practiceHistory = [
            PracticeResult(
                id: UUID(),
                practiceID: UUID(),
                userID: UUID(),
                practiceType: .vocabulary,
                score: 85,
                totalPoints: 100,
                correctCount: 17,
                totalCount: 20,
                completedAt: Date().addingTimeInterval(-86400), // 昨天
                duration: 600,
                feedback: "词汇掌握良好，但需要加强记忆生僻词",
                recommendations: ["多使用词汇卡片复习", "尝试使用这些词造句"],
                createdAt: Date().addingTimeInterval(-86400)
            ),
            PracticeResult(
                id: UUID(),
                practiceID: UUID(),
                userID: UUID(),
                practiceType: .grammar,
                score: 70,
                totalPoints: 100,
                correctCount: 7,
                totalCount: 10,
                completedAt: Date().addingTimeInterval(-172800), // 前天
                duration: 450,
                feedback: "语法基础不错，但复杂句型需要加强",
                recommendations: ["复习时态用法", "多做句型转换练习"],
                createdAt: Date().addingTimeInterval(-172800)
            ),
            PracticeResult(
                id: UUID(),
                practiceID: UUID(),
                userID: UUID(),
                practiceType: .listening,
                score: 90,
                totalPoints: 100,
                correctCount: 9,
                totalCount: 10,
                completedAt: Date().addingTimeInterval(-259200), // 3天前
                duration: 500,
                feedback: "听力表现优秀，继续保持",
                recommendations: ["尝试更快的语速", "尝试更复杂的对话"],
                createdAt: Date().addingTimeInterval(-259200)
            ),
            PracticeResult(
                id: UUID(),
                practiceID: UUID(),
                userID: UUID(),
                practiceType: .speaking,
                score: 75,
                totalPoints: 100,
                correctCount: 15,
                totalCount: 20,
                completedAt: Date().addingTimeInterval(-345600), // 4天前
                duration: 700,
                feedback: "发音基本准确，但语调需要改进",
                recommendations: ["多练习声调", "尝试跟读练习"],
                createdAt: Date().addingTimeInterval(-345600)
            ),
            PracticeResult(
                id: UUID(),
                practiceID: UUID(),
                userID: UUID(),
                practiceType: .reading,
                score: 80,
                totalPoints: 100,
                correctCount: 8,
                totalCount: 10,
                completedAt: Date().addingTimeInterval(-432000), // 5天前
                duration: 550,
                feedback: "阅读理解良好，但速度可以提高",
                recommendations: ["尝试快速阅读技巧", "扩大词汇量"],
                createdAt: Date().addingTimeInterval(-432000)
            )
        ]

        // 处理数据
        self.processData()
        self.state = .loaded
    }

    /// 处理数据，生成图表数据
    private func processData() {
        processWeeklyScores()
        processSkillScores()
    }

    /// 处理每周分数数据
    private func processWeeklyScores() {
        // 获取过去7天的日期
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        var weeklyScores: [Double] = []

        for dayOffset in (0..<7).reversed() {
            if let date = calendar.date(byAdding: .day, value: -dayOffset, to: today) {
                // 查找该日期的练习结果
                let dayResults = practiceHistory.filter { calendar.isDate($0.completedAt, inSameDayAs: date) }

                // 计算该日期的平均分
                if !dayResults.isEmpty {
                    let averageScore = dayResults.reduce(0.0) { $0 + Double($1.score) } / Double(dayResults.count)
                    weeklyScores.append(averageScore)
                } else {
                    weeklyScores.append(0.0)
                }
            }
        }

        self.weeklyScores = weeklyScores
    }

    /// 处理技能分数数据
    private func processSkillScores() {
        var skillScores: [String: [Int]] = [:]

        // 按技能类型分组练习结果
        for result in practiceHistory {
            let skill = result.practiceType.rawValue
            if skillScores[skill] == nil {
                skillScores[skill] = []
            }
            skillScores[skill]?.append(result.score)
        }

        // 计算每个技能的平均分
        var averageSkillScores: [String: Double] = [:]
        for (skill, scores) in skillScores {
            let average = Double(scores.reduce(0, +)) / Double(scores.count)
            averageSkillScores[skill] = average
        }

        self.skillScores = averageSkillScores
    }

    /// 获取学习streak天数
    /// - Returns: streak天数
    func learningStreakDays() -> Int {
        // 这里可能需要从API获取，临时返回计算值
        var streakDays = 0
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        for dayOffset in (0..<30) {
            if let date = calendar.date(byAdding: .day, value: -dayOffset, to: today) {
                let dayResults = practiceHistory.filter { calendar.isDate($0.completedAt, inSameDayAs: date) }

                if !dayResults.isEmpty {
                    streakDays += 1
                } else {
                    break
                }
            }
        }

        return streakDays
    }

    /// 获取最近一周的平均分
    /// - Returns: 平均分
    func weeklyAverageScore() -> Double {
        guard !weeklyScores.isEmpty else { return 0.0 }
        return weeklyScores.reduce(0.0, +) / Double(weeklyScores.count)
    }

    /// 获取最强技能
    /// - Returns: 技能名称和分数
    func strongestSkill() -> (skill: String, score: Double)? {
        guard !skillScores.isEmpty else { return nil }
        if let maxEntry = skillScores.max(by: { $0.value < $1.value }) {
            return (skill: maxEntry.key, score: maxEntry.value)
        }
        return nil
    }

    /// 获取最弱技能
    /// - Returns: 技能名称和分数
    func weakestSkill() -> (skill: String, score: Double)? {
        guard !skillScores.isEmpty else { return nil }
        if let minEntry = skillScores.min(by: { $0.value < $1.value }) {
            return (skill: minEntry.key, score: minEntry.value)
        }
        return nil
    }

    /// 获取技能名称的本地化字符串
    /// - Parameter skill: 技能类型
    /// - Returns: 本地化的技能名称
    func localizedSkillName(_ skill: String) -> String {
        switch skill {
        case "vocabulary":
            return "词汇"
        case "grammar":
            return "语法"
        case "listening":
            return "听力"
        case "speaking":
            return "口语"
        case "reading":
            return "阅读"
        case "writing":
            return "写作"
        case "mixed":
            return "综合"
        default:
            return skill
        }
    }
}
