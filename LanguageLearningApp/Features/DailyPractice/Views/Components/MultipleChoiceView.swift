import SwiftUI

/// 多选题视图组件
struct MultipleChoiceView: View {
    let exercise: [String: Any]
    @Binding var selectedOption: String?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            if let options = ExerciseDataProcessor.getExerciseOptions(from: exercise) {
                ForEach(options, id: \.self) { option in
                    Button(action: {
                        selectedOption = option
                    }) {
                        HStack {
                            Text(option)
                                .font(.body)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)
                            
                            Spacer()
                            
                            if selectedOption == option {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(selectedOption == option ? Color.blue.opacity(0.1) : Color(UIColor.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            } else {
                Text("没有可用的选项")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .padding()
            }
        }
    }
}

#if DEBUG
struct MultipleChoiceView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleExercise: [String: Any] = [
            "question": "选择正确的单词含义",
            "options": ["选项A", "选项B", "选项C", "选项D"]
        ]
        
        return MultipleChoiceView(
            exercise: sampleExercise,
            selectedOption: .constant("选项A")
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
#endif
