import SwiftUI

/// 填空题视图组件
struct FillInView: View {
    @Binding var textInput: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("请输入您的答案:")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            TextField("在此输入", text: $textInput)
                .padding()
                .background(Color(UIColor.systemGray6))
                .cornerRadius(10)
                .autocapitalization(.none)
        }
    }
}

#if DEBUG
struct FillInView_Previews: PreviewProvider {
    static var previews: some View {
        FillInView(textInput: .constant("示例答案"))
            .padding()
            .previewLayout(.sizeThatFits)
    }
}
#endif
