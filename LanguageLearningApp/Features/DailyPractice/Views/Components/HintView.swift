import SwiftUI

/// 提示视图组件
struct HintView: View {
    let hint: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 5) {
            Text("提示")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.blue)
            
            Text(hint)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(10)
    }
}

#if DEBUG
struct HintView_Previews: PreviewProvider {
    static var previews: some View {
        HintView(hint: "这是一个提示，帮助用户解答问题。")
            .padding()
            .previewLayout(.sizeThatFits)
    }
}
#endif
