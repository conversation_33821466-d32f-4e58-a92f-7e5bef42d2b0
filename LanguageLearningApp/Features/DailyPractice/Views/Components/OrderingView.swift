import SwiftUI

/// 排序题视图组件
struct OrderingView: View {
    let exercise: [String: Any]
    @Binding var selectedOption: String?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("请选择正确的顺序:")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            if let options = ExerciseDataProcessor.getExerciseOptions(from: exercise) {
                ForEach(options, id: \.self) { option in
                    Button(action: {
                        selectedOption = option
                    }) {
                        HStack {
                            Text(option)
                                .font(.body)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)
                            
                            Spacer()
                            
                            if selectedOption == option {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(selectedOption == option ? Color.blue.opacity(0.1) : Color(UIColor.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
}

#if DEBUG
struct OrderingView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleExercise: [String: Any] = [
            "options": ["第一步", "第二步", "第三步", "第四步"]
        ]
        
        return OrderingView(
            exercise: sampleExercise,
            selectedOption: .constant("第一步")
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
#endif
