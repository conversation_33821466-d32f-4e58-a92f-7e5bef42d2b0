import SwiftUI

/// 阅读题视图组件
struct ReadingView: View {
    let exercise: [String: Any]
    @Binding var selectedOption: String?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            // 阅读内容
            ScrollView {
                Text(exercise["content"] as? String ?? "")
                    .font(.body)
                    .padding()
                    .background(Color(UIColor.systemGray6))
                    .cornerRadius(10)
            }
            .frame(height: 200)
            
            // 问题
            Text("请回答问题:")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // 选项
            if let options = ExerciseDataProcessor.getExerciseOptions(from: exercise) {
                ForEach(options, id: \.self) { option in
                    Button(action: {
                        selectedOption = option
                    }) {
                        HStack {
                            Text(option)
                                .font(.body)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.leading)
                            
                            Spacer()
                            
                            if selectedOption == option {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(selectedOption == option ? Color.blue.opacity(0.1) : Color(UIColor.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
}

#if DEBUG
struct ReadingView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleExercise: [String: Any] = [
            "content": "这是一段阅读文本，用于测试阅读理解能力。这段文本应该足够长，以便测试滚动功能。",
            "options": ["选项A", "选项B", "选项C", "选项D"]
        ]
        
        return ReadingView(
            exercise: sampleExercise,
            selectedOption: .constant("选项A")
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
#endif
