import SwiftUI

/// 每日练习仪表板视图
struct DailyPracticeDashboardView: View {
    @StateObject private var viewModel = PersonalizedPracticeViewModel()
    @StateObject private var progressViewModel = ProgressTrackingViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var showingInitiateView = false
    @State private var showingDetailedStats = false
    @State private var isRefreshing = false
    
    // Add state variables for NavigationLink isActive
    @State private var showEvaluationView = false
    @State private var showPracticeSessionView = false
    @State private var showDetailedStatsView = false // Add state for detailed stats navigation

    var body: some View {
        StyledContainer {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 标题
                    Text(localizationManager.localizedString(LocalizationKey.daily_practice))
                        .font(AppTheme.Typography.title1)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .padding(.top)

                    // 学习进度卡片
                    learningProgressCard()

                    // 今日推荐练习
                    if case .error(_) = viewModel.state {
                        // 错误信息
                        Text(viewModel.errorMessage ?? localizationManager.localizedString(LocalizationKey.errorUnknown))
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.error)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .center)
                            .background(AppTheme.Colors.backgroundSecondary)
                            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                    } else {
                        todayPracticeCard()
                    }

                    // 学习统计
                    learningStatsSection()

                    // 技能分析
                    skillAnalysisSection()
                }
                .padding()
            }
            .refreshable {
                isRefreshing = true
                await refreshData()
                isRefreshing = false
            }
        }
        .sheet(isPresented: $showingInitiateView) {
            InitiatePersonalizedLearningView()
        }
        // Hidden NavigationLinks
        .background(
            Group { // Wrap hidden NavigationLinks in a Group
                NavigationLink(
                    destination: StartNewEvaluationView(),
                    isActive: $showEvaluationView,
                    label: { EmptyView() }
                )
                .hidden()

                NavigationLink(
                    destination: PracticeSessionView(viewModel: PersonalizedPracticeViewModel(practiceId: UUID(uuidString: viewModel.practice?["id"] as? String ?? "") ?? UUID())),
                    isActive: $showPracticeSessionView,
                    label: { EmptyView() }
                )
                .hidden()

                NavigationLink(
                    destination: ProgressTrackingView(viewModel: progressViewModel),
                    isActive: $showDetailedStatsView,
                    label: { EmptyView() }
                )
                .hidden()
            }
        )
        .onAppear {
            if case .loading = viewModel.state {
                viewModel.loadTodayPractice()
            }

            if case .loading = progressViewModel.state {
                progressViewModel.loadData()
            }
        }
    }

    /// 学习进度卡片
    private func learningProgressCard() -> some View {
        StyledCard {
            VStack(alignment: .leading, spacing: 15) {
                HStack {
                    VStack(alignment: .leading, spacing: 5) {
                        Text(localizationManager.localizedString(LocalizationKey.learning_progress))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        if let status = progressViewModel.learningStatus {
                            if let dataDict = status["data"] as? [String: Any],
                               let hasActiveLearningPath = dataDict["hasActiveLearningPath"] as? Int,
                               hasActiveLearningPath == 1 {
                                Text(localizationManager.localizedString(LocalizationKey.continue_learning))
                                    .font(AppTheme.Typography.subheadline)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            } else if let hasActivePath = status["hasActivePath"] as? Bool, hasActivePath {
                                Text(localizationManager.localizedString(LocalizationKey.continue_learning))
                                    .font(AppTheme.Typography.subheadline)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            } else {
                                Text(localizationManager.localizedString(LocalizationKey.start_learning))
                                    .font(AppTheme.Typography.subheadline)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            }
                        } else {
                            Text(localizationManager.localizedString(LocalizationKey.loading))
                                .font(AppTheme.Typography.subheadline)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }
                    }

                    Spacer()

                    // 学习streak
                    VStack {
                        Text("\(progressViewModel.learningStreakDays())")
                            .font(AppTheme.Typography.title2)
                            .foregroundColor(AppTheme.Colors.accent)

                        Text(localizationManager.localizedString(LocalizationKey.streak))
                            .font(AppTheme.Typography.caption)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }
                }

                VStack(spacing: 10) {
                    // "Start Evaluation" button
                    StyledButton(
                        title: localizationManager.localizedString(LocalizationKey.start_evaluation),
                        action: { 
                            print("Start Evaluation button tapped. Setting showEvaluationView to true.")
                            showEvaluationView = true 
                        }, // Set the state variable
                        icon: "chart.bar.fill"
                        // No need for allowLongPressGesture as the button action triggers navigation
                    )

                    StyledButton(
                        title: localizationManager.localizedString(LocalizationKey.start_personalized_learning),
                        action: { showingInitiateView = true },
                        icon: "person.fill",
                        isPrimary: false
                    )
                }
            }
            .padding()
        }
    }

    /// 今日推荐练习卡片
    private func todayPracticeCard() -> some View {
        StyledCard {
            VStack(alignment: .leading, spacing: 15) {
                Text(localizationManager.localizedString(LocalizationKey.recommended_practice))
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                if case .loading = viewModel.state {
                    ProgressView()
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                } else if let practice = viewModel.practice {
                    VStack(alignment: .leading, spacing: 10) {
                        Text(practice["title"] as? String ?? localizationManager.localizedString(LocalizationKey.today_practice))
                            .font(AppTheme.Typography.title3)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(practice["description"] as? String ?? localizationManager.localizedString(LocalizationKey.practice_description))
                            .font(AppTheme.Typography.body)
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        Divider()

                        HStack {
                            practiceInfoItem(
                                icon: practiceTypeIcon(DailyPracticeType(rawValue: practice["type"] as? String ?? "mixed") ?? .mixed),
                                text: localizedPracticeType(DailyPracticeType(rawValue: practice["type"] as? String ?? "mixed") ?? .mixed)
                            )

                            practiceInfoItem(
                                icon: "list.bullet",
                                text: "\((practice["exercises"] as? [[String: Any]])?.count ?? 0) \(localizationManager.localizedString(LocalizationKey.exercises))"
                            )

                            practiceInfoItem(
                                icon: "clock",
                                text: "\(practice["estimatedTimeMinutes"] as? Int ?? 15) \(localizationManager.localizedString(LocalizationKey.minutes))"
                            )
                        }
                        .padding(.vertical, 5)

                        if let practiceId = UUID(uuidString: practice["id"] as? String ?? "") {
                            // "Start Practice" button
                            StyledButton(
                                title: (practice["isCompleted"] as? Bool ?? false) ?
                                    localizationManager.localizedString(LocalizationKey.practice_again) :
                                    localizationManager.localizedString(LocalizationKey.start_practice),
                                action: { 
                                    print("Start Practice button tapped. Setting showPracticeSessionView to true.")
                                    showPracticeSessionView = true 
                                }, // Set the state variable
                                icon: "play.fill"
                                // No need for allowLongPressGesture
                            )
                            .padding(.top, 5)
                        }
                    }
                } else {
                    VStack(spacing: 10) {
                        Image(systemName: "checkmark.circle")
                            .font(.system(size: 50))
                            .foregroundColor(AppTheme.Colors.success)

                        Text(localizationManager.localizedString(LocalizationKey.practice_completed))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(localizationManager.localizedString(LocalizationKey.practice_completed_message))
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                }
            }
            .padding()
        }
    }

    /// 练习信息项
    private func practiceInfoItem(icon: String, text: String) -> some View {
        VStack {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(AppTheme.Colors.primary)

            Text(text)
                .font(AppTheme.Typography.caption)
                .foregroundColor(AppTheme.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
    }

    /// 学习统计部分
    private func learningStatsSection() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text(localizationManager.localizedString(LocalizationKey.statistics))
                .font(AppTheme.Typography.headline)
                .foregroundColor(AppTheme.Colors.textPrimary)

            HStack(spacing: 15) {
                statCard(
                    title: localizationManager.localizedString(LocalizationKey.weekly_average),
                    value: String(format: "%.1f", progressViewModel.weeklyAverageScore()),
                    icon: "chart.bar.fill",
                    color: AppTheme.Colors.primary
                )

                statCard(
                    title: localizationManager.localizedString(LocalizationKey.completed_exercises),
                    value: "\(progressViewModel.practiceHistory.count)",
                    icon: "checkmark.circle.fill",
                    color: AppTheme.Colors.success
                )
            }

            // "View Detailed Stats" button
            StyledButton(
                title: localizationManager.localizedString(LocalizationKey.view_detailed_stats),
                action: { 
                    print("View Detailed Stats button tapped. Setting showDetailedStatsView to true.")
                    showDetailedStatsView = true 
                }, // Set the state variable
                icon: "chart.bar.fill",
                isPrimary: false
                // No need for allowLongPressGesture
            )
        }
    }

    /// 统计卡片
    private func statCard(title: String, value: String, icon: String, color: Color) -> some View {
        StyledCard {
            VStack(spacing: 10) {
                Image(systemName: icon)
                    .font(.system(size: 30))
                    .foregroundColor(color)

                Text(value)
                    .font(AppTheme.Typography.title2)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                Text(title)
                    .font(AppTheme.Typography.caption)
                    .foregroundColor(AppTheme.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding()
        }
    }

    /// 技能分析部分
    private func skillAnalysisSection() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text(localizationManager.localizedString(LocalizationKey.skill_analysis))
                .font(AppTheme.Typography.headline)
                .foregroundColor(AppTheme.Colors.textPrimary)

            if case .loading = progressViewModel.state {
                ProgressView()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else if !progressViewModel.skillScores.isEmpty {
                StyledCard {
                    VStack(spacing: 15) {
                        ForEach(progressViewModel.skillScores.sorted(by: { $0.value > $1.value }), id: \.key) { skill, score in
                            skillScoreRow(
                                title: progressViewModel.localizedSkillName(skill),
                                score: Int(score),
                                maxScore: 100
                            )
                        }
                    }
                    .padding()
                }

                if let strongest = progressViewModel.strongestSkill(),
                   let weakest = progressViewModel.weakestSkill() {
                    HStack(spacing: 15) {
                        skillAnalysisCard(
                            title: localizationManager.localizedString(LocalizationKey.strongest_skill),
                            skill: progressViewModel.localizedSkillName(strongest.skill),
                            score: strongest.score,
                            color: AppTheme.Colors.success
                        )

                        skillAnalysisCard(
                            title: localizationManager.localizedString(LocalizationKey.weakest_skill),
                            skill: progressViewModel.localizedSkillName(weakest.skill),
                            score: weakest.score,
                            color: AppTheme.Colors.warning
                        )
                    }
                }
            } else {
                StyledCard {
                    Text(localizationManager.localizedString(LocalizationKey.no_skill_data))
                        .font(AppTheme.Typography.subheadline)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                }
            }
        }
    }

    /// 技能分析卡片
    private func skillAnalysisCard(title: String, skill: String, score: Double, color: Color) -> some View {
        StyledCard {
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(AppTheme.Typography.caption)
                    .foregroundColor(AppTheme.Colors.textSecondary)

                Text(skill)
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                Text(String(format: "%.1f \(localizationManager.localizedString(LocalizationKey.points))", score))
                    .font(AppTheme.Typography.subheadline)
                    .foregroundColor(color)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding()
        }
    }

    /// 技能分数行视图
    private func skillScoreRow(title: String, score: Int, maxScore: Int) -> some View {
        VStack(alignment: .leading, spacing: 5) {
            HStack {
                Text(title)
                    .font(AppTheme.Typography.subheadline)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                Spacer()

                Text("\(score)/\(maxScore)")
                    .font(AppTheme.Typography.subheadline)
                    .foregroundColor(AppTheme.Colors.textSecondary)
            }

            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(AppTheme.Colors.backgroundSecondary)
                        .frame(height: 8)
                        .cornerRadius(4)

                    Rectangle()
                        .fill(scoreColor(score))
                        .frame(width: geometry.size.width * CGFloat(score) / CGFloat(maxScore), height: 8)
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
        }
    }

    /// 根据分数获取颜色
    private func scoreColor(_ score: Int) -> Color {
        if score >= 90 {
            return AppTheme.Colors.success
        } else if score >= 70 {
            return AppTheme.Colors.primary
        } else if score >= 50 {
            return AppTheme.Colors.warning
        } else if score >= 30 {
            return AppTheme.Colors.accent
        } else {
            return AppTheme.Colors.error
        }
    }

    /// 获取练习类型图标
    private func practiceTypeIcon(_ type: DailyPracticeType) -> String {
        switch type {
        case .vocabulary:
            return "textformat.abc"
        case .grammar:
            return "text.book.closed"
        case .listening:
            return "ear"
        case .speaking:
            return "mic"
        case .reading:
            return "book"
        case .writing:
            return "pencil"
        case .mixed:
            return "square.grid.2x2"
        }
    }

    /// 获取本地化的练习类型名称
    private func localizedPracticeType(_ type: DailyPracticeType) -> String {
        switch type {
        case .vocabulary:
            return localizationManager.localizedString(LocalizationKey.vocabulary)
        case .grammar:
            return localizationManager.localizedString(LocalizationKey.grammar)
        case .listening:
            return localizationManager.localizedString(LocalizationKey.listening)
        case .speaking:
            return localizationManager.localizedString(LocalizationKey.speaking)
        case .reading:
            return localizationManager.localizedString(LocalizationKey.reading)
        case .writing:
            return localizationManager.localizedString(LocalizationKey.writing)
        case .mixed:
            return localizationManager.localizedString(LocalizationKey.mixed)
        }
    }
}

// MARK: - 刷新相关扩展
extension DailyPracticeDashboardView {
    /// 刷新数据
    func refreshData() async {
        await withTaskGroup(of: Void.self) { group in
            group.addTask {
                await refreshPracticeData()
            }
            group.addTask {
                await refreshProgressData()
            }
        }
    }

    /// 刷新个性化练习数据
    private func refreshPracticeData() async {
        await withCheckedContinuation { (continuation: CheckedContinuation<Void, Never>) in
            viewModel.loadTodayPractice()
            continuation.resume()
        }
    }

    /// 刷新进度数据
    private func refreshProgressData() async {
        await withCheckedContinuation { (continuation: CheckedContinuation<Void, Never>) in
            progressViewModel.loadData()
            continuation.resume()
        }
    }
}

struct DailyPracticeDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            DailyPracticeDashboardView()
        }
    }
}

