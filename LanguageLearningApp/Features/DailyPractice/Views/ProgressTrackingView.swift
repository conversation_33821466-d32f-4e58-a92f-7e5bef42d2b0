import SwiftUI

/// 进度追踪视图
struct ProgressTrackingView: View {
    @ObservedObject var viewModel: ProgressTrackingViewModel
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 标题
                Text("学习进度")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(Color.primary)
                    .padding(.top)

                if case .loaded = viewModel.state {
                    // 学习概览
                    learningOverviewSection()

                    // 每周进度
                    weeklyProgressSection()

                    // 技能分析
                    skillAnalysisSection()

                    // 学习历史
                    learningHistorySection()
                } else if case .loading = viewModel.state {
                    // 加载中
                    ProgressView()
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                } else {
                    // 错误信息
                    Text(viewModel.errorMessage ?? "加载数据失败")
                        .font(.headline)
                        .foregroundColor(.red)
                        .padding()
                }
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(colorScheme == .dark ? Color.black : Color.white)
        }
        .navigationTitle("学习进度")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            if case .loading = viewModel.state {
                viewModel.loadData()
            }
        }
    }

    /// 学习概览部分
    /// - Returns: 概览部分视图
    private func learningOverviewSection() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("学习概览")
                .font(.headline)
                .fontWeight(.semibold)

            HStack(spacing: 15) {
                // 学习天数
                statCard(
                    title: "连续学习",
                    value: "\(viewModel.learningStreakDays())",
                    unit: "天",
                    icon: "flame.fill",
                    color: .orange
                )

                // 完成练习
                statCard(
                    title: "完成练习",
                    value: "\(viewModel.practiceHistory.count)",
                    unit: "个",
                    icon: "checkmark.circle.fill",
                    color: .green
                )
            }

            if let status = viewModel.learningStatus,
               let pathDict = status["activePath"] as? [String: Any] {
                // 创建一个临时的学习路径对象来访问属性
                let path = LearningPathViewModel(
                    id: UUID(uuidString: pathDict["id"] as? String ?? "") ?? UUID(),
                    title: pathDict["title"] as? String ?? "学习路径",
                    level: LessonLevel(rawValue: pathDict["level"] as? String ?? "beginner") ?? .beginner,
                    completionPercentage: pathDict["progress"] as? Double ?? 0.0,
                    completedLessonsCount: (pathDict["lessons"] as? [[String: Any]])?.filter { $0["isCompleted"] as? Bool ?? false }.count ?? 0,
                    totalLessonsCount: (pathDict["lessons"] as? [[String: Any]])?.count ?? 0,
                    estimatedDuration: pathDict["estimatedDuration"] as? Int ?? 30
                )
                VStack(alignment: .leading, spacing: 10) {
                    // 当前学习路径
                    HStack {
                        Text("当前学习路径")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(path.completionPercentage))% 完成")
                            .font(.subheadline)
                            .foregroundColor(.blue)
                    }

                    // 进度条
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            // 背景
                            Rectangle()
                                .fill(Color(UIColor.systemGray5))
                                .frame(height: 8)
                                .cornerRadius(4)

                            // 进度
                            Rectangle()
                                .fill(Color.blue)
                                .frame(width: geometry.size.width * CGFloat(path.completionPercentage) / 100.0, height: 8)
                                .cornerRadius(4)
                        }
                    }
                    .frame(height: 8)

                    // 路径详情
                    HStack {
                        VStack(alignment: .leading, spacing: 5) {
                            Text(path.title)
                                .font(.subheadline)
                                .fontWeight(.medium)

                            Text(path.level.rawValue.capitalized)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        VStack(alignment: .trailing, spacing: 5) {
                            Text("\(path.completedLessonsCount)/\(path.totalLessonsCount) 课程")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text("预计 \(path.estimatedDuration) 天")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color(UIColor.systemGray6))
                .cornerRadius(10)
            }
        }
    }

    /// 每周进度部分
    /// - Returns: 每周进度部分视图
    private func weeklyProgressSection() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("每周进度")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 10) {
                // 周平均分
                HStack {
                    Text("本周平均分")
                        .font(.subheadline)

                    Spacer()

                    Text(String(format: "%.1f", viewModel.weeklyAverageScore()))
                        .font(.headline)
                        .foregroundColor(.blue)
                }

                // 周进度图表
                weeklyChart()
                    .frame(height: 200)
                    .padding(.top, 5)
            }
            .padding()
            .background(Color(UIColor.systemGray6))
            .cornerRadius(10)
        }
    }

    /// 技能分析部分
    /// - Returns: 技能分析部分视图
    private func skillAnalysisSection() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("技能分析")
                .font(.headline)
                .fontWeight(.semibold)

            if !viewModel.skillScores.isEmpty {
                VStack(spacing: 15) {
                    ForEach(viewModel.skillScores.sorted(by: { $0.value > $1.value }), id: \.key) { skill, score in
                        skillScoreRow(
                            title: viewModel.localizedSkillName(skill),
                            score: Int(score),
                            maxScore: 100
                        )
                    }
                }
                .padding()
                .background(Color(UIColor.systemGray6))
                .cornerRadius(10)

                // 强弱项分析
                if let strongest = viewModel.strongestSkill(),
                   let weakest = viewModel.weakestSkill() {

                    HStack(spacing: 15) {
                        // 最强项
                        VStack(alignment: .leading, spacing: 5) {
                            Text("最强项")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text(viewModel.localizedSkillName(strongest.skill))
                                .font(.headline)
                                .fontWeight(.medium)

                            Text(String(format: "%.1f 分", strongest.score))
                                .font(.subheadline)
                                .foregroundColor(.green)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                        .background(Color(UIColor.systemGray6))
                        .cornerRadius(10)

                        // 最弱项
                        VStack(alignment: .leading, spacing: 5) {
                            Text("需要提高")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text(viewModel.localizedSkillName(weakest.skill))
                                .font(.headline)
                                .fontWeight(.medium)

                            Text(String(format: "%.1f 分", weakest.score))
                                .font(.subheadline)
                                .foregroundColor(.orange)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                        .background(Color(UIColor.systemGray6))
                        .cornerRadius(10)
                    }
                }
            } else {
                Text("暂无技能数据")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
                    .background(Color(UIColor.systemGray6))
                    .cornerRadius(10)
            }
        }
    }

    /// 学习历史部分
    /// - Returns: 学习历史部分视图
    private func learningHistorySection() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("学习历史")
                .font(.headline)
                .fontWeight(.semibold)

            if !viewModel.practiceHistory.isEmpty {
                VStack(spacing: 0) {
                    ForEach(viewModel.practiceHistory.prefix(5), id: \.id) { result in
                        historyRow(result)

                        if result.id != viewModel.practiceHistory.prefix(5).last?.id {
                            Divider()
                                .padding(.leading, 40)
                        }
                    }
                }
                .padding()
                .background(Color(UIColor.systemGray6))
                .cornerRadius(10)

                // 查看更多按钮
                if viewModel.practiceHistory.count > 5 {
                    Button(action: {
                        // 导航到完整历史页面
                    }) {
                        Text("查看更多历史")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(10)
                    }
                }
            } else {
                Text("暂无学习历史")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
                    .background(Color(UIColor.systemGray6))
                    .cornerRadius(10)
            }
        }
    }

    /// 统计卡片
    /// - Parameters:
    ///   - title: 标题
    ///   - value: 值
    ///   - unit: 单位
    ///   - icon: 图标名称
    ///   - color: 颜色
    /// - Returns: 统计卡片视图
    private func statCard(title: String, value: String, unit: String, icon: String, color: Color) -> some View {
        VStack(spacing: 10) {
            Image(systemName: icon)
                .font(.system(size: 30))
                .foregroundColor(color)

            HStack(alignment: .lastTextBaseline, spacing: 2) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)

                Text(unit)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(UIColor.systemGray6))
        .cornerRadius(10)
    }

    /// 技能分数行视图
    /// - Parameters:
    ///   - title: 技能标题
    ///   - score: 分数
    ///   - maxScore: 最高分
    /// - Returns: 技能分数行视图
    private func skillScoreRow(title: String, score: Int, maxScore: Int) -> some View {
        VStack(alignment: .leading, spacing: 5) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                Text("\(score)/\(maxScore)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    Rectangle()
                        .fill(Color(UIColor.systemGray5))
                        .frame(height: 8)
                        .cornerRadius(4)

                    // 进度
                    Rectangle()
                        .fill(scoreColor(score))
                        .frame(width: geometry.size.width * CGFloat(score) / CGFloat(maxScore), height: 8)
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
        }
    }

    /// 历史记录行视图
    /// - Parameter result: 练习结果
    /// - Returns: 历史记录行视图
    private func historyRow(_ result: PracticeResult) -> some View {
        HStack(spacing: 15) {
            // 图标
            Image(systemName: practiceTypeIcon(result.practiceType))
                .font(.system(size: 20))
                .foregroundColor(.white)
                .frame(width: 36, height: 36)
                .background(practiceTypeColor(result.practiceType))
                .cornerRadius(18)

            // 内容
            VStack(alignment: .leading, spacing: 3) {
                Text(viewModel.localizedSkillName(result.practiceType.rawValue))
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(formatDate(result.completedAt))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            // 得分
            VStack(alignment: .trailing, spacing: 3) {
                Text("\(result.score) 分")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(scoreColor(result.score))

                Text("\(String(format: "%.1f%%", result.correctPercentage))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
    }

    /// 每周图表视图
    /// - Returns: 图表视图
    private func weeklyChart() -> some View {
        // 简化实现，实际应用中可能需要更复杂的图表库
        GeometryReader { geometry in
            VStack {
                // 图表
                HStack(alignment: .bottom, spacing: 5) {
                    ForEach(0..<viewModel.weeklyScores.count, id: \.self) { index in
                        let score = viewModel.weeklyScores[index]
                        let height = score > 0 ? CGFloat(score) / 100.0 * (geometry.size.height - 40) : 0

                        VStack {
                            // 柱状图
                            Rectangle()
                                .fill(scoreColor(Int(score)))
                                .frame(width: (geometry.size.width - CGFloat(viewModel.weeklyScores.count - 1) * 5) / CGFloat(viewModel.weeklyScores.count), height: height)
                                .cornerRadius(5)

                            // 日期标签
                            Text(dayOfWeek(index))
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.bottom, 20)

                // 图例
                HStack {
                    ForEach([0, 25, 50, 75, 100], id: \.self) { value in
                        Text("\(value)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity)
                    }
                }
            }
        }
    }

    /// 获取星期几
    /// - Parameter index: 索引（0-6）
    /// - Returns: 星期几的缩写
    private func dayOfWeek(_ index: Int) -> String {
        let days = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        return days[index]
    }

    /// 获取练习类型图标
    /// - Parameter type: 练习类型
    /// - Returns: 图标名称
    private func practiceTypeIcon(_ type: DailyPracticeType) -> String {
        switch type {
        case .vocabulary:
            return "textformat.abc"
        case .grammar:
            return "text.book.closed"
        case .listening:
            return "ear"
        case .speaking:
            return "mic"
        case .reading:
            return "book"
        case .writing:
            return "pencil"
        case .mixed:
            return "square.grid.2x2"
        }
    }

    /// 获取练习类型颜色
    /// - Parameter type: 练习类型
    /// - Returns: 颜色
    private func practiceTypeColor(_ type: DailyPracticeType) -> Color {
        switch type {
        case .vocabulary:
            return .blue
        case .grammar:
            return .purple
        case .listening:
            return .green
        case .speaking:
            return .orange
        case .reading:
            return .red
        case .writing:
            return .pink
        case .mixed:
            return .gray
        }
    }

    /// 根据分数获取颜色
    /// - Parameter score: 分数
    /// - Returns: 对应的颜色
    private func scoreColor(_ score: Int) -> Color {
        if score >= 90 {
            return .green
        } else if score >= 70 {
            return .blue
        } else if score >= 50 {
            return .yellow
        } else if score >= 30 {
            return .orange
        } else {
            return .red
        }
    }

    /// 格式化日期
    /// - Parameter date: 日期
    /// - Returns: 格式化的日期字符串
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
}

/// 临时的学习路径视图模型，用于从字典中提取数据
struct LearningPathViewModel {
    let id: UUID
    let title: String
    let level: LessonLevel
    let completionPercentage: Double
    let completedLessonsCount: Int
    let totalLessonsCount: Int
    let estimatedDuration: Int
}

struct ProgressTrackingView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ProgressTrackingView(viewModel: ProgressTrackingViewModel())
        }
    }
}
