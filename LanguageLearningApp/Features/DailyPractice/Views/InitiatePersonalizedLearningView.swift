import SwiftUI
import Combine

/// 直接启动个性化学习的视图
struct InitiatePersonalizedLearningView: View {
    @StateObject private var viewModel = InitiatePersonalizedLearningViewModel()
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        ZStack {
            StyledContainer {
                VStack(spacing: 24) {
                    // 标题和说明
                    VStack(spacing: 16) {
                        Text(localizationManager.localizedString(LocalizationKey.startPersonalizedLearning))
                            .font(AppTheme.Typography.title1)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .multilineTextAlignment(.center)
                        
                        Text(localizationManager.localizedString(LocalizationKey.personalizedLearningDescription))
                            .font(AppTheme.Typography.body)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }
                    .padding(.top, 30)

                    Spacer()

                    // 状态显示
                    statusView

                    Spacer()

                    // 启动按钮
                    StyledButton(
                        title: localizationManager.localizedString(LocalizationKey.startLearning),
                        action: {
                            viewModel.initiatePersonalizedLearning()
                        },
                        icon: "play.fill",
                        isPrimary: true,
                        isLoading: viewModel.isLoading
                    )
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)

                    // Navigation is handled by a .navigationDestination modifier later
                    // if viewModel.navigateToEvaluation, let evaluationId = viewModel.evaluationId {
                    //    NavigationLink(
                    //        destination: EvaluationView(evaluationId: evaluationId),
                    //        isActive: $viewModel.navigateToEvaluation,
                    //        label: { EmptyView() }
                    //    )
                    // }
                }
            }
            // Add navigationDestination for EvaluationView
            .navigationDestination(isPresented: $viewModel.navigateToEvaluation) {
                if let evalId = viewModel.evaluationId {
                    EvaluationView(evaluationId: evalId)
                } else {
                    // Provide a fallback or error view if evalId is nil, though logic should prevent this
                    Text("Error: Evaluation ID missing.")
                }
            }
        }
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarItems(trailing: closeButton)
        .alert(isPresented: $viewModel.showError) {
            Alert(
                title: Text(localizationManager.localizedString(LocalizationKey.error)),
                message: Text(viewModel.errorMessage),
                dismissButton: .default(Text(localizationManager.localizedString(LocalizationKey.ok)))
            )
        }
    }

    // 状态视图
    private var statusView: some View {
        Group {
            if viewModel.isLoading {
                StyledCard {
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.5)
                            .padding()
                            .tint(AppTheme.Colors.primary)

                        Text(localizationManager.localizedString(LocalizationKey.startingLearning))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }
                    .padding()
                }
            } else if viewModel.isSuccess {
                StyledCard {
                    VStack(spacing: 16) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 60))
                            .foregroundStyle(AppTheme.Colors.primaryGradient)

                        Text(localizationManager.localizedString(LocalizationKey.learningStarted))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(localizationManager.localizedString(LocalizationKey.evaluationPrompt))
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }
                    .padding()
                }
            }
        }
    }

    // 关闭按钮
    private var closeButton: some View {
        Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            Image(systemName: "xmark.circle.fill")
                .font(.title3)
                .foregroundColor(AppTheme.Colors.textSecondary)
        }
    }
}

/// 启动个性化学习视图模型
@MainActor
class InitiatePersonalizedLearningViewModel: ObservableObject {
    // 服务
    private let personalizedLearningService = PersonalizedLearningService.shared

    // 发布者
    @Published var isLoading = false
    @Published var isSuccess = false
    @Published var showError = false
    @Published var errorMessage = ""
    @Published var navigateToEvaluation = false
    @Published var evaluationId: UUID?

    // 取消令牌
    private var cancellables = Set<AnyCancellable>()

    /// 启动个性化学习
    func initiatePersonalizedLearning() {
        isLoading = true
        isSuccess = false
        showError = false

        print("开始启动个性化学习流程")

        Task {
            do {
                let evaluationId = try await personalizedLearningService.initiatePersonalizedLearning()
                await MainActor.run {
                    self.isLoading = false
                    self.isSuccess = true
                    self.evaluationId = evaluationId
                    print("成功获取评估ID: \(evaluationId)")

                    // 延迟2秒后导航到评估页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                        if let currentEvaluationId = self.evaluationId {
                            print("准备导航到评估页面，评估ID: \(currentEvaluationId)")
                            self.navigateToEvaluation = true
                        } else {
                            print("无法导航到评估页面，评估ID为nil")
                            self.showError = true
                            self.errorMessage = "无法获取评估ID，请重试"
                        }
                    }
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    self.showError = true
                    self.errorMessage = error.localizedDescription
                    print("启动个性化学习失败: \(error.localizedDescription)")

                    // 如果是网络错误，创建一个离线评估ID
                    if error.localizedDescription.contains("网络") || error.localizedDescription.contains("连接") {
                        print("检测到网络错误，创建离线评估ID")
                        let offlineEvaluationId = UUID()
                        self.evaluationId = offlineEvaluationId
                        self.isSuccess = true // Consider if this should still be true if primary action failed
                        print("创建的离线评估ID: \(offlineEvaluationId)")

                        // 延迟2秒后导航到评估页面
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            print("准备导航到离线评估页面，评估ID: \(offlineEvaluationId)")
                            self.navigateToEvaluation = true
                        }
                    }
                }
            }
        }
    }
}

struct InitiatePersonalizedLearningView_Previews: PreviewProvider {
    static var previews: some View {
        InitiatePersonalizedLearningView()
    }
}
