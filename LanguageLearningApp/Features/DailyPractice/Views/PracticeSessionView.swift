import SwiftUI
import AVFoundation

/// 练习会话视图
struct PracticeSessionView: View {
    @ObservedObject var viewModel: PersonalizedPracticeViewModel
    @State private var selectedOption: String?
    @State private var textInput: String = ""
    @State private var isRecording: Bool = false
    @State private var recordingURL: URL?
    @State private var isPlaying: Bool = false
    @State private var showFeedback: Bool = false
    @State private var isAnswerCorrect: Bool = false
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.presentationMode) var presentationMode

    // 通知观察者
    @State private var newExerciseObserver: NSObjectProtocol?
    @State private var answerSubmittedObserver: NSObjectProtocol?

    /// 初始化
    /// - Parameter viewModel: 个性化练习视图模型
    init(viewModel: PersonalizedPracticeViewModel) {
        self.viewModel = viewModel
    }

    var body: some View {
        ZStack {
            if case .completed = viewModel.state {
                completionView
            } else {
                mainContentView

                // 底部提交按钮
                submitButtonView

                // 答案反馈覆盖层
                if showFeedback {
                    feedbackView
                }
            }
        }
        .navigationTitle("每日练习")
        .navigationBarTitleDisplayMode(.inline)
        .background(colorScheme == .dark ? Color.black : Color.white)
        .onAppear(perform: setupOnAppear)
        .onDisappear(perform: cleanupOnDisappear)
    }

    /// 练习完成视图
    private var completionView: some View {
        VStack(spacing: 20) {
            // 顶部图标
            Image(systemName: "checkmark.circle.fill")
                .resizable()
                .scaledToFit()
                .frame(width: 100, height: 100)
                .foregroundColor(.green)
                .padding(.top, 40)

            // 标题
            Text("恭喜！")
                .font(.largeTitle)
                .fontWeight(.bold)
                .padding(.top, 10)

            // 副标题
            Text("你已完成今日所有练习")
                .font(.title2)
                .foregroundColor(.secondary)

            // 分隔线
            Divider()
                .padding(.horizontal, 40)
                .padding(.vertical, 10)

            // 统计信息
            VStack(alignment: .leading, spacing: 15) {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.blue)
                    Text("用时: \(viewModel.formattedElapsedTime())")
                    Spacer()
                }

                if let result = viewModel.result, let score = result["score"] as? Int {
                    HStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                        Text("得分: \(score)/100")
                        Spacer()
                    }
                }

                if let result = viewModel.result, let feedback = result["feedback"] as? String {
                    HStack {
                        Image(systemName: "text.bubble")
                            .foregroundColor(.purple)
                        Text("反馈: \(feedback)")
                            .lineLimit(2)
                        Spacer()
                    }
                }
            }
            .padding()
            .background(Color(UIColor.systemGray6))
            .cornerRadius(10)
            .padding(.horizontal)

            // 建议
            if let result = viewModel.result, let recommendations = result["recommendations"] as? [String], !recommendations.isEmpty {
                VStack(alignment: .leading, spacing: 10) {
                    Text("学习建议")
                        .font(.headline)
                        .padding(.bottom, 5)

                    ForEach(recommendations.prefix(3), id: \.self) { recommendation in
                        HStack(alignment: .top) {
                            Image(systemName: "lightbulb.fill")
                                .foregroundColor(.orange)
                                .padding(.top, 2)
                            Text(recommendation)
                                .fixedSize(horizontal: false, vertical: true)
                            Spacer()
                        }
                    }
                }
                .padding()
                .background(Color(UIColor.systemGray6))
                .cornerRadius(10)
                .padding(.horizontal)
            }

            Spacer()

            // 返回按钮
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Text("返回")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(10)
            }
            .padding()
        }
    }

    // MARK: - 子视图

    /// 主内容视图
    private var mainContentView: some View {
        VStack(spacing: 0) {
            // 顶部进度条
            ProgressView(value: min(1.0, max(0.0, viewModel.progress)))
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .padding(.horizontal)

            // 计时器
            Text("用时: \(viewModel.formattedElapsedTime())")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.top, 5)

            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    exerciseContentView

                    Spacer()
                }
                .padding()
                .frame(maxWidth: .infinity)
            }
        }
    }

    /// 练习内容视图
    private var exerciseContentView: some View {
        Group {
            if let exercise = viewModel.currentExercise {
                let actualExercise = ExerciseDataProcessor.extractActualExercise(from: exercise)

                // 使用通用的ExerciseContentView组件
                ExerciseContentView(
                    exerciseData: UnifiedExerciseData.fromExerciseDict(exercise),
                    currentIndex: viewModel.currentExerciseIndex,
                    totalCount: viewModel.totalExercises,
                    answerViewBuilder: { _ in
                        exerciseTypeView(actualExercise)
                    },
                    showHints: true,
                    isPlaying: $isPlaying,
                    togglePlayback: togglePlayback
                )
            } else {
                // 加载中
                ProgressView()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
    }

    /// 根据练习类型显示对应的视图
    /// - Parameter exercise: 练习数据
    /// - Returns: 对应类型的视图
    private func exerciseTypeView(_ exercise: [String: Any]) -> some View {
        if let typeString = exercise["type"] as? String,
           let type = convertToExerciseType(typeString) {
            // 使用type字段确定练习类型
            switch type {
            case .multipleChoice, .singleChoice:
                return AnyView(MultipleChoiceView(exercise: exercise, selectedOption: $selectedOption))
            case .fillIn:
                return AnyView(FillInView(textInput: $textInput))
            case .speaking:
                return AnyView(DailyPracticeSpeakingView(
                    exercise: exercise,
                    isRecording: $isRecording,
                    recordingURL: $recordingURL,
                    isPlaying: $isPlaying,
                    toggleRecording: toggleRecording,
                    togglePlayback: togglePlayback
                ))
            case .listening:
                return AnyView(DailyPracticeListeningView(
                    exercise: exercise,
                    selectedOption: $selectedOption,
                    isPlaying: $isPlaying,
                    togglePlayback: togglePlayback
                ))
            case .reading:
                return AnyView(ReadingView(exercise: exercise, selectedOption: $selectedOption))
            case .writing:
                return AnyView(WritingView(textInput: $textInput))
            case .grammar:
                return AnyView(MultipleChoiceView(exercise: exercise, selectedOption: $selectedOption))
            case .matching:
                return AnyView(MatchingView(exercise: exercise, selectedOption: $selectedOption))
            case .ordering:
                return AnyView(OrderingView(exercise: exercise, selectedOption: $selectedOption))
            }
        } else if let category = exercise["category"] as? String {
            // 使用category字段作为类型
            return AnyView(categoryBasedView(exercise, category: category))
        } else if exercise["prompt"] != nil && exercise["targetPhrase"] != nil {
            // 口语练习
            return AnyView(DailyPracticeSpeakingView(
                exercise: exercise,
                isRecording: $isRecording,
                recordingURL: $recordingURL,
                isPlaying: $isPlaying,
                toggleRecording: toggleRecording,
                togglePlayback: togglePlayback
            ))
        } else if exercise["audioURL"] != nil && exercise["transcript"] != nil {
            // 听力练习
            return AnyView(DailyPracticeListeningView(
                exercise: exercise,
                selectedOption: $selectedOption,
                isPlaying: $isPlaying,
                togglePlayback: togglePlayback
            ))
        } else if let options = ExerciseDataProcessor.getExerciseOptions(from: exercise) {
            // 有选项，默认为选择题
            return AnyView(MultipleChoiceView(exercise: exercise, selectedOption: $selectedOption))
        } else {
            // 默认为填空题
            return AnyView(FillInView(textInput: $textInput))
        }
    }

    /// 基于分类显示对应的视图
    /// - Parameters:
    ///   - exercise: 练习数据
    ///   - category: 分类
    /// - Returns: 对应分类的视图
    private func categoryBasedView(_ exercise: [String: Any], category: String) -> some View {
        switch category.lowercased() {
        case "grammar", "vocabulary":
            // 语法和词汇练习通常是选择题
            return AnyView(MultipleChoiceView(exercise: exercise, selectedOption: $selectedOption))
        case "listening", "conversation":
            return AnyView(DailyPracticeListeningView(
                exercise: exercise,
                selectedOption: $selectedOption,
                isPlaying: $isPlaying,
                togglePlayback: togglePlayback
            ))
        case "speaking", "self introduction", "self-introduction", "pronunciation":
            return AnyView(DailyPracticeSpeakingView(
                exercise: exercise,
                isRecording: $isRecording,
                recordingURL: $recordingURL,
                isPlaying: $isPlaying,
                toggleRecording: toggleRecording,
                togglePlayback: togglePlayback
            ))
        case "reading":
            return AnyView(ReadingView(exercise: exercise, selectedOption: $selectedOption))
        case "writing":
            return AnyView(WritingView(textInput: $textInput))
        default:
            // 默认使用选择题视图
            return AnyView(MultipleChoiceView(exercise: exercise, selectedOption: $selectedOption))
        }
    }

    /// 提交按钮视图
    private var submitButtonView: some View {
        VStack {
            Spacer()

            Button(action: submitAnswer) {
                Text("提交答案")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(isAnswerValid() ? Color.blue : Color.gray)
                    .cornerRadius(10)
            }
            .disabled(!isAnswerValid())
            .padding()
        }
    }

    /// 反馈视图
    private var feedbackView: some View {
        Group {
            if let exercise = viewModel.currentExercise {
                let actualExercise = ExerciseDataProcessor.extractActualExercise(from: exercise)
                let explanation = actualExercise["explanation"] as? String

                FeedbackOverlayView(
                    isCorrect: isAnswerCorrect,
                    explanation: explanation,
                    showFeedback: $showFeedback
                )
            } else {
                FeedbackOverlayView(
                    isCorrect: isAnswerCorrect,
                    explanation: nil,
                    showFeedback: $showFeedback
                )
            }
        }
    }

    // MARK: - 生命周期方法

    /// 设置onAppear回调
    private func setupOnAppear() {
        print("PracticeSessionView appeared, current state: \(viewModel.state)")

        // 设置通知观察者，当新练习加载完成时重置视图状态
        setupNewExerciseObserver()

        // 设置通知观察者，当答案提交后显示反馈
        setupAnswerSubmittedObserver()

        // 只在特定状态下启动练习，避免循环调用
        handleInitialState()
    }

    /// 设置新练习加载通知观察者
    private func setupNewExerciseObserver() {
        newExerciseObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("NewExerciseLoaded"),
            object: nil,
            queue: .main
        ) { [self] _ in
            print("收到新练习加载通知，重置视图状态")
            // 重置状态
            DispatchQueue.main.async {
                selectedOption = nil
                textInput = ""
                recordingURL = nil
                isRecording = false
                isPlaying = false
            }
        }
    }

    /// 设置答案提交通知观察者
    private func setupAnswerSubmittedObserver() {
        answerSubmittedObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("AnswerSubmitted"),
            object: nil,
            queue: .main
        ) { [self] notification in
            print("收到答案提交通知")
            if let userInfo = notification.userInfo,
               let isCorrect = userInfo["isCorrect"] as? Bool {
                DispatchQueue.main.async {
                    isAnswerCorrect = isCorrect
                    withAnimation {
                        showFeedback = true
                    }
                }
            }
        }
    }

    /// 处理初始状态
    private func handleInitialState() {
        switch viewModel.state {
        case .intro, .loading:
            print("Starting practice from \(viewModel.state) state...")
            viewModel.startPractice()
        case .inProgress, .submitting, .completing, .completed:
            // 已经在进行中或已完成，不需要重新开始
            print("Practice already in progress or completed, no need to restart")

            // 检查当前练习是否有效
            if viewModel.currentExercise == nil {
                print("警告: 当前练习为空，尝试重新开始练习")
                viewModel.startPractice()
            } else if let exercise = viewModel.currentExercise,
                      let typeString = exercise["type"] as? String {
                print("当前练习类型: '\(typeString)'")
            } else if let exercise = viewModel.currentExercise {
                print("警告: 当前练习没有类型字段")
                print("练习数据: \(exercise)")
            }
        case .error:
            // 发生错误，可以尝试重新加载
            print("Error state detected, attempting to reload...")
            viewModel.loadTodayPractice()
        }
    }

    /// 清理onDisappear回调
    private func cleanupOnDisappear() {
        // 移除通知观察者
        if let observer = newExerciseObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        if let observer = answerSubmittedObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        stopAudio()
    }

    // MARK: - 辅助方法

    /// 切换录音状态
    private func toggleRecording() {
        isRecording.toggle()

        if isRecording {
            startRecording()
        } else {
            stopRecording()
        }
    }

    /// 开始录音
    private func startRecording() {
        if let url = AudioManager.shared.startRecording() {
            recordingURL = url
        }
    }

    /// 停止录音
    private func stopRecording() {
        AudioManager.shared.stopRecording()
    }

    /// 切换音频播放状态
    private func togglePlayback() {
        if isPlaying {
            stopAudio()
        } else {
            playAudio()
        }
    }

    /// 播放音频
    private func playAudio() {
        if let url = recordingURL {
            if AudioManager.shared.playAudio(from: url) {
                isPlaying = true
            }
        } else if let exercise = viewModel.currentExercise {
            let actualExercise = ExerciseDataProcessor.extractActualExercise(from: exercise)
            if let audioURLString = actualExercise["audioURL"] as? String,
               let audioURL = URL(string: audioURLString) {
                if AudioManager.shared.playAudio(from: audioURL) {
                    isPlaying = true
                }
            }
        }
    }

    /// 停止音频
    private func stopAudio() {
        AudioManager.shared.stopAudio()
        isPlaying = false
    }


    /// 将API返回的练习类型字符串转换为PracticeExerciseType枚举
    /// - Parameter typeString: API返回的类型字符串
    /// - Returns: 对应的PracticeExerciseType枚举值，如果无法转换则返回nil
    private func convertToExerciseType(_ typeString: String) -> PracticeExerciseType? {
        print("尝试转换练习类型: '\(typeString)'")

        // 直接尝试使用原始值初始化
        if let type = PracticeExerciseType(rawValue: typeString) {
            print("成功使用原始值初始化类型: \(type)")
            return type
        }

        // 处理可能的不同格式
        switch typeString.lowercased() {
        case "multiple-choice", "multiplechoice", "multiple_choice":
            print("转换为 multipleChoice 类型")
            return .multipleChoice
        case "single-choice", "singlechoice", "single_choice":
            print("转换为 singleChoice 类型")
            return .singleChoice
        case "fill-in", "fillin", "fill_in":
            print("转换为 fillIn 类型")
            return .fillIn
        case "speaking":
            print("转换为 speaking 类型")
            return .speaking
        case "grammar":
            print("转换为 grammar 类型")
            return .grammar
        case "listening":
            print("转换为 listening 类型")
            return .listening
        case "reading":
            print("转换为 reading 类型")
            return .reading
        case "writing":
            print("转换为 writing 类型")
            return .writing
        case "matching":
            print("转换为 matching 类型")
            return .matching
        case "ordering":
            print("转换为 ordering 类型")
            return .ordering
        default:
            print("未知练习类型: '\(typeString)'")
            print("有效的类型有: multiple-choice, single-choice, fill-in, speaking, listening, reading, writing, matching, ordering")
            return nil
        }
    }

    /// 检查答案是否有效
    /// - Returns: 是否有效
    private func isAnswerValid() -> Bool {
        guard let exercise = viewModel.currentExercise else { return false }

        // 检查是否有嵌套的数据结构
        let actualExercise: [String: Any] = { () -> [String: Any] in
            // 检查是否有嵌套的exercise字段 - 处理多种可能的嵌套结构
            if let data = exercise["data"] as? [String: Any] {
                if let nestedExercise = data["exercise"] as? [String: Any] {
                    // 数据结构: {"data": {"exercise": {...}}}
                    return nestedExercise
                } else if let exercises = data["exercises"] as? [[String: Any]], !exercises.isEmpty {
                    // 数据结构: {"data": {"exercises": [{...}]}}
                    if let firstExercise = exercises.first {
                        if let innerExercise = firstExercise["exercise"] as? [String: Any] {
                            // 数据结构: {"data": {"exercises": [{"exercise": {...}}]}}
                            var result = innerExercise
                            // 检查外层是否有type字段
                            if let typeString = firstExercise["type"] as? String {
                                result["type"] = typeString
                            }
                            return result
                        } else {
                            // 数据结构: {"data": {"exercises": [{...}]}}
                            return firstExercise
                        }
                    }
                }
                // 如果没有找到嵌套的exercise，返回data
                return data
            }
            // 如果没有data字段，返回原始exercise
            return exercise
        }()

        // 首先尝试使用type字段
        if let typeString = actualExercise["type"] as? String,
           let type = convertToExerciseType(typeString) {

            switch type {
            case .multipleChoice, .singleChoice, .listening, .reading, .matching, .ordering:
                return selectedOption != nil
            case .fillIn:
                return !textInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            case .speaking:
                return recordingURL != nil
            case .writing:
                return !textInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            case .grammar:
                return selectedOption != nil
            }
        }
        // 如果没有type字段，尝试使用category字段
        else if let category = actualExercise["category"] as? String {
            // 根据category确定答案类型
            switch category.lowercased() {
            case "grammar", "vocabulary", "listening", "reading", "conversation":
                // 这些类型通常是选择题
                return selectedOption != nil
            case "speaking", "self introduction", "self-introduction", "pronunciation":
                return recordingURL != nil
            case "writing":
                return !textInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            default:
                // 默认使用选择题
                return selectedOption != nil
            }
        }
        // 检查是否有prompt和targetPhrase字段，这通常表示这是一个口语练习
        else if actualExercise["prompt"] != nil && actualExercise["targetPhrase"] != nil {
            return recordingURL != nil
        }
        // 检查是否有audioURL和transcript字段，这通常表示这是一个听力练习
        else if actualExercise["audioURL"] != nil && actualExercise["transcript"] != nil {
            return selectedOption != nil
        }
        // 检查是否有questions数组，这通常表示这是一个选择题
        else if let questions = actualExercise["questions"] as? [[String: Any]], !questions.isEmpty {
            return selectedOption != nil
        }
        // 如果既没有type也没有category，尝试根据是否有选项来判断
        else if actualExercise["options"] != nil {
            // 如果有选项，可能是选择题
            return selectedOption != nil
        } else {
            // 默认使用文本输入
            return !textInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        }
    }

    /// 提交答案
    private func submitAnswer() {
        guard let exercise = viewModel.currentExercise else {
            print("错误: 当前没有练习")
            return
        }

        // 检查是否有嵌套的数据结构
        let actualExercise: [String: Any] = { () -> [String: Any] in
            // 检查是否有嵌套的exercise字段 - 处理多种可能的嵌套结构
            if let data = exercise["data"] as? [String: Any] {
                if let nestedExercise = data["exercise"] as? [String: Any] {
                    // 数据结构: {"data": {"exercise": {...}}}
                    return nestedExercise
                } else if let exercises = data["exercises"] as? [[String: Any]], !exercises.isEmpty {
                    // 数据结构: {"data": {"exercises": [{...}]}}
                    if let firstExercise = exercises.first {
                        if let innerExercise = firstExercise["exercise"] as? [String: Any] {
                            // 数据结构: {"data": {"exercises": [{"exercise": {...}}]}}
                            var result = innerExercise
                            // 检查外层是否有type字段
                            if let typeString = firstExercise["type"] as? String {
                                result["type"] = typeString
                            }
                            return result
                        } else {
                            // 数据结构: {"data": {"exercises": [{...}]}}
                            return firstExercise
                        }
                    }
                }
                // 如果没有找到嵌套的exercise，返回data
                return data
            }
            // 如果没有data字段，返回原始exercise
            return exercise
        }()

        print("提取的练习数据: \(actualExercise)")

        // 确定练习类型
        let exerciseType: PracticeExerciseType? = {
            // 首先尝试使用type字段
            if let typeString = actualExercise["type"] as? String,
               let type = convertToExerciseType(typeString) {
                print("使用type字段确定练习类型: \(typeString)")
                return type
            }

            // 如果没有type字段，尝试使用category字段
            if let category = actualExercise["category"] as? String {
                print("使用category字段确定练习类型: \(category)")
                switch category.lowercased() {
                case "grammar", "vocabulary":
                    return .multipleChoice
                case "listening", "conversation":
                    return .listening
                case "speaking", "self introduction", "self-introduction", "pronunciation":
                    return .speaking
                case "reading":
                    return .reading
                case "writing":
                    return .writing
                default:
                    return .multipleChoice
                }
            }

            // 检查是否有prompt和targetPhrase字段，这通常表示这是一个口语练习
            if actualExercise["prompt"] != nil && actualExercise["targetPhrase"] != nil {
                print("根据prompt和targetPhrase字段判断为口语练习")
                return .speaking
            }

            // 检查是否有audioURL和transcript字段，这通常表示这是一个听力练习
            if actualExercise["audioURL"] != nil && actualExercise["transcript"] != nil {
                print("根据audioURL和transcript字段判断为听力练习")
                return .listening
            }

            // 检查是否有questions数组，这通常表示这是一个选择题
            if let questions = actualExercise["questions"] as? [[String: Any]], !questions.isEmpty {
                print("根据questions数组判断为选择题")
                return .multipleChoice
            }

            // 如果既没有type也没有category，尝试根据是否有选项来判断
            if actualExercise["options"] != nil {
                print("根据options字段判断为选择题")
                return .multipleChoice
            }

            print("无法确定练习类型，使用默认类型")
            return nil
        }()

        print("确定的练习类型: \(exerciseType?.rawValue ?? "未知")")

        // 根据练习类型获取答案
        var answer = ""
        var answerIndex: Int? = nil

        if let type = exerciseType {
            switch type {
            case .multipleChoice, .singleChoice, .listening, .reading, .matching, .ordering, .grammar:
                if let selectedOption = selectedOption {
                    answer = selectedOption

                    // 尝试找到选项的索引
                    if let questions = actualExercise["questions"] as? [[String: Any]], !questions.isEmpty,
                       let firstQuestion = questions.first,
                       let options = firstQuestion["options"] as? [String],
                       let index = options.firstIndex(of: selectedOption) {
                        answerIndex = index
                        print("找到选项索引: \(index)")
                    } else if let options = actualExercise["options"] as? [String],
                              let index = options.firstIndex(of: selectedOption) {
                        answerIndex = index
                        print("找到选项索引: \(index)")
                    }
                } else {
                    print("警告: 选择题没有选择选项")
                }

            case .fillIn, .writing:
                answer = textInput
                if answer.isEmpty {
                    print("警告: 填空题答案为空")
                }

            case .speaking:
                if let url = recordingURL {
                    answer = url.absoluteString
                    print("口语练习答案，录音文件: \(url.absoluteString)")
                } else {
                    print("警告: 口语练习没有录音文件")
                }
            }
        } else {
            // 如果无法确定类型，尝试使用可用的答案
            if let selectedOption = selectedOption {
                answer = selectedOption
                print("使用选择的选项作为答案: \(selectedOption)")
            } else if !textInput.isEmpty {
                answer = textInput
                print("使用文本输入作为答案: \(textInput)")
            } else if let url = recordingURL {
                answer = url.absoluteString
                print("使用录音文件作为答案: \(url.absoluteString)")
            } else {
                print("警告: 无法确定答案类型，且没有有效的答案")
            }
        }

        // 如果找到了选项索引，使用索引作为答案
        if let index = answerIndex {
            answer = String(index)
            print("使用选项索引作为最终答案: \(index)")
        }

        // 检查答案是否为空
        if answer.isEmpty {
            print("错误: 答案为空，无法提交")
            return
        }

        print("准备提交答案: \(answer)")

        // 如果有lessonID，使用它作为exerciseId，但先验证它是有效的UUID
        if let data = exercise["data"] as? [String: Any],
           let lessonID = data["lessonID"] as? String,
           UUID(uuidString: lessonID) != nil {
            print("使用lessonID作为exerciseId: \(lessonID)")
            viewModel.submitAnswer(answer: answer, exerciseId: lessonID)
        } else {
            // 如果lessonID不是有效的UUID，或者没有lessonID，使用默认方法
            print("使用默认方法提交答案")
            viewModel.submitAnswer(answer: answer)
        }

        print("答案提交完成，等待API响应...")

        // 不立即重置状态，等待新练习加载后再重置
        // 状态重置将在接收到 NewExerciseLoaded 通知时进行
    }
}

struct PracticeSessionViewPracticeSessionView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            PracticeSessionView(viewModel: PersonalizedPracticeViewModel())
        }
    }
}
