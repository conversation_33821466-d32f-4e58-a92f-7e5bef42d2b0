import Foundation

/// 每日练习统计数据模型
public struct DailyPracticeStats: Codable, Identifiable {
    public let id = UUID()
    public let date: Date
    public let minutesLearned: Int
    public let practicesCompleted: Int
    public let totalScore: Int
    public let averageScore: Int
    public let practicesByType: [String: Int]
    public let streak: Int
    
    /// 编码键
    private enum CodingKeys: String, CodingKey {
        case date
        case minutesLearned = "minutes_learned"
        case practicesCompleted = "practices_completed"
        case totalScore = "total_score"
        case averageScore = "average_score"
        case practicesByType = "practices_by_type"
        case streak
    }
    
    /// 初始化方法
    /// - Parameters:
    ///   - date: 日期
    ///   - minutesLearned: 学习分钟数
    ///   - practicesCompleted: 完成练习数
    ///   - totalScore: 总分
    ///   - averageScore: 平均分
    ///   - practicesByType: 按类型分组的练习数
    ///   - streak: 连续天数
    public init(
        date: Date,
        minutesLearned: Int,
        practicesCompleted: Int,
        totalScore: Int,
        averageScore: Int,
        practicesByType: [String: Int],
        streak: Int
    ) {
        self.date = date
        self.minutesLearned = minutesLearned
        self.practicesCompleted = practicesCompleted
        self.totalScore = totalScore
        self.averageScore = averageScore
        self.practicesByType = practicesByType
        self.streak = streak
    }
}

/// 每周练习统计数据模型
public struct WeeklyPracticeStats: Codable, Identifiable {
    public let id = UUID()
    public let weekStartDate: Date
    public let weekEndDate: Date
    public let totalMinutes: Int
    public let totalPractices: Int
    public let averageScore: Int
    public let dailyStats: [DailyPracticeStats]
    public let practicesByType: [String: Int]
    public let bestDay: Date?
    public let worstDay: Date?
    
    /// 编码键
    private enum CodingKeys: String, CodingKey {
        case weekStartDate = "week_start_date"
        case weekEndDate = "week_end_date"
        case totalMinutes = "total_minutes"
        case totalPractices = "total_practices"
        case averageScore = "average_score"
        case dailyStats = "daily_stats"
        case practicesByType = "practices_by_type"
        case bestDay = "best_day"
        case worstDay = "worst_day"
    }
    
    /// 初始化方法
    /// - Parameters:
    ///   - weekStartDate: 周开始日期
    ///   - weekEndDate: 周结束日期
    ///   - totalMinutes: 总学习分钟数
    ///   - totalPractices: 总练习数
    ///   - averageScore: 平均分
    ///   - dailyStats: 每日统计
    ///   - practicesByType: 按类型分组的练习数
    ///   - bestDay: 最佳学习日
    ///   - worstDay: 最差学习日
    public init(
        weekStartDate: Date,
        weekEndDate: Date,
        totalMinutes: Int,
        totalPractices: Int,
        averageScore: Int,
        dailyStats: [DailyPracticeStats],
        practicesByType: [String: Int],
        bestDay: Date?,
        worstDay: Date?
    ) {
        self.weekStartDate = weekStartDate
        self.weekEndDate = weekEndDate
        self.totalMinutes = totalMinutes
        self.totalPractices = totalPractices
        self.averageScore = averageScore
        self.dailyStats = dailyStats
        self.practicesByType = practicesByType
        self.bestDay = bestDay
        self.worstDay = worstDay
    }
}

/// 练习统计响应模型
public struct PracticeStatsResponse: Codable {
    public let success: Bool
    public let data: DailyPracticeStats
    public let message: String?
    
    /// 编码键
    private enum CodingKeys: String, CodingKey {
        case success
        case data
        case message
    }
}

/// 周练习统计响应模型
public struct WeeklyPracticeStatsResponse: Codable {
    public let success: Bool
    public let data: WeeklyPracticeStats
    public let message: String?
    
    /// 编码键
    private enum CodingKeys: String, CodingKey {
        case success
        case data
        case message
    }
}

// MARK: - 扩展方法

extension DailyPracticeStats {
    /// 是否达到每日目标
    /// - Parameters:
    ///   - goalMinutes: 目标分钟数
    ///   - goalPractices: 目标练习数
    /// - Returns: 是否达到目标
    public func hasMetGoals(goalMinutes: Int, goalPractices: Int) -> Bool {
        return minutesLearned >= goalMinutes && practicesCompleted >= goalPractices
    }
    
    /// 获取学习进度百分比
    /// - Parameters:
    ///   - goalMinutes: 目标分钟数
    ///   - goalPractices: 目标练习数
    /// - Returns: 进度百分比 (0.0 - 1.0)
    public func getProgress(goalMinutes: Int, goalPractices: Int) -> Double {
        let minutesProgress = Double(minutesLearned) / Double(goalMinutes)
        let practicesProgress = Double(practicesCompleted) / Double(goalPractices)
        return min((minutesProgress + practicesProgress) / 2.0, 1.0)
    }
}

extension WeeklyPracticeStats {
    /// 获取平均每日学习时间
    /// - Returns: 平均每日学习分钟数
    public func getAverageDailyMinutes() -> Double {
        let activeDays = dailyStats.filter { $0.minutesLearned > 0 }.count
        guard activeDays > 0 else { return 0.0 }
        return Double(totalMinutes) / Double(activeDays)
    }
    
    /// 获取学习活跃天数
    /// - Returns: 活跃天数
    public func getActiveDays() -> Int {
        return dailyStats.filter { $0.minutesLearned > 0 }.count
    }
    
    /// 获取最佳练习类型
    /// - Returns: 最佳练习类型
    public func getBestPracticeType() -> String? {
        return practicesByType.max(by: { $0.value < $1.value })?.key
    }
}
