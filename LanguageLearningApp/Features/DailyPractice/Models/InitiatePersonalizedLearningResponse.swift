import Foundation

/// 启动个性化学习响应模型
struct InitiatePersonalizedLearningResponse: Codable {
    /// 是否成功
    let success: Bool
    /// 消息
    let message: String?
    /// 数据
    let data: InitiatePersonalizedLearningData?
    /// 错误信息
    let error: String?
}

/// 启动个性化学习数据
struct InitiatePersonalizedLearningData: Codable {
    /// 评估ID
    let id: String
    /// 用户ID
    let userId: String
    /// 评估类型
    let type: String?
    /// 评估标题
    let title: String?
    /// 评估描述
    let description: String?
    /// 总问题数
    let totalQuestions: Int?
    /// 评估时长（分钟）
    let duration: Int?
    /// 创建时间
    let createdAt: Date?
    /// 更新时间
    let updatedAt: Date?
    /// 开始时间
    let startedAt: Date?
    /// 完成时间
    let completedAt: Date?
    /// 是否已完成
    let isCompleted: Bool?
    /// 及格分数
    let passingScore: Int?
    /// 评估部分（API响应中的原始数据）
    let sections: [APIEvaluationSection]?

    /// 评估ID（兼容旧版API）
    var evaluationId: String {
        return id
    }

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "userId"
        case type
        case title
        case description
        case totalQuestions
        case duration
        case createdAt = "createdAt"
        case updatedAt = "updatedAt"
        case startedAt = "startedAt"
        case completedAt = "completedAt"
        case isCompleted
        case passingScore
        case sections
    }
}

/// API响应中的评估部分（与应用中的EvaluationSection区分）
struct APIEvaluationSection: Codable {
    /// 部分ID
    let sectionId: Int?
    /// 评估ID
    let evaluationId: String?
    /// 技能
    let skill: String?
    /// 标题
    let title: String?
    /// 权重
    let weight: Int?
    /// 问题列表
    let questions: [APIEvaluationQuestion]?

    enum CodingKeys: String, CodingKey {
        case sectionId = "sectionId"
        case evaluationId = "evaluationId"
        case skill
        case title
        case weight
        case questions
    }
}

/// API响应中的评估问题（与应用中的EvaluationQuestion区分）
struct APIEvaluationQuestion: Codable {
    /// 问题内容
    let content: String?
    /// 问题类型
    let type: String?
    /// 部分ID
    let sectionId: Int?
    /// 分数
    let points: Int?
    /// 选项（多选题）
    let options: [String]?
    /// 正确答案
    let correctAnswer: String?

    enum CodingKeys: String, CodingKey {
        case content
        case type
        case sectionId = "sectionId"
        case points
        case options
        case correctAnswer = "correctAnswer"
    }
}
