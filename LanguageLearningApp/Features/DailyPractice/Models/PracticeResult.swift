import Foundation

/// 练习结果模型
struct PracticeResult: Identifiable, Codable {
    /// 结果ID
    let id: UUID
    /// 练习ID
    let practiceID: UUID
    /// 用户ID
    let userID: UUID
    /// 练习类型
    let practiceType: DailyPracticeType
    /// 总分
    let score: Int
    /// 总分值
    let totalPoints: Int
    /// 正确题目数
    let correctCount: Int
    /// 总题目数
    let totalCount: Int
    /// 完成时间
    let completedAt: Date
    /// 用时（秒）
    let duration: Int
    /// 反馈
    let feedback: String?
    /// 建议
    let recommendations: [String]?
    /// 创建时间
    let createdAt: Date
    
    /// 计算正确率
    var correctPercentage: Double {
        guard totalCount > 0 else { return 0 }
        return Double(correctCount) / Double(totalCount) * 100.0
    }
    
    /// 计算得分率
    var scorePercentage: Double {
        guard totalPoints > 0 else { return 0 }
        return Double(score) / Double(totalPoints) * 100.0
    }
    
    /// 计算表现级别
    var performanceLevel: PerformanceLevel {
        let percentage = scorePercentage
        
        if percentage >= 90 {
            return .excellent
        } else if percentage >= 75 {
            return .good
        } else if percentage >= 60 {
            return .satisfactory
        } else {
            return .needsImprovement
        }
    }
}

/// 练习结果响应
struct PracticeResultResponse: Codable {
    let data: PracticeResult
}

/// 练习历史响应
struct PracticeHistoryResponse: Codable {
    let data: [PracticeResult]
}
