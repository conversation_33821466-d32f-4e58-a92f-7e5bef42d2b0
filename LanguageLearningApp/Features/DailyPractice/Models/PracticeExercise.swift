import Foundation

/// 练习题目类型
enum PracticeExerciseType: String, Codable {
    /// 多选题
    case multipleChoice = "multiple-choice"
    /// 单选题
    case singleChoice = "single-choice"
    /// 填空题
    case fillIn = "fill-in"
    /// 口语题
    case speaking = "speaking"
    /// 听力题
    case listening = "listening"
    /// 阅读题
    case reading = "reading"
    /// 写作题
    case writing = "writing"
    /// 语法题
    case grammar = "grammar"
    /// 配对题
    case matching = "matching"
    /// 排序题
    case ordering = "ordering"
}

/// 练习题目模型
struct PracticeExercise: Identifiable, Codable {
    /// 题目ID
    let id: UUID
    /// 练习ID
    let practiceID: UUID
    /// 题目类型
    let type: PracticeExerciseType
    /// 题目内容
    let content: String
    /// 题目提示
    let hint: String?
    /// 选项（用于选择题）
    let options: [String]?
    /// 正确答案
    let correctAnswer: String
    /// 用户答案
    var userAnswer: String?
    /// 是否正确
    var isCorrect: Bool?
    /// 分值
    let points: Int
    /// 解释
    let explanation: String?
    /// 音频URL（用于听力题）
    let audioURL: URL?
    /// 图片URL
    let imageURL: URL?
    /// 是否已完成
    var isCompleted: Bool
    /// 完成时间
    var completedAt: Date?
    
    /// 检查答案是否正确
    mutating func checkAnswer() {
        guard let userAnswer = userAnswer else {
            isCorrect = false
            return
        }
        
        switch type {
        case .multipleChoice, .singleChoice, .fillIn, .matching, .ordering:
            isCorrect = userAnswer == correctAnswer
        case .speaking, .grammar:
            // 口语题可能需要更复杂的评分逻辑
            isCorrect = true // 简化处理，实际应用中可能需要语音识别或人工评分
        case .listening:
            isCorrect = userAnswer == correctAnswer
        case .reading:
            isCorrect = userAnswer == correctAnswer
        case .writing:
            // 写作题可能需要更复杂的评分逻辑
            isCorrect = true // 简化处理，实际应用中可能需要自然语言处理或人工评分
        }
    }
}

/// 提交练习答案请求
struct SubmitPracticeAnswerRequest: Codable {
    /// 练习ID
    let practiceID: UUID
    /// 题目ID
    let exerciseID: UUID
    /// 用户答案
    let answer: String
}

/// 提交练习答案响应
struct SubmitPracticeAnswerResponse: Codable {
    /// 是否正确
    let isCorrect: Bool
    /// 解释
    let explanation: String?
}
