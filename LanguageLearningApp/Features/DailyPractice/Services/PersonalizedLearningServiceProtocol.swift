import Foundation
import Combine

/// 个性化学习服务协议，定义与个性化学习相关的API调用接口
protocol PersonalizedLearningServiceProtocol {
    /// 是否有网络连接
    var isNetworkAvailable: Bool { get }
    
    /// 启动个性化学习过程
    /// - Returns: 评估ID
    func initiatePersonalizedLearning() async throws -> UUID
    
    /// 获取个性化学习状态
    /// - Returns: 个性化学习状态
    func getPersonalizedLearningStatus() async throws -> Any
    
    /// 获取当前学习路径
    /// - Returns: 个性化学习路径
    func getCurrentLearningPath() async throws -> PersonalizedLearningPath
    
    /// 获取下一个练习
    /// - Parameter pathId: 学习路径ID
    /// - Returns: 每日练习
    func getNextExercise(pathId: UUID) async throws -> Any
    
    /// 完成练习
    /// - Parameters:
    ///   - pathId: 学习路径ID
    ///   - lessonId: 课程ID
    /// - Returns: 成功状态
    func completeExercise(pathId: UUID, lessonId: String) async throws -> Bool
    
    /// 保存练习会话
    /// - Parameters:
    ///   - type: 练习类型
    ///   - duration: 持续时间（秒）
    ///   - score: 得分
    /// - Returns: 成功状态
    func savePracticeSession(type: String, duration: Int, score: Int) async throws -> Bool
    
    /// 获取练习历史
    /// - Returns: 练习历史
    func getPracticeHistory() async throws -> [Any]
    
    /// 提交练习答案
    /// - Parameters:
    ///   - practiceId: 练习ID
    ///   - exerciseId: 题目ID
    ///   - answer: 用户答案
    /// - Returns: 提交结果
    func submitPracticeAnswer(practiceId: UUID, exerciseId: UUID, answer: String) async throws -> [String: Any]
    
    /// 重置学习路径
    /// - Returns: 成功状态
    func resetLearningPath() async throws -> Bool
}
