import Foundation
import Combine
import SwiftUI

/// 练习管理器，处理练习相关的业务逻辑
public class PracticeManager: ObservableObject, PracticeManagerProtocol {
    // 状态枚举
    public enum State {
        case idle
        case loading
        case loaded([PracticeSession])
        case saving
        case saved(PracticeSession)
        case error(Error)
    }

    // 当前状态
    @Published public var state: State = .idle

    // 错误信息
    @Published public var errorMessage: String?

    // 是否正在加载
    @Published public var isLoading: Bool = false

    // 是否正在保存
    @Published public var isSaving: Bool = false

    // 最近的练习
    @Published public var recentPractices: [PracticeSession] = []

    // 每日连续学习天数
    @Published public var dailyStreak: Int = 0

    // 总练习时间
    @Published public var totalPracticeTime: TimeInterval = 0

    // 平均分数
    @Published public var averageScore: Double = 0

    // 仓库
    private let repository: any PracticeRepositoryProtocol

    // 当前用户ID
    private var currentUserID: UUID?

    // 取消标记
    private var cancellables = Set<AnyCancellable>()

    // 单例实例
    public static let shared = PracticeManager(repository: PracticeRepository.shared)

    /// 初始化方法
    /// - Parameter repository: 练习仓库
    public init(repository: any PracticeRepositoryProtocol) {
        self.repository = repository
    }

    /// 设置当前用户
    /// - Parameter userID: 用户ID
    public func setCurrentUser(userID: UUID) {
        self.currentUserID = userID
        loadPracticeHistory()
    }

    /// 添加练习
    /// - Parameter practice: 练习会话
    public func addPractice(_ practice: PracticeSession) {
        recentPractices.insert(practice, at: 0)
        updateStatistics()
    }

    /// 更新练习
    /// - Parameter practice: 练习会话
    public func updatePractice(_ practice: PracticeSession) {
        if let index = recentPractices.firstIndex(where: { $0.id == practice.id }) {
            recentPractices[index] = practice
            updateStatistics()
        }
    }

    /// 删除练习
    /// - Parameter practice: 练习会话
    public func deletePractice(_ practice: PracticeSession) {
        recentPractices.removeAll { $0.id == practice.id }
        updateStatistics()
    }

    /// 更新统计数据
    public func updateStatistics() {
        // 计算总练习时间
        totalPracticeTime = recentPractices.reduce(0) { $0 + $1.duration }

        // 计算平均分数
        if !recentPractices.isEmpty {
            let totalScore = recentPractices.reduce(0) { $0 + $1.score }
            averageScore = Double(totalScore) / Double(recentPractices.count)
        }

        // 计算连续天数
        dailyStreak = calculateStreak()
    }

    /// 获取推荐练习
    /// - Returns: 推荐练习数组
    public func getRecommendedPractice() -> [PracticeSession] {
        // 这里可以根据用户的学习进度和偏好返回推荐练习
        // 目前简单返回最近的练习
        return Array(recentPractices.prefix(5))
    }

    /// 从API加载练习历史
    /// - Parameter completion: 完成回调
    public func loadPracticeHistoryFromAPI(completion: (() -> Void)?) async {
        guard let userID = currentUserID else {
            errorMessage = "未设置当前用户"
            completion?()
            return
        }

        do {
            let sessions = try await repository.getPracticeHistoryAsync(userID: userID)
            await MainActor.run {
                recentPractices = sessions
                updateStatistics()
            }
        } catch {
            await MainActor.run {
                errorMessage = error.localizedDescription
            }
        }

        completion?()
    }

    /// 从API加载推荐练习
    /// - Returns: 推荐练习数组
    public func loadRecommendedPracticeFromAPI() async -> [PracticeSession] {
        // 这里可以实现从API获取推荐练习的逻辑
        // 目前返回最近的练习
        return Array(recentPractices.prefix(5))
    }

    /// 保存练习会话到API
    /// - Parameters:
    ///   - type: 练习类型
    ///   - duration: 持续时间（秒）
    ///   - score: 分数
    public func savePracticeSessionToAPI(type: PracticeSession.PracticeType, duration: TimeInterval, score: Int) async {
        guard let userID = currentUserID else {
            errorMessage = "未设置当前用户"
            return
        }

        let session = PracticeSession(
            userID: userID,
            type: type,
            startTime: Date().addingTimeInterval(-duration),
            endTime: Date(),
            duration: duration,
            score: score,
            completed: true,
            isSynced: false
        )

        do {
            let savedSession = try await repository.saveAsync(session)
            await MainActor.run {
                addPractice(savedSession)
            }
        } catch {
            await MainActor.run {
                errorMessage = error.localizedDescription
            }
        }
    }

    /// 加载用户的练习历史
    public func loadPracticeHistory() {
        guard let userID = currentUserID else {
            state = .error(NSError(domain: "PracticeManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"]))
            errorMessage = "未设置当前用户"
            return
        }

        state = .loading
        isLoading = true
        errorMessage = nil

        repository.getPracticeHistory(userID: userID)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                guard let self = self else { return }

                self.isLoading = false

                if case .failure(let error) = completion {
                    self.state = .error(error)
                    self.errorMessage = error.localizedDescription
                }
            } receiveValue: { [weak self] sessions in
                guard let self = self else { return }

                self.state = .loaded(sessions)
            }
            .store(in: &cancellables)
    }

    /// 保存练习会话
    /// - Parameters:
    ///   - type: 练习类型
    ///   - durationMinutes: 持续时间（分钟）
    ///   - score: 得分
    public func savePracticeSession(type: PracticeSession.PracticeType, duration: TimeInterval, score: Int) {
        guard let userID = currentUserID else {
            state = .error(NSError(domain: "PracticeManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"]))
            errorMessage = "未设置当前用户"
            return
        }

        state = .saving
        isSaving = true
        errorMessage = nil

        // 创建新的练习会话
        let session = PracticeSession(
            userID: userID,
            type: type,
            startTime: Date(),
            endTime: Date().addingTimeInterval(duration),
            duration: duration,
            score: score,
            completed: true,
            isSynced: false
        )

        repository.save(session)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                guard let self = self else { return }

                self.isSaving = false

                if case .failure(let error) = completion {
                    self.state = .error(error)
                    self.errorMessage = error.localizedDescription
                }
            } receiveValue: { [weak self] savedSession in
                guard let self = self else { return }

                self.state = .saved(savedSession)

                // 触发同步
                self.syncUnsyncedSessions()
            }
            .store(in: &cancellables)
    }

    /// 同步未同步的练习会话
    public func syncUnsyncedSessions() {
        repository.syncUnsyncedSessions()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.errorMessage = "同步失败: \(error.localizedDescription)"
                }
            } receiveValue: { _ in
                // 同步成功，什么都不做
            }
            .store(in: &cancellables)
    }

    /// 清除本地缓存
    public func clearLocalCache() {
        repository.clearLocalCache()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.errorMessage = "清除缓存失败: \(error.localizedDescription)"
                }
            } receiveValue: { [weak self] success in
                if success {
                    self?.state = .idle
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 异步方法

    /// 异步加载用户的练习历史
    /// - Returns: 是否成功
    @MainActor
    public func loadPracticeHistoryAsync() async -> Bool {
        guard let userID = currentUserID else {
            state = .error(NSError(domain: "PracticeManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"]))
            errorMessage = "未设置当前用户"
            return false
        }

        state = .loading
        isLoading = true
        errorMessage = nil

        do {
            let sessions = try await repository.getPracticeHistoryAsync(userID: userID)
            state = .loaded(sessions)
            isLoading = false
            return true
        } catch {
            state = .error(error)
            errorMessage = error.localizedDescription
            isLoading = false
            return false
        }
    }

    /// 异步保存练习会话
    /// - Parameters:
    ///   - type: 练习类型
    ///   - duration: 持续时间（秒）
    ///   - score: 得分
    /// - Returns: 是否成功
    @MainActor
    public func savePracticeSessionAsync(type: PracticeSession.PracticeType, duration: TimeInterval, score: Int) async -> Bool {
        guard let userID = currentUserID else {
            state = .error(NSError(domain: "PracticeManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"]))
            errorMessage = "未设置当前用户"
            return false
        }

        state = .saving
        isSaving = true
        errorMessage = nil

        // 创建新的练习会话
        let session = PracticeSession(
            userID: userID,
            type: type,
            startTime: Date(),
            endTime: Date().addingTimeInterval(duration),
            duration: duration,
            score: score,
            completed: true,
            isSynced: false
        )

        do {
            let savedSession = try await repository.saveAsync(session)
            state = .saved(savedSession)
            isSaving = false

            // 触发同步
            Task {
                await syncUnsyncedSessionsAsync()
            }

            return true
        } catch {
            state = .error(error)
            errorMessage = error.localizedDescription
            isSaving = false
            return false
        }
    }

    /// 异步同步未同步的练习会话
    /// - Returns: 是否成功
    public func syncUnsyncedSessionsAsync() async -> Bool {
        do {
            return try await repository.syncUnsyncedSessionsAsync()
        } catch {
            await MainActor.run {
                errorMessage = "同步失败: \(error.localizedDescription)"
            }
            return false
        }
    }

    /// 异步清除本地缓存
    /// - Returns: 是否成功
    @MainActor
    public func clearLocalCacheAsync() async -> Bool {
        do {
            let success = try await repository.clearLocalCacheAsync()
            if success {
                state = .idle
            }
            return success
        } catch {
            errorMessage = "清除缓存失败: \(error.localizedDescription)"
            return false
        }
    }

    /// 获取总练习时长（分钟）
    /// - Parameter sessions: 练习会话列表
    /// - Returns: 总时长
    public func getTotalPracticeTime(from sessions: [PracticeSession]) -> Int {
        return sessions.reduce(into: 0) { result, session in
            result += Int(session.duration / 60)
        }
    }

    /// 获取平均得分
    /// - Parameter sessions: 练习会话列表
    /// - Returns: 平均得分
    public func getAverageScore(from sessions: [PracticeSession]) -> Int {
        guard !sessions.isEmpty else { return 0 }

        let totalScore = sessions.reduce(0) { $0 + $1.score }
        return totalScore / sessions.count
    }

    /// 按类型统计练习次数
    /// - Parameter sessions: 练习会话列表
    /// - Returns: 类型统计字典
    public func getPracticeCountByType(from sessions: [PracticeSession]) -> [PracticeSession.PracticeType: Int] {
        var countByType: [PracticeSession.PracticeType: Int] = [:]

        for session in sessions {
            countByType[session.type, default: 0] += 1
        }

        return countByType
    }

    /// 计算学习连续天数
    /// - Parameter sessions: 练习会话列表
    /// - Returns: 连续天数
    public func calculateLearningStreak(from sessions: [PracticeSession]) -> Int {
        guard !sessions.isEmpty else { return 0 }

        // 将会话按日期分组
        let calendar = Calendar.current
        var sessionsByDate: [Date: [PracticeSession]] = [:]

        for session in sessions {
            let date = calendar.startOfDay(for: session.endTime)
            if sessionsByDate[date] == nil {
                sessionsByDate[date] = [session]
            } else {
                sessionsByDate[date]?.append(session)
            }
        }

        // 获取不重复的日期并排序
        let dates = sessionsByDate.keys.sorted(by: >)
        guard !dates.isEmpty else { return 0 }

        // 计算连续天数
        var streak = 1
        let today = calendar.startOfDay(for: Date())

        // 如果今天没有学习，检查昨天
        if !sessionsByDate.keys.contains(today) {
            // 如果昨天也没有学习，连续天数为0
            let yesterday = calendar.date(byAdding: .day, value: -1, to: today)!
            if !sessionsByDate.keys.contains(yesterday) {
                return 0
            }
        }

        // 从最近的日期开始检查
        for i in 0..<(dates.count - 1) {
            let current = dates[i]
            let next = dates[i + 1]

            // 计算两个日期之间的天数差
            let days = calendar.dateComponents([.day], from: next, to: current).day ?? 0

            if days == 1 {
                // 连续的
                streak += 1
            } else {
                // 不连续，中断
                break
            }
        }

        return streak
    }

    private func updateSessionProgress(_ session: PracticeSession, progress: Double) {
        // 由于PracticeSession是不可变的，我们需要创建一个新的会话
        let updatedSession = PracticeSession(
            id: session.id,
            userID: session.userID,
            type: session.type,
            startTime: session.startTime,
            endTime: progress >= 1.0 ? Date() : session.endTime,
            duration: session.duration,
            score: session.score,
            completed: progress >= 1.0,
            isSynced: session.isSynced
        )

        // 保存更新后的会话
        Task {
            _ = try? await repository.saveAsync(updatedSession)
        }
    }

    // MARK: - Private Methods

    /// 计算连续学习天数
    private func calculateStreak() -> Int {
        guard !recentPractices.isEmpty else { return 0 }

        let calendar = Calendar.current
        var currentStreak = 0
        var lastDate: Date?

        // 按日期排序
        let sortedSessions = recentPractices.sorted { $0.endTime > $1.endTime }

        for session in sortedSessions {
            let sessionDate = calendar.startOfDay(for: session.endTime)

            if let last = lastDate {
                let daysBetween = calendar.dateComponents([.day], from: sessionDate, to: last).day ?? 0

                if daysBetween == 1 {
                    currentStreak += 1
                } else if daysBetween > 1 {
                    break
                }
            } else {
                currentStreak = 1
            }

            lastDate = sessionDate
        }

        return currentStreak
    }
}