import SwiftUI

/// 评估介绍视图
struct EvaluationIntroView: View {
    @ObservedObject var viewModel: EvaluationViewModel
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.presentationMode) var presentationMode
    @State private var navigateToEvaluation = false

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 标题
                Text("语言水平评估")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(Color.primary)
                    .padding(.top)

                // 评估信息
                if let evaluation = viewModel.evaluation {
                    // 评估标题
                    Text(evaluation.title)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.primary)

                    // 评估描述
                    Text(evaluation.description)
                        .font(.body)
                        .foregroundColor(Color.secondary)
                        .padding(.vertical, 5)

                    // 评估详情
                    evaluationDetailsView(evaluation)

                    // 评估说明
                    evaluationInstructionsView()
                } else {
                    // 加载中
                    ProgressView()
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                }

                Spacer()

                // 开始按钮
                Button(action: {
                    viewModel.startEvaluation()
                    navigateToEvaluation = true
                }) {
                    Text("开始评估")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(10)
                }
                .disabled(viewModel.evaluation == nil)
                .padding(.bottom)
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(colorScheme == .dark ? Color.black : Color.white)
            // Add navigationDestination for EvaluationView
            .navigationDestination(isPresented: $navigateToEvaluation) {
                // Ensure evaluation and its ID are available before navigating
                if let evalId = viewModel.evaluation?.id {
                    EvaluationView(evaluationId: evalId)
                } else {
                    // Fallback or error view if evaluationId is somehow nil
                    Text("错误：无法加载评估。")
                }
            }
        }
        .navigationTitle("语言评估")
        .navigationBarTitleDisplayMode(.inline)
    }

    /// 评估详情视图
    /// - Parameter evaluation: 评估模型
    /// - Returns: 详情视图
    private func evaluationDetailsView(_ evaluation: Evaluation) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            // 详情卡片
            VStack(alignment: .leading, spacing: 12) {
                // 目标语言
                HStack {
                    Image(systemName: "globe")
                        .foregroundColor(.blue)
                    Text("目标语言：中文")
                        .font(.subheadline)
                }

                // 估计时间
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.blue)
                    Text("估计时间：\(evaluation.duration) 分钟")
                        .font(.subheadline)
                }

                // 难度级别
                HStack {
                    Image(systemName: "chart.bar")
                        .foregroundColor(.blue)
                    Text("难度级别：\(evaluation.type.rawValue.capitalized)")
                        .font(.subheadline)
                }

                // 问题数量
                HStack {
                    Image(systemName: "list.bullet")
                        .foregroundColor(.blue)
                    Text("问题数量：\(evaluation.totalQuestions)")
                        .font(.subheadline)
                }
            }
            .padding()
            .background(colorScheme == .dark ? Color(UIColor.systemGray6) : Color(UIColor.systemGray6))
            .cornerRadius(10)
        }
    }

    /// 评估说明视图
    /// - Returns: 说明视图
    private func evaluationInstructionsView() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("评估说明")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 10) {
                instructionRow(icon: "1.circle", text: "评估将测试您的听力、阅读、写作和口语能力")
                instructionRow(icon: "2.circle", text: "请在安静的环境中完成评估")
                instructionRow(icon: "3.circle", text: "评估结果将用于创建个性化学习计划")
                instructionRow(icon: "4.circle", text: "您可以随时暂停评估，稍后继续")
                instructionRow(icon: "5.circle", text: "完成评估后，您将收到详细的能力分析报告")
            }
            .padding()
            .background(colorScheme == .dark ? Color(UIColor.systemGray6) : Color(UIColor.systemGray6))
            .cornerRadius(10)
        }
    }

    /// 说明行视图
    /// - Parameters:
    ///   - icon: 图标名称
    ///   - text: 说明文本
    /// - Returns: 说明行视图
    private func instructionRow(icon: String, text: String) -> some View {
        HStack(alignment: .top, spacing: 10) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)

            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
        }
    }
}

struct EvaluationIntroView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EvaluationIntroView(viewModel: EvaluationViewModel())
        }
    }
}
