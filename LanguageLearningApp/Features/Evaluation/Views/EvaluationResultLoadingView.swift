import SwiftUI
import Combine

/// 评估结果加载视图，在评估完成后显示加载动画并获取结果
struct EvaluationResultLoadingView: View {
    @StateObject private var viewModel: EvaluationResultLoadingViewModel
    @Environment(\.dismiss) private var dismiss
    private let resultId: UUID

    init(resultId: UUID, viewModel: EvaluationResultLoadingViewModel = EvaluationResultLoadingViewModel(
        evaluationManager: DependencyContainer.shared.resolve(EvaluationManager.self)
    )) {
        self.resultId = resultId
        _viewModel = StateObject(wrappedValue: viewModel)
    }

    var body: some View {
        ZStack {
            // 背景
            Color(UIColor.systemBackground)
                .edgesIgnoringSafeArea(.all)

            if viewModel.isLoading {
                // 加载动画
                VStack(spacing: 30) {
                    LoadingAnimationView()
                        .frame(width: 200, height: 200)

                    Text("正在生成您的评估结果...")
                        .font(.title3)
                        .fontWeight(.medium)

                    Text("我们正在分析您的答案，生成个性化的学习建议")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 40)
                }
            } else if let result = viewModel.result {
                // 结果视图（直接传递结果对象）
                EvaluationResultView(viewModel: EvaluationResultViewModel(result: result))
            } else {
                // 错误视图
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.orange)

                    Text("无法加载结果")
                        .font(.title2)
                        .fontWeight(.medium)

                    Text(viewModel.errorMessage)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button(action: {
                        dismiss()
                    }) {
                        Text("返回")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .frame(width: 200)
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                    .padding(.top)
                }
                .padding()
            }
        }
        .onAppear {
            viewModel.loadResult(id: resultId)
        }
    }
}

/// 评估结果加载视图模型
class EvaluationResultLoadingViewModel: ObservableObject {
    // 管理器
    private let evaluationManager: EvaluationManager

    // 发布者
    @Published var result: EvaluationResult?
    @Published var isLoading = true
    @Published var errorMessage = ""

    // 取消令牌
    private var cancellables = Set<AnyCancellable>()

    init(evaluationManager: EvaluationManager) {
        self.evaluationManager = evaluationManager
        setupSubscriptions()
    }

    /// 设置订阅
    private func setupSubscriptions() {
        // 监听评估结果
        evaluationManager.$evaluationResult
            .receive(on: DispatchQueue.main)
            .sink { [weak self] result in
                if let result = result {
                    self?.result = result
                    self?.isLoading = false
                }
            }
            .store(in: &cancellables)

        // 监听错误
        evaluationManager.$error
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                if let error = error {
                    self?.errorMessage = error.localizedDescription
                    self?.isLoading = false
                }
            }
            .store(in: &cancellables)
    }

    /// 加载结果
    /// - Parameter id: 评估ID
    func loadResult(id: UUID) {
        isLoading = true

        // 添加延迟，以便显示加载动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.fetchResult(id: id)
        }
    }

    /// 获取结果
    /// - Parameter id: 评估ID
    private func fetchResult(id: UUID) {
        // 使用评估管理器获取结果
        evaluationManager.loadEvaluationResult(id: id)
    }
}

/// 加载动画视图
struct LoadingAnimationView: View {
    @State private var isAnimating = false

    var body: some View {
        ZStack {
            Circle()
                .stroke(Color.gray.opacity(0.2), lineWidth: 8)
                .frame(width: 100, height: 100)

            Circle()
                .trim(from: 0, to: 0.75)
                .stroke(Color.blue, lineWidth: 8)
                .frame(width: 100, height: 100)
                .rotationEffect(Angle(degrees: isAnimating ? 360 : 0))
                .animation(Animation.linear(duration: 1).repeatForever(autoreverses: false), value: isAnimating)
                .onAppear {
                    isAnimating = true
                }
        }
    }
}

struct EvaluationResultLoadingView_Previews: PreviewProvider {
    static var previews: some View {
        EvaluationResultLoadingView(resultId: UUID())
    }
}
