import SwiftUI

/// 评估入口视图，作为评估功能的主入口
struct EvaluationEntryView: View {
    @StateObject private var viewModel = EvaluationEntryViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var showingEvaluationIntro = false
    @State private var showingEvaluationHistory = false

    var body: some View {
        StyledContainer {
            ScrollView {
                VStack(spacing: AppTheme.Dimensions.spacingLarge) {
                    // 顶部标题
                    Text(localizationManager.localizedString(LocalizationKey.language_assessment))
                        .font(AppTheme.Typography.title1)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .padding(.top, AppTheme.Dimensions.spacingLarge)

                    // 评估说明卡片
                    evaluationInfoCard

                    // 开始评估按钮
                    startEvaluationButton

                    // 历史评估卡片
                    if !viewModel.evaluationHistory.isEmpty {
                        historyCard
                    }

                    // 评估优势说明
                    evaluationBenefitsSection

                    Spacer(minLength: 50)
                }
                .padding()
            }
        }
        .onAppear {
            viewModel.loadEvaluationHistory()
        }
        .sheet(isPresented: $showingEvaluationIntro) {
            EvaluationIntroView(viewModel: EvaluationViewModel())
                .onDisappear {
                    viewModel.startNewEvaluation()
                }
        }
        .sheet(isPresented: $showingEvaluationHistory) {
            EvaluationHistoryView(history: viewModel.evaluationHistory)
        }
        .alert(isPresented: $viewModel.showError) {
            Alert(
                title: Text(localizationManager.localizedString(LocalizationKey.error)),
                message: Text(viewModel.errorMessage),
                dismissButton: .default(Text(localizationManager.localizedString(LocalizationKey.ok)))
            )
        }
        .background(
            NavigationLink(
                destination: EvaluationView(evaluationId: viewModel.currentEvaluationId),
                isActive: $viewModel.navigateToEvaluation,
                label: { EmptyView() }
            )
        )
    }

    // 评估信息卡片
    private var evaluationInfoCard: some View {
        StyledCard {
            VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingMedium) {
                HStack {
                    Image(systemName: "chart.bar.doc.horizontal")
                        .font(.title2)
                        .foregroundColor(AppTheme.Colors.primary)

                    Text(localizationManager.localizedString(LocalizationKey.language_level_assessment))
                        .font(AppTheme.Typography.title2)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                }

                Text(localizationManager.localizedString(LocalizationKey.assessment_description))
                    .font(AppTheme.Typography.body)
                    .foregroundColor(AppTheme.Colors.textSecondary)

                HStack {
                    Label(localizationManager.localizedString(LocalizationKey.assessment_duration), systemImage: "clock")
                        .font(AppTheme.Typography.subheadline)
                        .foregroundColor(AppTheme.Colors.textSecondary)

                    Spacer()

                    Label(localizationManager.localizedString(LocalizationKey.assessment_questions), systemImage: "list.bullet")
                        .font(AppTheme.Typography.subheadline)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }
                .padding(.top, AppTheme.Dimensions.spacingSmall)
            }
            .padding()
        }
    }

    // 开始评估按钮
    private var startEvaluationButton: some View {
        StyledButton(
            title: localizationManager.localizedString(LocalizationKey.start_new_assessment),
            action: { showingEvaluationIntro = true },
            icon: "arrow.right.circle.fill",
            isPrimary: true
        )
        .disabled(viewModel.isLoading)
    }

    // 历史评估卡片
    private var historyCard: some View {
        StyledCard {
            VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingMedium) {
                HStack {
                    Text(localizationManager.localizedString(LocalizationKey.recent_assessments))
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    Spacer()

                    Button(action: {
                        showingEvaluationHistory = true
                    }) {
                        Text(localizationManager.localizedString(LocalizationKey.view_all))
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.primary)
                    }
                }

                if let latestResult = viewModel.evaluationHistory.first {
                    HStack {
                        VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingSmall) {
                            Text(latestResult.localizedLevelName())
                                .font(AppTheme.Typography.title3)
                                .foregroundColor(AppTheme.Colors.textPrimary)

                            Text(latestResult.completedTimeDescription())
                                .font(AppTheme.Typography.caption)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }

                        Spacer()

                        ZStack {
                            Circle()
                                .stroke(AppTheme.Colors.backgroundSecondary, lineWidth: 5)
                                .frame(width: 60, height: 60)

                            Circle()
                                .trim(from: 0, to: CGFloat(latestResult.scorePercentage / 100))
                                .stroke(
                                    scoreColor(for: latestResult.overallPerformanceLevel), lineWidth: 5
                                )
                                .frame(width: 60, height: 60)
                                .rotationEffect(.degrees(-90))

                            Text("\(latestResult.overallScore)")
                                .font(AppTheme.Typography.headline)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                        }
                    }
                    .padding()
                }
            }
            .padding()
        }
    }

    // 评估优势说明
    private var evaluationBenefitsSection: some View {
        StyledCard {
            VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingLarge) {
                Text(localizationManager.localizedString(LocalizationKey.assessment_benefits))
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .padding(.top)

                benefitRow(
                    icon: "chart.bar.fill",
                    title: localizationManager.localizedString(LocalizationKey.accurate_assessment),
                    description: localizationManager.localizedString(LocalizationKey.accurate_assessment_desc)
                )
                benefitRow(
                    icon: "person.fill",
                    title: localizationManager.localizedString(LocalizationKey.personalized_learning),
                    description: localizationManager.localizedString(LocalizationKey.personalized_learning_desc)
                )
                benefitRow(
                    icon: "arrow.up.right",
                    title: localizationManager.localizedString(LocalizationKey.progress_tracking),
                    description: localizationManager.localizedString(LocalizationKey.progress_tracking_desc)
                )
                benefitRow(
                    icon: "star.fill",
                    title: localizationManager.localizedString(LocalizationKey.professional_standards),
                    description: localizationManager.localizedString(LocalizationKey.professional_standards_desc)
                )
            }
            .padding()
        }
    }

    // 优势行
    private func benefitRow(icon: String, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: AppTheme.Dimensions.spacingMedium) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(AppTheme.Colors.primary)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingSmall) {
                Text(title)
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                Text(description)
                    .font(AppTheme.Typography.subheadline)
                    .foregroundColor(AppTheme.Colors.textSecondary)
            }
        }
    }

    // 根据表现级别返回颜色
    private func scoreColor(for level: PerformanceLevel) -> Color {
        switch level {
        case .excellent:
            return AppTheme.Colors.success
        case .good:
            return AppTheme.Colors.primary
        case .satisfactory:
            return AppTheme.Colors.warning
        case .needsImprovement:
            return AppTheme.Colors.error
        }
    }
}

struct EvaluationEntryView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EvaluationEntryView()
        }
    }
}

