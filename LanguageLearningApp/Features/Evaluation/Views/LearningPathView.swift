import SwiftUI

/// 学习路径视图，显示个性化学习路径
struct LearningPathView: View {
    let learningPath: PersonalizedLearningPath
    
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedTab = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部标签栏
                tabBar
                
                // 内容
                TabView(selection: $selectedTab) {
                    // 概览
                    overviewTab
                        .tag(0)
                    
                    // 课程
                    lessonsTab
                        .tag(1)
                    
                    // 练习
                    practicesTab
                        .tag(2)
                    
                    // 资源
                    resourcesTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationBarTitle("个性化学习路径", displayMode: .inline)
            .navigationBarItems(trailing: closeButton)
        }
    }
    
    // 关闭按钮
    private var closeButton: some View {
        Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            Image(systemName: "xmark.circle.fill")
                .font(.title3)
                .foregroundColor(.gray)
        }
    }
    
    // 标签栏
    private var tabBar: some View {
        HStack(spacing: 0) {
            tabButton(title: "概览", index: 0)
            tabButton(title: "课程", index: 1)
            tabButton(title: "练习", index: 2)
            tabButton(title: "资源", index: 3)
        }
        .padding(.top, 5)
        .background(Color(UIColor.secondarySystemBackground))
    }
    
    // 标签按钮
    private func tabButton(title: String, index: Int) -> some View {
        Button(action: {
            withAnimation {
                selectedTab = index
            }
        }) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(selectedTab == index ? .semibold : .regular)
                    .foregroundColor(selectedTab == index ? .blue : .gray)
                
                Rectangle()
                    .fill(selectedTab == index ? Color.blue : Color.clear)
                    .frame(height: 3)
            }
        }
        .frame(maxWidth: .infinity)
        .buttonStyle(PlainButtonStyle())
    }
    
    // 概览标签
    private var overviewTab: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 目标级别
                targetLevelSection
                
                // 学习目标
                learningObjectivesSection
                
                // 学习时间
                learningTimeSection
                
                // 学习建议
                learningTipsSection
                
                Spacer(minLength: 30)
            }
            .padding()
        }
    }
    
    // 目标级别部分
    private var targetLevelSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("目标级别")
                .font(.headline)
                .padding(.top)
            
            HStack {
                VStack(alignment: .leading, spacing: 5) {
                    Text(learningPath.targetLevel)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("预计学习时间: \(learningPath.estimatedTimeToComplete) 周")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "flag.fill")
                    .font(.system(size: 30))
                    .foregroundColor(.blue)
            }
            .padding()
            .background(Color(UIColor.tertiarySystemBackground))
            .cornerRadius(10)
        }
    }
    
    // 学习目标部分
    private var learningObjectivesSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("学习目标")
                .font(.headline)
            
            ForEach(learningPath.learningObjectives, id: \.self) { objective in
                HStack(alignment: .top, spacing: 10) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.subheadline)
                    
                    Text(objective)
                        .font(.subheadline)
                        .fixedSize(horizontal: false, vertical: true)
                }
                .padding(.vertical, 5)
            }
        }
    }
    
    // 学习时间部分
    private var learningTimeSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("推荐学习时间")
                .font(.headline)
            
            HStack {
                VStack(alignment: .center, spacing: 5) {
                    Text("\(learningPath.recommendedDailyStudyTime)")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("分钟/天")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(UIColor.tertiarySystemBackground))
                .cornerRadius(10)
                
                VStack(alignment: .center, spacing: 5) {
                    Text("\(learningPath.recommendedDaysPerWeek)")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("天/周")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(UIColor.tertiarySystemBackground))
                .cornerRadius(10)
            }
        }
    }
    
    // 学习建议部分
    private var learningTipsSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("学习建议")
                .font(.headline)
            
            ForEach(learningPath.learningTips, id: \.self) { tip in
                HStack(alignment: .top, spacing: 10) {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(.yellow)
                        .font(.subheadline)
                    
                    Text(tip)
                        .font(.subheadline)
                        .fixedSize(horizontal: false, vertical: true)
                }
                .padding(.vertical, 5)
            }
        }
    }
    
    // 课程标签
    private var lessonsTab: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 15) {
                Text("推荐课程")
                    .font(.headline)
                    .padding(.top)
                
                ForEach(learningPath.recommendedLessons, id: \.id) { lesson in
                    lessonRow(lesson: lesson)
                }
                
                Spacer(minLength: 30)
            }
            .padding()
        }
    }
    
    // 课程行
    private func lessonRow(lesson: LessonRecommendation) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Text(lesson.title)
                    .font(.headline)
                
                Spacer()
                
                Text(lesson.difficulty)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(difficultyColor(for: lesson.difficulty).opacity(0.2))
                    )
                    .foregroundColor(difficultyColor(for: lesson.difficulty))
            }
            
            Text(lesson.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
            
            HStack {
                Image(systemName: "clock")
                    .font(.caption)
                
                Text("\(lesson.estimatedTimeMinutes) 分钟")
                    .font(.caption)
                
                Spacer()
                
                Button(action: {
                    // 开始课程
                }) {
                    Text("开始学习")
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(15)
                }
            }
            .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(10)
    }
    
    // 练习标签
    private var practicesTab: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 15) {
                Text("推荐练习")
                    .font(.headline)
                    .padding(.top)
                
                ForEach(learningPath.recommendedPractices, id: \.id) { practice in
                    practiceRow(practice: practice)
                }
                
                Spacer(minLength: 30)
            }
            .padding()
        }
    }
    
    // 练习行
    private func practiceRow(practice: PracticeRecommendation) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Image(systemName: practiceIcon(for: practice.type))
                    .font(.title3)
                    .foregroundColor(.blue)
                
                Text(practice.title)
                    .font(.headline)
                
                Spacer()
                
                Text(practice.focusArea)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.blue.opacity(0.2))
                    )
                    .foregroundColor(.blue)
            }
            
            Text(practice.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
            
            HStack {
                Image(systemName: "clock")
                    .font(.caption)
                
                Text("\(practice.estimatedTimeMinutes) 分钟")
                    .font(.caption)
                
                Spacer()
                
                Button(action: {
                    // 开始练习
                }) {
                    Text("开始练习")
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(15)
                }
            }
            .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(10)
    }
    
    // 资源标签
    private var resourcesTab: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 15) {
                Text("推荐资源")
                    .font(.headline)
                    .padding(.top)
                
                ForEach(learningPath.recommendedResources, id: \.id) { resource in
                    resourceRow(resource: resource)
                }
                
                Spacer(minLength: 30)
            }
            .padding()
        }
    }
    
    // 资源行
    private func resourceRow(resource: ResourceRecommendation) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Image(systemName: resourceIcon(for: resource.type))
                    .font(.title3)
                    .foregroundColor(.blue)
                
                Text(resource.title)
                    .font(.headline)
                
                Spacer()
                
                Text(resource.type)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.purple.opacity(0.2))
                    )
                    .foregroundColor(.purple)
            }
            
            Text(resource.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
            
            Button(action: {
                // 打开资源
            }) {
                HStack {
                    Text("查看资源")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Image(systemName: "arrow.right")
                        .font(.caption)
                }
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(10)
    }
    
    // 根据难度返回颜色
    private func difficultyColor(for difficulty: String) -> Color {
        switch difficulty.lowercased() {
        case "beginner", "初级":
            return .green
        case "intermediate", "中级":
            return .blue
        case "advanced", "高级":
            return .purple
        default:
            return .gray
        }
    }
    
    // 根据练习类型返回图标
    private func practiceIcon(for type: String) -> String {
        switch type.lowercased() {
        case "listening", "听力":
            return "ear.fill"
        case "speaking", "口语":
            return "mic.fill"
        case "reading", "阅读":
            return "book.fill"
        case "writing", "写作":
            return "pencil"
        case "vocabulary", "词汇":
            return "textformat.abc"
        case "grammar", "语法":
            return "text.badge.checkmark"
        default:
            return "square.stack.fill"
        }
    }
    
    // 根据资源类型返回图标
    private func resourceIcon(for type: String) -> String {
        switch type.lowercased() {
        case "video", "视频":
            return "play.rectangle.fill"
        case "audio", "音频":
            return "headphones"
        case "article", "文章":
            return "doc.text.fill"
        case "book", "书籍":
            return "book.fill"
        case "website", "网站":
            return "globe"
        case "app", "应用":
            return "app.fill"
        default:
            return "link"
        }
    }
    
    private var statusColor: Color {
        switch learningPath.status {
        case .completed:
            return .green
        case .inProgress:
            return .blue
        case .locked:
            return .gray
        case .notStarted:
            return .orange
        }
    }
}

struct LearningPathView_Previews: PreviewProvider {
    static var previews: some View {
        let mockLearningPath = PersonalizedLearningPath(
            id: UUID(),
            userId: UUID(),
            evaluationId: UUID(),
            targetLevel: "中级 (B1)",
            currentLevel: "初级 (A2)",
            estimatedTimeToComplete: 12,
            recommendedDailyStudyTime: 30,
            recommendedDaysPerWeek: 5,
            learningObjectives: [
                "能够理解日常对话和简单的讲话",
                "能够阅读和理解简单的文章和故事",
                "能够进行基本的日常对话",
                "能够写简单的文章和信件"
            ],
            learningTips: [
                "每天坚持学习，保持规律",
                "多听多说，不要害怕犯错",
                "使用闪卡和应用程序增强词汇记忆",
                "找语言交换伙伴练习口语"
            ],
            recommendedLessons: [
                LessonRecommendation(id: UUID(), title: "日常对话进阶", description: "学习在各种日常场景中的对话技巧", difficulty: "中级", estimatedTimeMinutes: 45),
                LessonRecommendation(id: UUID(), title: "阅读理解策略", description: "掌握阅读中文文章的关键技巧", difficulty: "中级", estimatedTimeMinutes: 30)
            ],
            recommendedPractices: [
                PracticeRecommendation(id: UUID(), title: "听力练习：天气预报", description: "练习理解天气预报中的关键信息", type: "听力", focusArea: "理解", estimatedTimeMinutes: 15),
                PracticeRecommendation(id: UUID(), title: "口语练习：餐厅点餐", description: "模拟餐厅点餐场景的对话", type: "口语", focusArea: "流利度", estimatedTimeMinutes: 20)
            ],
            recommendedResources: [
                ResourceRecommendation(id: UUID(), title: "中文播客：慢速中文", description: "适合中级学习者的慢速中文播客", type: "音频", url: "https://example.com"),
                ResourceRecommendation(id: UUID(), title: "中文阅读材料集", description: "适合中级学习者的短文集", type: "文章", url: "https://example.com")
            ],
            status: .inProgress,
            createdAt: Date(),
            updatedAt: Date()
        )
        
        return LearningPathView(learningPath: mockLearningPath)
    }
}
