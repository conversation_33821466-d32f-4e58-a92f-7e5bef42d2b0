import SwiftUI

/// 评估视图，用于进行评估
struct EvaluationView: View {
    let evaluationId: UUID?

    @StateObject private var viewModel = EvaluationViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @Environment(\.presentationMode) var presentationMode
    @State private var showingExitConfirmation = false

    var body: some View {
        StyledContainer {
            if viewModel.state == .loading {
                // 加载指示器
                ProgressView(localizationManager.localizedString(LocalizationKey.loading))
                    .progressViewStyle(CircularProgressViewStyle())
                    .padding()
                    .background(AppTheme.Colors.backgroundSecondary)
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                    .shadow(radius: 5)
            } else if let evaluation = viewModel.evaluation {
                // 评估内容
                VStack(spacing: 0) {
                    // 顶部进度条
                    progressHeader

                    // 问题内容
                    ScrollView {
                        VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingLarge) {
                            // 当前部分标题
                            if let currentSection = viewModel.currentSection {
                                Text(currentSection.title)
                                    .font(AppTheme.Typography.title2)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                                    .padding(.top)
                            }

                            // 当前问题
                            if let currentQuestion = viewModel.currentQuestion {
                                questionView(for: currentQuestion)
                            } else {
                                Text(localizationManager.localizedString(LocalizationKey.error_loading_question))
                                    .font(AppTheme.Typography.headline)
                                    .foregroundColor(AppTheme.Colors.error)
                                    .padding()
                            }

                            // 答案反馈
                            if let currentQuestion = viewModel.currentQuestion, currentQuestion.userAnswer != nil {
                                feedbackView
                            }

                            Spacer(minLength: 100)
                        }
                        .padding()
                    }

                    // 底部按钮
                    bottomButtons
                }
            } else {
                // 错误视图 - 使用统一的错误处理
                VStack(spacing: AppTheme.Dimensions.spacingLarge) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(AppTheme.Colors.warning)

                    Text(localizationManager.localizedString(LocalizationKey.error_loading_assessment))
                        .font(AppTheme.Typography.title2)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    StyledButton(
                        title: localizationManager.localizedString(LocalizationKey.back),
                        action: {
                            presentationMode.wrappedValue.dismiss()
                        },
                        isPrimary: true
                    )
                    .padding(.top)
                }
                .padding()
                .onAppear {
                    // 使用统一的错误管理器显示错误
                    if let errorMessage = viewModel.errorMessage {
                        ErrorManager.shared.showError(.customError(errorMessage))
                    }
                }
            }
        }
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarBackButtonHidden(true)
        .navigationBarItems(leading: backButton)
        .onAppear {
            if let id = evaluationId {
                print("开始加载评估，ID: \(id)")
                print("DEBUG: 网络状态检查 - APIClient.isNetworkAvailable = \(APIClient.shared.isNetworkAvailable)")
                print("DEBUG: 当前环境 - \(AppEnvironment.current.rawValue)")
                print("DEBUG: API基础URL - \(AppEnvironment.current.baseURL.absoluteString)")

                // 添加延迟以确保网络状态已正确初始化
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    print("DEBUG: 延迟后网络状态 - APIClient.isNetworkAvailable = \(APIClient.shared.isNetworkAvailable)")
                    viewModel.loadEvaluation(id: id)
                }
            } else {
                print("无法加载评估，ID为nil")
            }
        }
        .alert(isPresented: $showingExitConfirmation) {
            Alert(
                title: Text(localizationManager.localizedString(LocalizationKey.confirm_exit)),
                message: Text(localizationManager.localizedString(LocalizationKey.confirm_exit_message)),
                primaryButton: .destructive(Text(localizationManager.localizedString(LocalizationKey.exit))) {
                    presentationMode.wrappedValue.dismiss()
                },
                secondaryButton: .cancel(Text(localizationManager.localizedString(LocalizationKey.continue_assessment)))
            )
        }
        .sheet(isPresented: $viewModel.showResultView) {
            if let result = viewModel.evaluationResult {
                // 直接使用结果对象
                EvaluationResultView(viewModel: EvaluationResultViewModel(result: result))
            } else if let evaluation = viewModel.evaluation {
                // 如果没有结果对象，则使用评估ID获取结果
                EvaluationResultLoadingView(resultId: evaluation.id)
            }
        }
        .withErrorHandling() // 添加统一错误处理
    }

    // 返回按钮
    private var backButton: some View {
        Button(action: {
            showingExitConfirmation = true
        }) {
            HStack {
                Image(systemName: "chevron.left")
                Text(localizationManager.localizedString(LocalizationKey.exit))
            }
            .foregroundColor(AppTheme.Colors.textPrimary)
        }
    }

    // 进度头部
    private var progressHeader: some View {
        VStack(spacing: AppTheme.Dimensions.spacingSmall) {
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .frame(width: geometry.size.width, height: 8)
                        .opacity(0.2)
                        .foregroundColor(AppTheme.Colors.textSecondary)

                    Rectangle()
                        .frame(width: geometry.size.width * CGFloat(viewModel.progress), height: 8)
                        .foregroundColor(AppTheme.Colors.primary)
                }
                .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
            }
            .frame(height: 8)
            .padding(.horizontal)

            // 进度文本
            HStack {
                Text("\(localizationManager.localizedString(LocalizationKey.question)) \(viewModel.currentQuestionIndex + 1) / \(viewModel.totalQuestions)")
                    .font(AppTheme.Typography.subheadline)
                    .foregroundColor(AppTheme.Colors.textSecondary)

                Spacer()

                if let remainingTime = viewModel.remainingTime {
                    let minutes = Int(remainingTime) / 60
                    let seconds = Int(remainingTime) % 60
                    let formattedTime = String(format: "%02d:%02d", minutes, seconds)
                    let isTimeAlmostUp = remainingTime < 60 // 少于1分钟

                    HStack {
                        Image(systemName: "clock")
                            .font(AppTheme.Typography.subheadline)

                        Text(formattedTime)
                            .font(AppTheme.Typography.subheadline)
                    }
                    .foregroundColor(isTimeAlmostUp ? AppTheme.Colors.error : AppTheme.Colors.textSecondary)
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, AppTheme.Dimensions.spacingSmall)
        .background(AppTheme.Colors.backgroundSecondary)
    }

    // 问题视图
    private func questionView(for question: EvaluationQuestion) -> some View {
        VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingMedium) {
            // 调试信息
            #if DEBUG
            VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingSmall) {
                Text("问题ID: \(question.id)")
                    .font(AppTheme.Typography.caption)
                    .foregroundColor(AppTheme.Colors.textSecondary)
                Text("问题类型: \(question.type.rawValue)")
                    .font(AppTheme.Typography.caption)
                    .foregroundColor(AppTheme.Colors.textSecondary)
                if let options = question.options {
                    Text("选项数量: \(options.count)")
                        .font(AppTheme.Typography.caption)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }
            }
            .padding(.bottom, AppTheme.Dimensions.spacingSmall)
            #endif

            // 使用通用的ExerciseContentView组件
            ExerciseContentView(
                exerciseData: UnifiedExerciseData.fromEvaluationQuestion(question),
                currentIndex: viewModel.currentQuestionIndex,
                totalCount: viewModel.totalQuestions,
                answerViewBuilder: { _ in
                    answerView(for: question)
                },
                showHints: false
            )
        }
    }

    // 获取阅读内容
    private func getReadingContent(for question: EvaluationQuestion) -> String? {
        // 这里应该从API获取真实的阅读内容
        // 目前使用模拟数据，未来可以扩展为从API获取
        return question.content
    }

    // 回答视图
    private func answerView(for question: EvaluationQuestion) -> some View {
        VStack {
            if question.type == .multipleChoice {
                multipleChoiceView(question)
            } else if question.type == .fillIn {
                fillInBlankView()
            } else {
                // 其他类型的问题
                Text(localizationManager.localizedString(LocalizationKey.unsupported_question_type))
                    .font(AppTheme.Typography.body)
                    .foregroundColor(AppTheme.Colors.error)
            }
        }
    }

    // 多选视图
    private func multipleChoiceView(_ question: EvaluationQuestion) -> some View {
        VStack(spacing: AppTheme.Dimensions.spacingMedium) {
            if let options = question.options {
                ForEach(options.indices, id: \.self) { index in
                    let option = options[index]
                    Button(action: {
                        viewModel.selectAnswer(option)
                    }) {
                        HStack {
                            Text(option)
                                .font(AppTheme.Typography.body)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                                .multilineTextAlignment(.leading)

                            Spacer()

                            if question.userAnswer == option {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(AppTheme.Colors.primary)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                .fill(question.userAnswer == option ? AppTheme.Colors.primary.opacity(0.1) : AppTheme.Colors.backgroundSecondary)
                        )
                    }
                }
            }
        }
    }

    // 填空视图
    private func fillInBlankView() -> some View {
        TextField(localizationManager.localizedString(LocalizationKey.enter_your_answer), text: .constant(""))
            .textFieldStyle(RoundedBorderTextFieldStyle())
            .padding()
    }

    // 反馈视图
    private var feedbackView: some View {
        VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingMedium) {
            Text(localizationManager.localizedString(LocalizationKey.yourAnswer))
                .font(AppTheme.Typography.headline)
                .foregroundColor(AppTheme.Colors.textPrimary)

            if let currentQuestion = viewModel.currentQuestion {
                Text(currentQuestion.userAnswer ?? "")
                    .font(AppTheme.Typography.body)
                    .foregroundColor(AppTheme.Colors.textSecondary)
            }
        }
        .padding()
        .background(AppTheme.Colors.backgroundSecondary)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
    }

    // 底部按钮
    private var bottomButtons: some View {
        HStack(spacing: AppTheme.Dimensions.spacingMedium) {
            if viewModel.currentQuestionIndex > 0 {
                StyledButton(
                    title: localizationManager.localizedString(LocalizationKey.previous),
                    action: {
                        viewModel.previousQuestion()
                    },
                    icon: "arrow.left",
                    isPrimary: false
                )
            }

            if viewModel.currentQuestionIndex < viewModel.totalQuestions - 1 {
                StyledButton(
                    title: localizationManager.localizedString(LocalizationKey.next),
                    action: {
                        viewModel.nextQuestion()
                    },
                    icon: "arrow.right",
                    isPrimary: true
                )
            } else {
                StyledButton(
                    title: localizationManager.localizedString(LocalizationKey.submit),
                    action: {
                        viewModel.submitAnswer()
                    },
                    icon: "checkmark",
                    isPrimary: true
                )
            }
        }
        .padding()
        .background(AppTheme.Colors.backgroundSecondary)
    }
}

struct EvaluationView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EvaluationView(evaluationId: UUID())
        }
    }
}