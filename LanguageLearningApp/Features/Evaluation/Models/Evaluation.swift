import Foundation

/// 评估类型
public enum EvaluationType: String, Codable {
    /// 初始评估（用于确定用户的语言水平）
    case placement = "placement"
    /// 进度评估（用于评估用户的学习进度）
    case progress = "progress"
    /// 技能评估（用于评估特定技能）
    case skill = "skill"
    /// 成就评估
    case achievement = "achievement"
    /// 认证评估
    case certification = "certification"
    /// 初级评估
    case beginner = "beginner"
    /// 中级评估
    case intermediate = "intermediate"
    /// 高级评估
    case advanced = "advanced"
}

/// 评估类别
public enum EvaluationCategory: String, Codable {
    /// 词汇
    case vocabulary = "vocabulary"
    /// 语法
    case grammar = "grammar"
    /// 会话
    case conversation = "conversation"
    /// 听力
    case listening = "listening"
    /// 阅读
    case reading = "reading"
    /// 写作
    case writing = "writing"
}

/// 评估状态
public enum EvaluationStatus: String, Codable {
    /// 可用
    case available = "available"
    /// 进行中
    case inProgress = "in_progress"
    /// 已完成
    case completed = "completed"
    /// 已过期
    case expired = "expired"
    /// 已取消
    case cancelled = "cancelled"
}

/// 问题类型
public enum QuestionType: String, Codable {
    /// 多选题
    case multipleChoice = "multiple-choice"
    /// 单选题
    case singleChoice = "single-choice"
    /// 填空题
    case fillIn = "fill-in"
    /// 口语题
    case speaking = "speaking"
    /// 听力题
    case listening = "listening"
}

/// 评估问题模型
public struct EvaluationQuestion: Identifiable, Codable {
    /// 问题ID
    public let id: UUID
    /// 部分ID
    public let sectionID: UUID
    /// 问题类型
    public let type: QuestionType
    /// 问题内容
    public let content: String
    /// 选项（用于选择题）
    public let options: [String]?
    /// 正确答案
    public let correctAnswer: String
    /// 用户答案
    public var userAnswer: String?
    /// 是否正确
    public var isCorrect: Bool?
    /// 分值
    public let points: Int
    /// 解释（用于错误反馈）
    public let explanation: String?
    /// 音频URL（用于听力题）
    public let audioURL: URL?
    /// 图片URL（用于辅助理解）
    public let imageURL: URL?

    /// 检查答案是否正确
    public mutating func checkAnswer() {
        guard let userAnswer = userAnswer else {
            isCorrect = false
            return
        }

        switch type {
        case .multipleChoice, .singleChoice, .fillIn:
            isCorrect = userAnswer == correctAnswer
        case .speaking:
            // 口语题可能需要更复杂的评分逻辑
            isCorrect = true // 简化处理，实际应用中可能需要语音识别或人工评分
        case .listening:
            isCorrect = userAnswer == correctAnswer
        }
    }
}

/// 评估部分
public struct EvaluationSection: Identifiable, Codable {
    /// 部分ID
    public let id: UUID
    /// 评估ID
    public let evaluationID: UUID
    /// 部分标题
    public let title: String
    /// 技能类型
    public let skill: String
    /// 权重（用于计算总分）
    public let weight: Int
    /// 得分
    public var score: Int?
    /// 问题列表
    public var questions: [EvaluationQuestion]

    /// 计算部分总分
    public var totalPoints: Int {
        return questions.reduce(0) { $0 + $1.points }
    }

    /// 计算已完成的问题数量
    public var completedQuestionsCount: Int {
        return questions.filter { $0.userAnswer != nil }.count
    }

    /// 计算正确的问题数量
    public var correctQuestionsCount: Int {
        return questions.filter { $0.isCorrect == true }.count
    }

    /// 计算部分完成百分比
    public var completionPercentage: Double {
        guard !questions.isEmpty else { return 0 }
        return Double(completedQuestionsCount) / Double(questions.count) * 100.0
    }

    /// 计算部分得分
    public mutating func calculateScore() {
        let earnedPoints = questions.reduce(0) { total, question in
            if question.isCorrect == true {
                return total + question.points
            }
            return total
        }

        score = earnedPoints
    }
}

/// 评估模型
public struct Evaluation: Identifiable, Codable {
    /// 评估ID
    public let id: UUID
    /// 用户ID
    public let userID: UUID
    /// 评估类型
    public let type: EvaluationType
    /// 评估类别
    public let category: EvaluationCategory
    /// 评估状态
    public let status: EvaluationStatus
    /// 评估标题
    public let title: String
    /// 评估描述
    public let description: String
    /// 通过分数
    public let passingScore: Int
    /// 评估时长（分钟）
    public let duration: Int
    /// 问题总数
    public let totalQuestions: Int
    /// 评估部分
    public var sections: [EvaluationSection]
    /// 是否已开始
    public var isStarted: Bool
    /// 开始时间
    public var startedAt: Date?
    /// 是否已完成
    public var isCompleted: Bool
    /// 完成时间
    public var completedAt: Date?
    /// 得分
    public var score: Int?
    /// 结果ID
    public var resultID: UUID?
    /// 创建时间
    public let createdAt: Date
    /// 更新时间
    public var updatedAt: Date

    public init(id: UUID, userID: UUID, type: EvaluationType, category: EvaluationCategory, status: EvaluationStatus, title: String = "", description: String = "", passingScore: Int, duration: Int = 30, totalQuestions: Int, sections: [EvaluationSection], isStarted: Bool, isCompleted: Bool, createdAt: Date, updatedAt: Date) {
        self.id = id
        self.userID = userID
        self.type = type
        self.category = category
        self.status = status
        self.title = title
        self.description = description
        self.passingScore = passingScore
        self.duration = duration
        self.totalQuestions = totalQuestions
        self.sections = sections
        self.isStarted = isStarted
        self.isCompleted = isCompleted
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    /// 计算总分
    public var totalPoints: Int {
        return sections.reduce(0) { $0 + $1.totalPoints }
    }

    /// 计算已完成的问题数量
    public var completedQuestionsCount: Int {
        return sections.reduce(0) { $0 + $1.completedQuestionsCount }
    }

    /// 计算正确的问题数量
    public var correctQuestionsCount: Int {
        return sections.reduce(0) { $0 + $1.correctQuestionsCount }
    }

    /// 计算评估完成百分比
    public var completionPercentage: Double {
        guard totalQuestions > 0 else { return 0 }
        return Double(completedQuestionsCount) / Double(totalQuestions) * 100.0
    }

    /// 计算评估得分
    public mutating func calculateScore() {
        // 先计算每个部分的得分
        for i in 0..<sections.count {
            sections[i].calculateScore()
        }

        // 计算总得分
        let totalScore = sections.reduce(0) { total, section in
            if let sectionScore = section.score {
                return total + sectionScore
            }
            return total
        }

        score = totalScore
    }

    /// 检查是否通过评估
    public var isPassed: Bool {
        guard let score = score else { return false }
        return score >= passingScore
    }

    /// 获取剩余时间（秒）
    public func remainingTime() -> TimeInterval? {
        guard let startedAt = startedAt, !isCompleted else { return nil }

        let elapsedTime = Date().timeIntervalSince(startedAt)
        let totalTime = TimeInterval(duration * 60) // 转换为秒

        let remainingTime = totalTime - elapsedTime
        return remainingTime > 0 ? remainingTime : 0
    }
}

/// 评估响应
public struct EvaluationResponse: Codable {
    public let data: Evaluation
}

/// 评估列表响应
public struct EvaluationListResponse: Codable {
    public let data: [Evaluation]
}
