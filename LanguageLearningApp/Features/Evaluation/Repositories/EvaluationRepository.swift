import Foundation
import Combine

/// 评估仓库协议
/// 定义评估相关的业务方法
public protocol EvaluationRepositoryProtocol: RepositoryProtocol where T == Evaluation, ID == UUID {
    /// 获取可用评估列表
    /// - Returns: 包含评估列表的发布者
    func getAvailableEvaluations() -> AnyPublisher<[Evaluation], Error>

    /// 开始评估
    /// - Parameter id: 评估ID
    /// - Returns: 包含更新后评估的发布者
    func startEvaluation(id: UUID) -> AnyPublisher<Evaluation, Error>

    /// 提交评估答案
    /// - Parameters:
    ///   - id: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 用户答案
    /// - Returns: 包含更新后评估的发布者
    func submitAnswer(id: UUID, questionId: UUID, answer: String) -> AnyPublisher<Evaluation, Error>

    /// 完成评估
    /// - Parameter id: 评估ID
    /// - Returns: 包含评估结果的发布者
    func completeEvaluation(id: UUID) -> AnyPublisher<EvaluationResult, Error>

    /// 获取用户评估历史
    /// - Parameter userID: 用户ID
    /// - Returns: 包含评估结果列表的发布者
    func getUserEvaluationHistory(userID: UUID) -> AnyPublisher<[EvaluationResult], Error>

    /// 获取评估结果
    /// - Parameter id: 评估ID
    /// - Returns: 包含评估结果的发布者
    func getEvaluationResult(id: UUID) -> AnyPublisher<EvaluationResult, Error>

    /// 创建新评估
    /// - Returns: 包含新创建评估ID的发布者
    func createEvaluation() -> AnyPublisher<UUID, Error>

    /// 清除本地缓存的所有评估
    /// - Returns: 包含操作结果的发布者
    func clearLocalCache() -> AnyPublisher<Bool, Error>

    // MARK: - 异步方法

    /// 异步获取可用评估列表
    /// - Returns: 评估列表
    func getAvailableEvaluationsAsync() async throws -> [Evaluation]

    /// 异步开始评估
    /// - Parameter id: 评估ID
    /// - Returns: 更新后的评估
    func startEvaluationAsync(id: UUID) async throws -> Evaluation

    /// 异步提交评估答案
    /// - Parameters:
    ///   - id: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 用户答案
    /// - Returns: 更新后的评估
    func submitAnswerAsync(id: UUID, questionId: UUID, answer: String) async throws -> Evaluation

    /// 异步完成评估
    /// - Parameter id: 评估ID
    /// - Returns: 评估结果
    func completeEvaluationAsync(id: UUID) async throws -> EvaluationResult

    /// 异步获取用户评估历史
    /// - Parameter userID: 用户ID
    /// - Returns: 评估结果列表
    func getUserEvaluationHistoryAsync(userID: UUID) async throws -> [EvaluationResult]

    /// 异步获取评估结果
    /// - Parameter id: 评估ID
    /// - Returns: 评估结果
    func getEvaluationResultAsync(id: UUID) async throws -> EvaluationResult

    /// 异步创建新评估
    /// - Returns: 新创建的评估ID
    func createEvaluationAsync() async throws -> UUID

    /// 异步清除本地缓存的所有评估
    /// - Returns: 操作结果
    func clearLocalCacheAsync() async throws -> Bool
}

/// 评估仓库实现
public class EvaluationRepository: EvaluationRepositoryProtocol {
    public typealias T = Evaluation
    public typealias ID = UUID

    private let remoteDataSource: EvaluationRemoteDataSourceProtocol
    private let localDataSource: EvaluationLocalDataSource
    private let userManager: any UserManagerProtocol
    private let shouldFetchRemoteFirst: Bool

    public init(
        remoteDataSource: EvaluationRemoteDataSourceProtocol = EvaluationRemoteDataSource.shared,
        localDataSource: EvaluationLocalDataSource = EvaluationLocalDataSource.shared,
        userManager: any UserManagerProtocol = UserManager.shared,
        shouldFetchRemoteFirst: Bool = true
    ) {
        self.remoteDataSource = remoteDataSource
        self.localDataSource = localDataSource
        self.userManager = userManager
        self.shouldFetchRemoteFirst = shouldFetchRemoteFirst
    }

    private func getAuthToken() -> String? { userManager.authToken }
    private func getCurrentUserId() -> UUID? { userManager.currentUser?.id }

    // Helper to wrap local sync throwing calls into Combine publishers
    private func publisher<Output>(for कार्य: @escaping () throws -> Output) -> AnyPublisher<Output, Error> {
        return Future<Output, Error> { promise in
            do {
                let result = try कार्य()
                promise(.success(result))
            } catch {
                promise(.failure(error))
            }
        }.eraseToAnyPublisher()
    }

    // MARK: - EvaluationRepositoryProtocol Specific Methods

    /// 获取可用评估列表
    /// - Returns: 包含评估列表的发布者
    public func getAvailableEvaluations() -> AnyPublisher<[Evaluation], Error> { getAll() }

    /// 开始评估
    /// - Parameter id: 评估ID
    /// - Returns: 包含更新后评估的发布者
    public func startEvaluation(id: UUID) -> AnyPublisher<Evaluation, Error> {
        guard let authToken = getAuthToken() else { return Fail<Evaluation, Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        return remoteDataSource.startEvaluation(evaluationId: id, authToken: authToken)
            .flatMap { [weak self] evaluation -> AnyPublisher<Evaluation, Error> in // Ensure return type is explicit
                guard let self = self else { return Fail<Evaluation, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.save(evaluation) } // This already returns AnyPublisher<Evaluation, Error>
            }.eraseToAnyPublisher()
    }

    /// 提交评估答案
    /// - Parameters:
    ///   - id: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 用户答案
    /// - Returns: 包含更新后评估的发布者
    public func submitAnswer(id: UUID, questionId: UUID, answer: String) -> AnyPublisher<Evaluation, Error> {
        guard let authToken = getAuthToken() else { return Fail<Evaluation, Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        let evalAnswer = EvaluationAnswer(questionId: questionId, answer: answer)
        return remoteDataSource.submitAnswer(evaluationId: id, questionId: questionId.uuidString, answer: evalAnswer, authToken: authToken)
            .flatMap { [weak self] evaluation -> AnyPublisher<Evaluation, Error> in // Ensure return type is explicit
                guard let self = self else { return Fail<Evaluation, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.save(evaluation) }
            }.eraseToAnyPublisher()
    }

    /// 完成评估
    /// - Parameter id: 评估ID
    /// - Returns: 包含评估结果的发布者
    public func completeEvaluation(id: UUID) -> AnyPublisher<EvaluationResult, Error> {
        guard let authToken = getAuthToken() else { return Fail<EvaluationResult, Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        return remoteDataSource.completeEvaluation(evaluationId: id, authToken: authToken)
    }

    /// 获取用户评估历史
    /// - Parameter userID: 用户ID
    /// - Returns: 包含评估结果列表的发布者
    public func getUserEvaluationHistory(userID: UUID) -> AnyPublisher<[EvaluationResult], Error> {
        guard let authToken = getAuthToken() else { return Fail<[EvaluationResult], Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        return remoteDataSource.getUserEvaluationHistory(userId: userID.uuidString, authToken: authToken)
    }

    /// 获取评估结果
    /// - Parameter id: 评估ID
    /// - Returns: 包含评估结果的发布者
    public func getEvaluationResult(id: UUID) -> AnyPublisher<EvaluationResult, Error> {
        guard let authToken = getAuthToken() else { return Fail<EvaluationResult, Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        return remoteDataSource.getEvaluationResult(evaluationId: id, authToken: authToken)
    }

    /// 创建新评估
    /// - Returns: 包含新创建评估ID的发布者
    public func createEvaluation() -> AnyPublisher<UUID, Error> {
        guard let currentUserID = getCurrentUserId() else { return Fail<UUID, Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        let newEval = Evaluation(
            id: UUID(), 
            userID: currentUserID,
            type: .placement,
            category: .grammar,
            status: .available,
            passingScore: 0, 
            totalQuestions: 0, 
            sections: [], 
            isStarted: false, 
            isCompleted: false, 
            createdAt: Date(), 
            updatedAt: Date()
        )
        return publisher { try self.localDataSource.save(newEval) }.map { $0.id }.eraseToAnyPublisher()
    }

    /// 清除本地缓存的所有评估
    /// - Returns: 包含操作结果的发布者
    public func clearLocalCache() -> AnyPublisher<Bool, Error> {
        // EvaluationLocalDataSource.clearAll clears both evaluations and results.
        // If only evaluations should be cleared, a new method in localDS is needed.
        // For now, using existing clearAll and mapping to true.
        return publisher { try self.localDataSource.clearAllEvaluationData() }.map { true }.eraseToAnyPublisher()
    }

    // MARK: - RepositoryProtocol Conformance

    public func getAll() -> AnyPublisher<[Evaluation], Error> {
        guard let authToken = getAuthToken() else { return Fail<[Evaluation], Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        let remoteFetch = remoteDataSource.getAll(authToken: authToken)
            .flatMap { [weak self] remoteEntities -> AnyPublisher<[Evaluation], Error> in
                guard let self = self else { return Fail<[Evaluation], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.saveAll(remoteEntities) }
            }

        if shouldFetchRemoteFirst {
            return remoteFetch.catch { [weak self] (error: Error) -> AnyPublisher<[Evaluation], Error> in // Explicit error type
                guard let self = self else { return Fail<[Evaluation], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.getAll() }
            }.eraseToAnyPublisher()
        } else {
            return publisher { try self.localDataSource.getAll() }
                .catch { [weak self] (error: Error) -> AnyPublisher<[Evaluation], Error> in // Explicit error type
                    guard let self = self else { return Fail<[Evaluation], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                    return remoteFetch.eraseToAnyPublisher()
                }.eraseToAnyPublisher()
        }
    }

    public func getById(_ id: UUID) -> AnyPublisher<Evaluation?, Error> {
        guard let authToken = getAuthToken() else { return Fail<Evaluation?, Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        let remoteFetch = remoteDataSource.getById(id, authToken: authToken)
            .flatMap { [weak self] remoteEntity -> AnyPublisher<Evaluation?, Error> in
                guard let self = self else { return Fail<Evaluation?, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.save(remoteEntity) }.map { $0 as Evaluation? }.eraseToAnyPublisher()
            }

        if shouldFetchRemoteFirst {
            return remoteFetch.catch { [weak self] (error: Error) -> AnyPublisher<Evaluation?, Error> in // Explicit error type and parameter name
                guard let self = self else { return Fail<Evaluation?, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.getById(id) }
            }.eraseToAnyPublisher()
        } else {
            return publisher { try self.localDataSource.getById(id) }
                .catch { [weak self] (error: Error) -> AnyPublisher<Evaluation?, Error> in // Explicit error type
                    guard let self = self else { return Fail<Evaluation?, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                    return remoteFetch.eraseToAnyPublisher()
                }.eraseToAnyPublisher()
        }
    }

    public func save(_ entity: Evaluation) -> AnyPublisher<Evaluation, Error> {
        guard let authToken = getAuthToken() else { return Fail<Evaluation, Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        return remoteDataSource.save(entity, authToken: authToken)
            .flatMap { [weak self] savedRemoteEntity -> AnyPublisher<Evaluation, Error> in // Explicit return type
                guard let self = self else { return Fail<Evaluation, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.save(savedRemoteEntity) }
            }
            .catch { [weak self] (error: Error) -> AnyPublisher<Evaluation, Error> in // Explicit error type and explicit return type
                guard let self = self else { return Fail<Evaluation, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.save(entity) } // Save locally only
            }.eraseToAnyPublisher()
    }

    public func saveAll(_ entities: [Evaluation]) -> AnyPublisher<[Evaluation], Error> {
        guard let authToken = getAuthToken() else { return Fail<[Evaluation], Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        return remoteDataSource.saveAll(entities, authToken: authToken)
            .flatMap { [weak self] savedRemoteEntities -> AnyPublisher<[Evaluation], Error> in // Explicit return type
                guard let self = self else { return Fail<[Evaluation], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                return self.publisher { try self.localDataSource.saveAll(savedRemoteEntities) }
            }
            .catch { [weak self] (error: Error) -> AnyPublisher<[Evaluation], Error> in // Explicit error type and explicit return type
                guard let self = self else { return Fail<[Evaluation], Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                // The previous version had .map{entities}.switchToLatest() here, which was incorrect.
                // self.publisher already returns AnyPublisher<[Evaluation], Error>
                return self.publisher { try self.localDataSource.saveAll(entities) } 
            }.eraseToAnyPublisher()
    }

    public func delete(_ id: UUID) -> AnyPublisher<Bool, Error> {
        guard let authToken = getAuthToken() else { return Fail<Bool, Error>(error: RepositoryError.authenticationRequired).mapError { $0 as Error }.eraseToAnyPublisher() }
        return remoteDataSource.delete(id, authToken: authToken)
            .flatMap { [weak self] success -> AnyPublisher<Bool, Error> in
                guard let self = self else { return Fail<Bool, Error>(error: RepositoryError.unknown).mapError { $0 as Error }.eraseToAnyPublisher() }
                if success {
                    return self.publisher { try self.localDataSource.delete(id) }
                } else {
                    return Just(false).setFailureType(to: Error.self).eraseToAnyPublisher()
                }
            }.eraseToAnyPublisher()
    }

    // MARK: - Async Methods

    private func performAsyncTask<Output>( कार्य: @escaping () throws -> Output) async throws -> Output {
        try कार्य()
    }

    /// 异步获取可用评估列表
    /// - Returns: 评估列表
    public func getAvailableEvaluationsAsync() async throws -> [Evaluation] { try await getAllAsync() }

    /// 异步开始评估
    /// - Parameter id: 评估ID
    /// - Returns: 更新后的评估
    public func startEvaluationAsync(id: UUID) async throws -> Evaluation {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        let evaluation = try await remoteDataSource.startEvaluationAsync(evaluationId: id, authToken: authToken)
        return try await performAsyncTask { try self.localDataSource.save(evaluation) }
    }

    /// 异步提交评估答案
    /// - Parameters:
    ///   - id: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 用户答案
    /// - Returns: 更新后的评估
    public func submitAnswerAsync(id: UUID, questionId: UUID, answer: String) async throws -> Evaluation {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        let evalAnswer = EvaluationAnswer(questionId: questionId, answer: answer)
        let evaluation = try await remoteDataSource.submitAnswerAsync(evaluationId: id, questionId: questionId.uuidString, answer: evalAnswer, userId: getCurrentUserId()?.uuidString ?? "", authToken: authToken)
        return try await performAsyncTask { try self.localDataSource.save(evaluation) }
    }

    /// 异步完成评估
    /// - Parameter id: 评估ID
    /// - Returns: 评估结果
    public func completeEvaluationAsync(id: UUID) async throws -> EvaluationResult {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        return try await remoteDataSource.completeEvaluationAsync(evaluationId: id, authToken: authToken)
    }

    /// 异步获取用户评估历史
    /// - Parameter userID: 用户ID
    /// - Returns: 评估结果列表
    public func getUserEvaluationHistoryAsync(userID: UUID) async throws -> [EvaluationResult] {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        return try await remoteDataSource.getUserEvaluationHistoryAsync(userId: userID.uuidString, authToken: authToken)
    }

    /// 异步获取评估结果
    /// - Parameter id: 评估ID
    /// - Returns: 评估结果
    public func getEvaluationResultAsync(id: UUID) async throws -> EvaluationResult {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        return try await remoteDataSource.getEvaluationResultAsync(evaluationId: id, authToken: authToken)
    }

    /// 异步创建新评估
    /// - Returns: 新创建的评估ID
    public func createEvaluationAsync() async throws -> UUID {
        guard let currentUserID = getCurrentUserId() else { throw RepositoryError.authenticationRequired }
        let newEval = Evaluation(
            id: UUID(), 
            userID: currentUserID,
            type: .placement,
            category: .grammar,
            status: .available,
            passingScore: 0, 
            totalQuestions: 0, 
            sections: [], 
            isStarted: false, 
            isCompleted: false, 
            createdAt: Date(), 
            updatedAt: Date()
        )
        let savedEval = try await performAsyncTask { try self.localDataSource.save(newEval) }
        return savedEval.id
    }

    /// 异步清除本地缓存的所有评估
    /// - Returns: 操作结果
    public func clearLocalCacheAsync() async throws -> Bool {
        try await performAsyncTask { try self.localDataSource.clearAllEvaluationData() }
        return true
    }

    public func getAllAsync() async throws -> [Evaluation] {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        if shouldFetchRemoteFirst {
            do {
                let remoteEntities = try await remoteDataSource.getAllAsync(authToken: authToken)
                _ = try await performAsyncTask { try self.localDataSource.saveAll(remoteEntities) }
                return remoteEntities
            } catch { return try await performAsyncTask { try self.localDataSource.getAll() } }
        } else {
            do { return try await performAsyncTask { try self.localDataSource.getAll() } }
            catch { 
                let remoteEntities = try await remoteDataSource.getAllAsync(authToken: authToken)
                _ = try await performAsyncTask { try self.localDataSource.saveAll(remoteEntities) }
                return remoteEntities
            }
        }
    }

    public func getByIdAsync(_ id: UUID) async throws -> Evaluation? {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        if shouldFetchRemoteFirst {
            do {
                let remoteEntity = try await remoteDataSource.getByIdAsync(id, authToken: authToken)
                return try await performAsyncTask { try self.localDataSource.save(remoteEntity) }
            } catch { return try await performAsyncTask { try self.localDataSource.getById(id) } }
        } else {
            do { return try await performAsyncTask { try self.localDataSource.getById(id) } }
            catch { 
                let remoteEntity = try await remoteDataSource.getByIdAsync(id, authToken: authToken)
                return try await performAsyncTask { try self.localDataSource.save(remoteEntity) }
            }
        }
    }

    public func saveAsync(_ entity: Evaluation) async throws -> Evaluation {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        do {
            let savedRemoteEntity = try await remoteDataSource.saveAsync(entity, authToken: authToken)
            return try await performAsyncTask { try self.localDataSource.save(savedRemoteEntity) }
        } catch { return try await performAsyncTask { try self.localDataSource.save(entity) } }
    }

    public func saveAllAsync(_ entities: [Evaluation]) async throws -> [Evaluation] {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        do {
            let savedRemoteEntities = try await remoteDataSource.saveAllAsync(entities, authToken: authToken)
            _ = try await performAsyncTask { try self.localDataSource.saveAll(savedRemoteEntities) }
            return savedRemoteEntities
        } catch { 
            _ = try await performAsyncTask { try self.localDataSource.saveAll(entities) }
            return entities 
        }
    }

    public func deleteAsync(_ id: UUID) async throws -> Bool {
        guard let authToken = getAuthToken() else { throw RepositoryError.authenticationRequired }
        let success = try await remoteDataSource.deleteAsync(id, authToken: authToken)
        if success { _ = try await performAsyncTask { try self.localDataSource.delete(id) } }
        return success
    }
}

// Ensure RepositoryError is defined as in the previous attempt or available globally.
// public enum RepositoryError: Error, LocalizedError {
//    case authenticationRequired
//    case entityNotFound
//    case networkError(Error)
//    case localStoreError(Error)
//    case unknown
//    case custom(String)
//
//    public var errorDescription: String? {
//        switch self {
//            case .authenticationRequired: return "Authentication is required for this operation."
//            case .entityNotFound: return "The requested entity was not found."
//            case .networkError(let err): return "Network error: \(err.localizedDescription)"
//            case .localStoreError(let err): return "Local storage error: \(err.localizedDescription)"
//            case .unknown: return "An unknown repository error occurred."
//            case .custom(let message): return message
//        }
//    }
// }

// Ensure EvaluationLocalDataSource has the methods assumed by this repository, e.g.:
// func saveAll(_ evaluations: [Evaluation]) -> AnyPublisher<[Evaluation], Error>
// func getAllElements() -> AnyPublisher<[Evaluation], Error>
// func getById(_ id: UUID) -> AnyPublisher<Evaluation?, Error>
// func save(_ evaluation: Evaluation) -> AnyPublisher<Evaluation, Error>
// func delete(_ id: UUID) -> AnyPublisher<Void, Error> // or AnyPublisher<Bool, Error>
// func deleteAll() -> AnyPublisher<Void, Error>
// And their async counterparts like getAllElementsAsync(), saveAllAsync(), getByIdAsync(), saveAsync(), deleteAsync(), deleteAllAsync()
// Also, ensure Evaluation model is correctly defined and Decodable/Encodable as needed.