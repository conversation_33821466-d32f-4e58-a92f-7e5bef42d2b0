import Foundation
import Combine

/// 评估视图模型，处理评估流程逻辑
class EvaluationViewModel: ObservableObject {
    // 管理器
    private let evaluationManager: EvaluationManager
    private var cancellables = Set<AnyCancellable>()

    // 评估状态
    enum EvaluationState: Equatable {
        case loading
        case intro
        case inProgress
        case submitting
        case completing
        case completed
        case error(String)

        static func == (lhs: EvaluationState, rhs: EvaluationState) -> Bool {
            switch (lhs, rhs) {
            case (.loading, .loading),
                 (.intro, .intro),
                 (.inProgress, .inProgress),
                 (.submitting, .submitting),
                 (.completing, .completing),
                 (.completed, .completed):
                return true
            case (.error(let lhsError), .error(let rhsError)):
                return lhsError == rhsError
            default:
                return false
            }
        }
    }

    // 发布的属性
    @Published var state: EvaluationState = .loading
    @Published var evaluation: Evaluation?
    @Published var currentSection: EvaluationSection?
    @Published var currentQuestion: EvaluationQuestion?
    @Published var currentQuestionIndex: Int = 0
    @Published var totalQuestions: Int = 0
    @Published var progress: Double = 0.0
    @Published var remainingTime: TimeInterval?
    @Published var resultID: UUID?
    @Published var evaluationResult: EvaluationResult?
    @Published var errorMessage: String?
    @Published var showResultView: Bool = false

    // 用户回答相关属性
    @Published var selectedAnswer: String?
    @Published var textAnswer: String = ""

    // 计时器
    private var timer: Timer?

    /// 初始化
    /// - Parameter evaluationManager: 评估管理器
    init(evaluationManager: EvaluationManager? = nil) {
        print("📊 [EvaluationViewModel] 初始化开始")

        // 尝试从容器解析 EvaluationManager，如果失败则使用默认值
        if let evaluationManager = evaluationManager {
            self.evaluationManager = evaluationManager
        } else {
            do {
                self.evaluationManager = try DependencyContainer.shared.tryResolve(EvaluationManager.self)
                print("📊 [EvaluationViewModel] 成功从容器解析 EvaluationManager")
            } catch {
                print("📊 [EvaluationViewModel] 无法从容器解析 EvaluationManager，使用默认实例: \(error)")
                self.evaluationManager = EvaluationManager.shared
            }
        }

        setupSubscriptions()
    }

    /// 设置订阅
    private func setupSubscriptions() {
        // 监听评估管理器的状态变化
        evaluationManager.$availableEvaluations
            .receive(on: DispatchQueue.main)
            .sink { [weak self] evaluations in
                if let evaluation = evaluations.first {
                    self?.handleEvaluationLoaded(evaluation)
                }
            }
            .store(in: &cancellables)

        evaluationManager.$currentEvaluation
            .receive(on: DispatchQueue.main)
            .sink { [weak self] evaluation in
                if let evaluation = evaluation {
                    self?.handleEvaluationLoaded(evaluation)
                }
            }
            .store(in: &cancellables)

        evaluationManager.$evaluationResult
            .receive(on: DispatchQueue.main)
            .sink { [weak self] result in
                if let result = result {
                    self?.evaluationResult = result
                    self?.resultID = result.id
                    self?.state = .completed
                    self?.showResultView = true
                }
            }
            .store(in: &cancellables)

        evaluationManager.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                if isLoading && self?.state != .submitting && self?.state != .completing {
                    self?.state = .loading
                }
            }
            .store(in: &cancellables)

        evaluationManager.$error
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                if let error = error {
                    self?.state = .error(error.localizedDescription)
                    self?.errorMessage = error.localizedDescription
                }
            }
            .store(in: &cancellables)
    }

    /// 处理评估加载完成
    private func handleEvaluationLoaded(_ evaluation: Evaluation) {
        self.evaluation = evaluation
        self.totalQuestions = evaluation.totalQuestions

        if evaluation.isCompleted {
            if evaluation.resultID == nil {
                self.resultID = evaluation.id
                self.state = .completed
                DispatchQueue.main.async {
                    self.showResultView = true
                }
            } else {
                self.state = .completed
                self.resultID = evaluation.resultID
            }
        } else if evaluation.isStarted {
            self.setupCurrentQuestion()
            self.startTimer()
            self.state = .inProgress
        } else {
            if !evaluation.sections.isEmpty && !evaluation.sections[0].questions.isEmpty {
                self.currentSection = evaluation.sections[0]
                self.currentQuestion = evaluation.sections[0].questions[0]
                self.currentQuestionIndex = 0
                self.updateProgress()
            }
            self.state = .intro
        }
    }

    /// 加载可用评估
    func loadAvailableEvaluation() {
        state = .loading
        evaluationManager.loadAvailableEvaluations()
    }

    /// 重置评估状态
    private func resetState() {
        // 重置所有状态变量
        evaluation = nil
        currentSection = nil
        currentQuestion = nil
        currentQuestionIndex = 0
        totalQuestions = 0
        progress = 0.0
        remainingTime = nil
        resultID = nil
        evaluationResult = nil
        errorMessage = nil
        showResultView = false
        selectedAnswer = nil
        textAnswer = ""

        // 停止计时器
        stopTimer()

        // 设置初始状态
        state = .loading

        print("评估状态已重置")
    }

    /// 加载评估详情
    /// - Parameter id: 评估ID
    func loadEvaluation(id: UUID) {
        // 重置状态
        resetState()

        print("开始加载评估详情，ID: \(id)")
        evaluationManager.loadEvaluation(id: id)
    }

    /// 创建离线评估（当网络请求失败时使用）
    /// - Parameter id: 评估ID
    private func createOfflineEvaluation(id: UUID) {
        print("创建离线评估，ID: \(id)")
        print("DEBUG: createOfflineEvaluation - 调用堆栈: \(Thread.callStackSymbols)")

        // 创建一个简单的离线评估
        let section = EvaluationSection(
            id: UUID(),
            evaluationID: id,
            title: "词汇测试",
            skill: "vocabulary",
            weight: 100,
            questions: [
                EvaluationQuestion(
                    id: UUID(),
                    sectionID: UUID(),
                    type: .multipleChoice,
                    content: "选择\"苹果\"的正确翻译",
                    options: ["Apple", "Orange", "Banana", "Pear"],
                    correctAnswer: "Apple",
                    userAnswer: nil,
                    isCorrect: nil,
                    points: 10,
                    explanation: "苹果的英文是Apple",
                    audioURL: nil,
                    imageURL: nil
                ),
                EvaluationQuestion(
                    id: UUID(),
                    sectionID: UUID(),
                    type: .multipleChoice,
                    content: "选择\"你好\"的正确翻译",
                    options: ["Hello", "Goodbye", "Thank you", "Sorry"],
                    correctAnswer: "Hello",
                    userAnswer: nil,
                    isCorrect: nil,
                    points: 10,
                    explanation: "你好的英文是Hello",
                    audioURL: nil,
                    imageURL: nil
                )
            ]
        )

        let offlineEvaluation = Evaluation(
            id: id,
            userID: UUID(),
            type: .placement,
            category: .vocabulary,
            status: .available,
            title: "离线中文水平测试",
            description: "测试您的中文词汇和语法能力",
            passingScore: 60,
            duration: 30,
            totalQuestions: 2,
            sections: [section],
            isStarted: false,
            isCompleted: false,
            createdAt: Date(),
            updatedAt: Date()
        )

        print("DEBUG: createOfflineEvaluation - 创建了离线评估: \(offlineEvaluation)")
        print("DEBUG: createOfflineEvaluation - 部分数量: \(offlineEvaluation.sections.count)")
        print("DEBUG: createOfflineEvaluation - 问题总数: \(offlineEvaluation.totalQuestions)")
        print("DEBUG: createOfflineEvaluation - 评估状态: isStarted=\(offlineEvaluation.isStarted), isCompleted=\(offlineEvaluation.isCompleted)")

        // 更新视图模型
        DispatchQueue.main.async { [weak self] in
            print("DEBUG: createOfflineEvaluation - 异步更新视图模型")
            self?.evaluation = offlineEvaluation
            self?.totalQuestions = offlineEvaluation.totalQuestions
            self?.state = .intro
            print("DEBUG: createOfflineEvaluation - 设置状态为 .intro")
            print("DEBUG: 视图状态: isLoading=\(self?.state == .loading), showError=\(self?.isErrorState(self?.state) ?? false)")
        }
    }

    /// 开始评估
    func startEvaluation() {
        guard let evaluation = evaluation, !evaluation.sections.isEmpty else {
            state = .error("评估不包含任何问题")
            errorMessage = "评估不包含任何问题"
            return
        }

        print("开始评估，ID: \(evaluation.id)")

        // 设置当前部分和问题
        if let firstSection = evaluation.sections.first {
            currentSection = firstSection
            if let firstQuestion = firstSection.questions.first {
                currentQuestion = firstQuestion
            }
        }

        currentQuestionIndex = 0
        progress = 0.0

        // 调用评估管理器开始评估
        evaluationManager.startEvaluation(id: evaluation.id)
    }

    /// 设置当前问题
    private func setupCurrentQuestion() {
        guard let evaluation = evaluation else {
            print("无法设置当前问题，评估为nil")
            print("DEBUG: setupCurrentQuestion - 评估为nil，无法设置当前问题")
            return
        }

        print("设置当前问题，评估ID: \(evaluation.id)")
        print("评估包含 \(evaluation.sections.count) 个部分，总共 \(evaluation.totalQuestions) 个问题")
        print("DEBUG: setupCurrentQuestion - 调用堆栈: \(Thread.callStackSymbols)")
        print("DEBUG: setupCurrentQuestion - 评估状态: isStarted=\(evaluation.isStarted), isCompleted=\(evaluation.isCompleted)")
        print("DEBUG: setupCurrentQuestion - 当前视图状态: \(state)")

        // 找到第一个未完成的问题
        for (sectionIndex, section) in evaluation.sections.enumerated() {
            print("检查部分 \(sectionIndex+1): \(section.title)，包含 \(section.questions.count) 个问题")
            print("DEBUG: setupCurrentQuestion - 部分ID: \(section.id)")

            for (questionIndex, question) in section.questions.enumerated() {
                print("检查问题 \(questionIndex+1): \(question.content)")
                print("DEBUG: setupCurrentQuestion - 问题ID: \(question.id), 用户答案: \(question.userAnswer ?? "nil")")

                if question.userAnswer == nil {
                    print("找到未回答的问题：\(question.content)")
                    print("DEBUG: setupCurrentQuestion - 找到未回答的问题，设置为当前问题")
                    currentSection = section
                    currentQuestion = question

                    // 计算当前问题索引
                    var index = 0
                    for i in 0..<sectionIndex {
                        index += evaluation.sections[i].questions.count
                    }
                    index += questionIndex
                    currentQuestionIndex = index
                    print("DEBUG: setupCurrentQuestion - 设置当前问题索引: \(currentQuestionIndex)")

                    // 打印当前问题的详细信息
                    print("设置当前问题：")
                    print("- 内容: \(question.content)")
                    print("- 类型: \(question.type.rawValue)")
                    if let options = question.options {
                        print("- 选项数量: \(options.count)")
                        for (i, option) in options.enumerated() {
                            print("  选项 \(i+1): \(option)")
                        }
                    } else {
                        print("- 没有选项")
                    }

                    updateProgress()
                    print("DEBUG: setupCurrentQuestion - 更新进度: \(progress)")
                    print("DEBUG: 视图状态: isLoading=\(state == .loading), showError=\(isErrorState(state))")
                    return
                }
            }
        }

        // 如果所有问题都已完成，设置为最后一个问题
        if let lastSection = evaluation.sections.last, let lastQuestion = lastSection.questions.last {
            print("所有问题都已回答，设置为最后一个问题")
            print("DEBUG: setupCurrentQuestion - 所有问题都已回答，设置为最后一个问题")
            currentSection = lastSection
            currentQuestion = lastQuestion
            currentQuestionIndex = totalQuestions - 1
            updateProgress()
            print("DEBUG: setupCurrentQuestion - 更新进度: \(progress)")

            // 重置最后一个问题的用户答案，以便用户可以重新回答
            var updatedEvaluation = evaluation
            for i in 0..<updatedEvaluation.sections.count {
                for j in 0..<updatedEvaluation.sections[i].questions.count {
                    // 重置所有问题的用户答案
                    updatedEvaluation.sections[i].questions[j].userAnswer = nil
                    updatedEvaluation.sections[i].questions[j].isCorrect = nil
                }
            }
            self.evaluation = updatedEvaluation
            print("已重置所有问题的用户答案")
            print("DEBUG: setupCurrentQuestion - 已重置所有问题的用户答案")
            print("DEBUG: 视图状态: isLoading=\(state == .loading), showError=\(isErrorState(state))")
        } else {
            print("警告：评估没有部分")
            print("DEBUG: setupCurrentQuestion - 警告：评估没有部分")
        }
    }

    /// 更新进度
    private func updateProgress() {
        guard totalQuestions > 0 else { return }
        progress = Double(currentQuestionIndex) / Double(totalQuestions)
    }

    /// 提交答案
    /// - Parameter answer: 用户答案（可选，如果不提供则根据问题类型使用selectedAnswer或textAnswer）
    func submitAnswer(answer: String? = nil) {
        guard let evaluation = evaluation,
              let currentQuestion = currentQuestion else {
            return
        }

        // 确定要提交的答案
        var finalAnswer: String

        if let providedAnswer = answer {
            finalAnswer = providedAnswer
        } else {
            // 根据问题类型选择答案
            switch currentQuestion.type {
            case .multipleChoice:
                guard let selectedAnswer = selectedAnswer else { return }
                finalAnswer = selectedAnswer
            case .fillIn:
                finalAnswer = textAnswer.trimmingCharacters(in: .whitespacesAndNewlines)
                if finalAnswer.isEmpty { return }
            default:
                guard let selectedAnswer = selectedAnswer else { return }
                finalAnswer = selectedAnswer
            }
        }

        state = .submitting

        // 调用评估管理器提交答案
        evaluationManager.submitAnswer(
            evaluationId: evaluation.id,
            questionId: currentQuestion.id,
            answer: finalAnswer
        )

        // 移动到下一个问题
        moveToNextQuestion()
    }

    /// 移动到下一个问题
    func moveToNextQuestion() {
        guard let evaluation = evaluation,
              let currentSection = currentSection,
              let sectionIndex = evaluation.sections.firstIndex(where: { $0.id == currentSection.id }) else {
            print("DEBUG: moveToNextQuestion - 评估、当前部分为nil或找不到部分索引")
            return
        }

        print("DEBUG: moveToNextQuestion - 开始移动到下一个问题")
        print("DEBUG: moveToNextQuestion - 调用堆栈: \(Thread.callStackSymbols)")
        print("DEBUG: moveToNextQuestion - 当前部分: \(currentSection.title), 索引: \(sectionIndex)")
        print("DEBUG: moveToNextQuestion - 当前问题索引: \(currentQuestionIndex), 总问题数: \(totalQuestions)")

        // 重置答案状态
        selectedAnswer = nil
        textAnswer = ""
        print("DEBUG: moveToNextQuestion - 重置答案状态")

        // 在当前部分中查找下一个问题
        if let currentQuestion = currentQuestion,
           let questionIndex = evaluation.sections[sectionIndex].questions.firstIndex(where: { $0.id == currentQuestion.id }) {

            print("DEBUG: moveToNextQuestion - 当前问题: \(currentQuestion.content), 索引: \(questionIndex)")

            if questionIndex + 1 < evaluation.sections[sectionIndex].questions.count {
                // 移动到当前部分的下一个问题
                let nextQuestion = evaluation.sections[sectionIndex].questions[questionIndex + 1]
                print("DEBUG: moveToNextQuestion - 移动到当前部分的下一个问题: \(nextQuestion.content)")
                self.currentQuestion = nextQuestion
                currentQuestionIndex += 1
                updateProgress()
                state = .inProgress
                print("DEBUG: moveToNextQuestion - 更新进度: \(progress)")
                print("DEBUG: moveToNextQuestion - 设置状态为 .inProgress")
                print("DEBUG: 视图状态: isLoading=\(state == .loading), showError=\(isErrorState(state))")
                return
            } else {
                print("DEBUG: moveToNextQuestion - 当前部分的所有问题已完成")
            }
        } else {
            print("DEBUG: moveToNextQuestion - 找不到当前问题或问题索引")
        }

        // 如果当前部分已完成，移动到下一个部分
        if sectionIndex + 1 < evaluation.sections.count {
            let nextSection = evaluation.sections[sectionIndex + 1]
            print("DEBUG: moveToNextQuestion - 移动到下一个部分: \(nextSection.title)")
            self.currentSection = nextSection

            if let firstQuestion = nextSection.questions.first {
                print("DEBUG: moveToNextQuestion - 设置为下一个部分的第一个问题: \(firstQuestion.content)")
                self.currentQuestion = firstQuestion
            } else {
                print("DEBUG: moveToNextQuestion - 警告：下一个部分没有问题")
            }

            currentQuestionIndex += 1
            updateProgress()
            state = .inProgress
            print("DEBUG: moveToNextQuestion - 更新进度: \(progress)")
            print("DEBUG: moveToNextQuestion - 设置状态为 .inProgress")
            print("DEBUG: 视图状态: isLoading=\(state == .loading), showError=\(isErrorState(state))")
            return
        } else {
            print("DEBUG: moveToNextQuestion - 所有部分都已完成")
        }

        // 检查是否所有问题都已回答
        var allQuestionsAnswered = true
        var answeredCount = 0

        for section in evaluation.sections {
            for question in section.questions {
                if question.userAnswer != nil {
                    answeredCount += 1
                } else {
                    allQuestionsAnswered = false
                }
            }
        }

        print("DEBUG: moveToNextQuestion - 已回答问题数: \(answeredCount)/\(totalQuestions), 所有问题已回答: \(allQuestionsAnswered)")

        if allQuestionsAnswered || answeredCount >= totalQuestions {
            // 如果所有问题都已回答，完成评估
            print("DEBUG: moveToNextQuestion - 所有问题都已回答，准备完成评估")
            completeEvaluation()
        } else {
            // 如果还有未回答的问题，重新设置当前问题
            print("DEBUG: moveToNextQuestion - 还有未回答的问题，重新设置当前问题")
            setupCurrentQuestion()
        }
    }

    /// 移动到上一个问题
    func previousQuestion() {
        guard let evaluation = evaluation,
              let currentSection = currentSection,
              let sectionIndex = evaluation.sections.firstIndex(where: { $0.id == currentSection.id }) else {
            print("DEBUG: previousQuestion - 评估、当前部分为nil或找不到部分索引")
            return
        }

        print("DEBUG: previousQuestion - 开始移动到上一个问题")
        print("DEBUG: previousQuestion - 当前部分: \(currentSection.title), 索引: \(sectionIndex)")
        print("DEBUG: previousQuestion - 当前问题索引: \(currentQuestionIndex), 总问题数: \(totalQuestions)")

        // 重置答案状态
        selectedAnswer = nil
        textAnswer = ""

        // 在当前部分中查找上一个问题
        if let currentQuestion = currentQuestion,
           let questionIndex = evaluation.sections[sectionIndex].questions.firstIndex(where: { $0.id == currentQuestion.id }) {

            if questionIndex > 0 {
                // 移动到当前部分的上一个问题
                let previousQuestion = evaluation.sections[sectionIndex].questions[questionIndex - 1]
                self.currentQuestion = previousQuestion
                currentQuestionIndex -= 1
                updateProgress()
                state = .inProgress
                return
            }
        }

        // 如果当前部分没有上一个问题，移动到上一个部分
        if sectionIndex > 0 {
            let previousSection = evaluation.sections[sectionIndex - 1]
            self.currentSection = previousSection

            if let lastQuestion = previousSection.questions.last {
                self.currentQuestion = lastQuestion
                currentQuestionIndex -= 1
                updateProgress()
                state = .inProgress
            }
        }
    }

    /// 移动到下一个问题
    func nextQuestion() {
        moveToNextQuestion()
    }

    /// 完成评估
    func completeEvaluation() {
        guard let evaluation = evaluation else {
            return
        }

        state = .completing
        stopTimer()

        // 调用评估管理器完成评估
        evaluationManager.completeEvaluation(id: evaluation.id)
    }

    /// 开始计时器
    private func startTimer() {
        // 每秒更新剩余时间
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateRemainingTime()
        }
    }

    /// 更新剩余时间
    private func updateRemainingTime() {
        remainingTime = evaluation?.remainingTime()

        // 如果时间到了，自动完成评估
        if let remainingTime = remainingTime, remainingTime <= 0 {
            completeEvaluation()
        }
    }

    /// 停止计时器
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    /// 选择答案（多选题）
    /// - Parameter answer: 选择的答案
    func selectAnswer(_ answer: String) {
        guard currentQuestion?.userAnswer == nil else { return }
        selectedAnswer = answer

        // 不再自动提交答案，让用户点击"提交答案"按钮
        // submitAnswer(answer: answer)
        print("已选择答案: \(answer)，请点击提交按钮确认")
    }

    /// 获取评估结果
    /// - Parameter evaluationId: 评估ID
    func fetchEvaluationResults(evaluationId: UUID) {
        print("获取评估结果，评估ID: \(evaluationId)")

        // 调用评估管理器获取结果
        evaluationManager.loadEvaluationResult(id: evaluationId)
    }

    deinit {
        stopTimer()
    }

    /// 检查状态是否为错误状态
    private func isErrorState(_ state: EvaluationState?) -> Bool {
        guard let state = state else { return false }
        if case .error(_) = state {
            return true
        }
        return false
    }
}
