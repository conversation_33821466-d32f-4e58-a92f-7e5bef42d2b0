import Foundation
import Combine

/// 评估入口视图模型，处理评估入口页面的业务逻辑
@MainActor
class EvaluationEntryViewModel: ObservableObject {
    // 管理器
    private let evaluationManager: EvaluationManager

    // 发布者
    @Published var evaluationHistory: [EvaluationResult] = []
    @Published var isLoading = false
    @Published var showError = false
    @Published var errorMessage = ""
    @Published var navigateToEvaluation = false
    @Published var evaluations: [Evaluation] = []
    @Published var error: Error?

    // 当前评估ID
    var currentEvaluationId: UUID?

    // 取消令牌
    private var cancellables = Set<AnyCancellable>()

    init(evaluationManager: EvaluationManager = DependencyContainer.shared.resolve(EvaluationManager.self)) {
        self.evaluationManager = evaluationManager
        setupSubscriptions()
    }

    /// 设置订阅
    private func setupSubscriptions() {
        // 监听可用评估
        evaluationManager.$availableEvaluations
            .receive(on: DispatchQueue.main)
            .sink { [weak self] evaluations in
                self?.evaluations = evaluations
            }
            .store(in: &cancellables)

        // 监听当前评估
        evaluationManager.$currentEvaluation
            .receive(on: DispatchQueue.main)
            .sink { [weak self] evaluation in
                if let evaluation = evaluation {
                    self?.currentEvaluationId = evaluation.id
                    self?.navigateToEvaluation = true
                }
            }
            .store(in: &cancellables)

        // 监听加载状态
        evaluationManager.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                self?.isLoading = isLoading
            }
            .store(in: &cancellables)

        // 监听错误
        evaluationManager.$error
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                if let error = error {
                    self?.showError = true
                    self?.errorMessage = error.localizedDescription
                    self?.error = error
                }
            }
            .store(in: &cancellables)
    }

    /// 加载评估历史
    func loadEvaluationHistory() {
        evaluationManager.loadEvaluationHistory()
    }

    /// 开始新评估
    func startNewEvaluation() {
        // 先加载可用评估
        evaluationManager.loadAvailableEvaluations()

        // 在订阅中处理评估选择逻辑
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }

            // 找到水平测试类型的评估
            if let placementEvaluation = self.evaluations.first(where: { $0.type == .placement }) {
                self.startEvaluation(id: placementEvaluation.id)
            } else {
                self.showError = true
                self.errorMessage = "未找到水平测试评估"
            }
        }
    }

    /// 开始特定评估
    /// - Parameter id: 评估ID
    private func startEvaluation(id: UUID) {
        evaluationManager.startEvaluation(id: id)
    }
}
