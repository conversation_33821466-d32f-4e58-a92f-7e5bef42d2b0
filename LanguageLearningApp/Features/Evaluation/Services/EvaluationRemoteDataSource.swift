import Foundation
import Combine

/// 评估远程数据源实现
public class EvaluationRemoteDataSource: EvaluationRemoteDataSourceProtocol {
    // 数据类型是评估
    public typealias T = Evaluation
    
    // 标识符类型是UUID
    public typealias ID = UUID
    
    // API客户端
    private let apiClient: APIClientProtocol
    
    // API端点
    private let endpoint = "/evaluations"
    
    // 单例实例
    public static let shared = EvaluationRemoteDataSource(apiClient: DependencyContainer.shared.resolve(APIClientProtocol.self))
    
    /// 初始化方法
    /// - Parameter apiClient: API客户端
    public init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }
    
    private func standardHeaders(authToken: String) -> [String: String] {
        return [
            "Authorization": "Bearer \\(authToken)",
            "Content-Type": "application/json",
            "Accept": "application/json"
        ]
    }
    
    /// 获取所有评估
    /// - Parameter authToken: 认证令牌
    /// - Returns: 包含评估列表的发布者
    public func getAll(authToken: String) -> AnyPublisher<[Evaluation], Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint)!
        let headers = standardHeaders(authToken: authToken)
        return Future<[Evaluation], Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: headers)
                    let response = try JSONDecoder().decode(EvaluationListResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 根据ID获取评估
    /// - Parameters:
    ///   - id: 评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 包含评估的发布者
    public func getById(_ id: ID, authToken: String) -> AnyPublisher<Evaluation, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)")!
        let headers = standardHeaders(authToken: authToken)
        return Future<Evaluation, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: headers)
                    let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 保存评估
    /// - Parameters:
    ///   - entity: 要保存的评估
    ///   - authToken: 认证令牌
    /// - Returns: 包含保存后评估的发布者
    public func save(_ entity: Evaluation, authToken: String) -> AnyPublisher<Evaluation, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(entity.id.uuidString)")!
        let headers = standardHeaders(authToken: authToken)
        
        // Construct body carefully from entity properties
        let sectionsData = entity.sections.map { section -> [String: Any] in
            let questionsData = section.questions.map { question -> [String: Any] in
                var qDict: [String: Any] = [
                    "id": question.id.uuidString,
                    "sectionID": question.sectionID.uuidString,
                    "text": question.content,
                    "questionType": question.type.rawValue,
                    "correctAnswer": question.correctAnswer,
                    "points": question.points
                ]
                qDict["options"] = (question.options as Any?) ?? NSNull()
                qDict["userAnswer"] = (question.userAnswer as Any?) ?? NSNull()
                qDict["isCorrect"] = (question.isCorrect as Any?) ?? NSNull()
                qDict["explanation"] = (question.explanation as Any?) ?? NSNull()
                qDict["audioURL"] = (question.audioURL?.absoluteString as Any?) ?? NSNull()
                qDict["imageURL"] = (question.imageURL?.absoluteString as Any?) ?? NSNull()
                return qDict
            }
            var sDict: [String: Any] = [
                "id": section.id.uuidString,
                "evaluationID": section.evaluationID.uuidString,
                "title": section.title,
                "skill": section.skill,
                "weight": section.weight,
                "questions": questionsData
            ]
            sDict["score"] = (section.score as Any?) ?? NSNull()
            return sDict
        }

        var bodyDict: [String: Any] = [
            "userID": entity.userID.uuidString,
            "type": entity.type.rawValue,
            "category": entity.category.rawValue,
            "status": entity.status.rawValue,
            "title": entity.title,
            "description": entity.description,
            "passingScore": entity.passingScore,
            "duration": entity.duration,
            "totalQuestions": entity.totalQuestions,
            "isStarted": entity.isStarted,
            "isCompleted": entity.isCompleted,
            "sections": sectionsData
        ]
        bodyDict["score"] = (entity.score as Any?) ?? NSNull()
        bodyDict["resultID"] = (entity.resultID?.uuidString as Any?) ?? NSNull()
        bodyDict["startedAt"] = (entity.startedAt?.timeIntervalSince1970 as Any?) ?? NSNull()
        bodyDict["completedAt"] = (entity.completedAt?.timeIntervalSince1970 as Any?) ?? NSNull()

        return Future<Evaluation, Error> { promise in
            Task {
                do {
                    let bodyData = try JSONSerialization.data(withJSONObject: bodyDict, options: .fragmentsAllowed)
                    let data = try await self.apiClient.post(url, headers: headers, body: bodyData)
                    let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 保存多个评估
    /// - Parameters:
    ///   - entities: 要保存的评估列表
    ///   - authToken: 认证令牌
    /// - Returns: 包含保存后评估列表的发布者
    public func saveAll(_ entities: [Evaluation], authToken: String) -> AnyPublisher<[Evaluation], Error> {
        let publishers = entities.map { save($0, authToken: authToken) }
        return Publishers.Sequence(sequence: publishers)
            .flatMap(maxPublishers: .max(5)) { $0 }
            .collect()
            .eraseToAnyPublisher()
    }
    
    /// 删除评估
    /// - Parameters:
    ///   - id: 要删除的评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 包含删除成功标志的发布者
    public func delete(_ id: ID, authToken: String) -> AnyPublisher<Bool, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)")!
        let headers = standardHeaders(authToken: authToken)
        return Future<Bool, Error> { promise in
            Task {
                do {
                    _ = try await self.apiClient.delete(url, headers: headers, body: nil)
                    promise(.success(true))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 开始评估
    /// - Parameters:
    ///   - id: 评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 包含更新后评估的发布者
    public func startEvaluation(evaluationId: UUID, authToken: String) -> AnyPublisher<Evaluation, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(evaluationId)/start")!
        let headers = standardHeaders(authToken: authToken)
        return Future<Evaluation, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: headers, body: nil)
                    let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 提交评估答案
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - questionId: 问题ID (String)
    ///   - answer: 用户答案 (EvaluationAnswer)
    ///   - authToken: 认证令牌
    /// - Returns: 包含更新后评估的发布者
    public func submitAnswer(evaluationId: UUID, questionId: String, answer: EvaluationAnswer, authToken: String) -> AnyPublisher<Evaluation, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(evaluationId.uuidString)/answer")!
        let headers = standardHeaders(authToken: authToken)
        let payload = AnswerSubmissionPayload(questionId: questionId, answer: answer, userId: nil)
        return Future<Evaluation, Error> { promise in
            Task {
                do {
                    let body = try JSONEncoder().encode(payload)
                    let data = try await self.apiClient.post(url, headers: headers, body: body)
                    let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 完成评估
    /// - Parameters:
    ///   - id: 评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 包含评估结果的发布者
    public func completeEvaluation(evaluationId: UUID, authToken: String) -> AnyPublisher<EvaluationResult, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(evaluationId)/complete")!
        let headers = standardHeaders(authToken: authToken)
        return Future<EvaluationResult, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: headers, body: nil)
                    let response = try JSONDecoder().decode(EvaluationResult.self, from: data)
                    promise(.success(response))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 获取用户评估历史
    /// - Parameters:
    ///   - userID: 用户ID
    ///   - authToken: 认证令牌
    /// - Returns: 包含评估结果列表的发布者
    public func getUserEvaluationHistory(userId: String, authToken: String) -> AnyPublisher<[EvaluationResult], Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "/users/\(userId)/evaluations")!
        let headers = standardHeaders(authToken: authToken)
        return Future<[EvaluationResult], Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: headers)
                    let response = try JSONDecoder().decode([EvaluationResult].self, from: data)
                    promise(.success(response))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 获取评估结果
    /// - Parameters:
    ///   - id: 评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 包含评估结果的发布者
    public func getEvaluationResult(evaluationId: UUID, authToken: String) -> AnyPublisher<EvaluationResult, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(evaluationId)/result")!
        let headers = standardHeaders(authToken: authToken)
        return Future<EvaluationResult, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: headers)
                    let response = try JSONDecoder().decode(EvaluationResult.self, from: data)
                    promise(.success(response))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 创建新评估
    /// - Returns: 包含新创建评估ID的发布者
    public func createEvaluation() -> AnyPublisher<UUID, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint)!
        return Future<UUID, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: [:], body: nil)
                    let response = try JSONDecoder().decode(CreationResponse.self, from: data)
                    if let id = UUID(uuidString: response.id) {
                        promise(.success(id))
                    } else {
                        promise(.failure(AppError.invalidResponse))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    // MARK: - Async Methods Implementation
    
    /// 异步获取所有评估
    /// - Parameter authToken: 认证令牌
    /// - Returns: 评估列表
    public func getAllAsync(authToken: String) async throws -> [Evaluation] {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint)!
        let headers = standardHeaders(authToken: authToken)
        let data = try await apiClient.get(url, headers: headers)
        let response = try JSONDecoder().decode(EvaluationListResponse.self, from: data)
        return response.data
    }

    /// 异步根据ID获取评估
    /// - Parameters:
    ///   - id: 评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 评估
    public func getByIdAsync(_ id: ID, authToken: String) async throws -> Evaluation {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)")!
        let data = try await apiClient.get(url, headers: standardHeaders(authToken: authToken))
        let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
        return response.data
    }

    /// 异步保存评估
    /// - Parameters:
    ///   - entity: 要保存的评估
    ///   - authToken: 认证令牌
    /// - Returns: 保存后的评估
    public func saveAsync(_ entity: Evaluation, authToken: String) async throws -> Evaluation {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(entity.id.uuidString)")!
        let headers = standardHeaders(authToken: authToken)
        
        let sectionsData = entity.sections.map { section -> [String: Any] in
            let questionsData = section.questions.map { question -> [String: Any] in
                var qDict: [String: Any] = [
                    "id": question.id.uuidString,
                    "sectionID": question.sectionID.uuidString,
                    "text": question.content,
                    "questionType": question.type.rawValue,
                    "correctAnswer": question.correctAnswer,
                    "points": question.points,
                ]
                qDict["options"] = (question.options as Any?) ?? NSNull()
                qDict["userAnswer"] = (question.userAnswer as Any?) ?? NSNull()
                qDict["isCorrect"] = (question.isCorrect as Any?) ?? NSNull()
                qDict["explanation"] = (question.explanation as Any?) ?? NSNull()
                qDict["audioURL"] = (question.audioURL?.absoluteString as Any?) ?? NSNull()
                qDict["imageURL"] = (question.imageURL?.absoluteString as Any?) ?? NSNull()
                return qDict
            }
            var sDict: [String: Any] = [
                "id": section.id.uuidString,
                "evaluationID": section.evaluationID.uuidString,
                "title": section.title,
                "skill": section.skill,
                "weight": section.weight,
                "questions": questionsData
            ]
            sDict["score"] = (section.score as Any?) ?? NSNull()
            return sDict
        }
        
        var bodyDict: [String: Any] = [
            "userID": entity.userID.uuidString,
            "type": entity.type.rawValue,
            "category": entity.category.rawValue,
            "status": entity.status.rawValue,
            "title": entity.title,
            "description": entity.description,
            "passingScore": entity.passingScore,
            "duration": entity.duration,
            "totalQuestions": entity.totalQuestions,
            "isStarted": entity.isStarted,
            "isCompleted": entity.isCompleted,
            "sections": sectionsData
        ]
        bodyDict["score"] = (entity.score as Any?) ?? NSNull()
        bodyDict["resultID"] = (entity.resultID?.uuidString as Any?) ?? NSNull()
        bodyDict["startedAt"] = (entity.startedAt?.timeIntervalSince1970 as Any?) ?? NSNull()
        bodyDict["completedAt"] = (entity.completedAt?.timeIntervalSince1970 as Any?) ?? NSNull()

        let bodyData = try JSONSerialization.data(withJSONObject: bodyDict, options: .fragmentsAllowed)
        let data = try await apiClient.post(url, headers: headers, body: bodyData)
        let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
        return response.data
    }
    
    /// 异步保存多个评估
    /// - Parameters:
    ///   - entities: 要保存的评估列表
    ///   - authToken: 认证令牌
    /// - Returns: 保存后的评估列表
    public func saveAllAsync(_ entities: [Evaluation], authToken: String) async throws -> [Evaluation] {
        var savedEntities: [Evaluation] = []
        for entity in entities {
            let savedEntity = try await saveAsync(entity, authToken: authToken)
            savedEntities.append(savedEntity)
        }
        return savedEntities
    }

    /// 异步删除评估
    /// - Parameters:
    ///   - id: 要删除的评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 删除成功标志
    public func deleteAsync(_ id: ID, authToken: String) async throws -> Bool {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id.uuidString)")!
        let headers = standardHeaders(authToken: authToken)
        _ = try await apiClient.delete(url, headers: headers, body: nil)
        return true
    }
    
    public func getAvailableEvaluationsAsync(userId: String, authToken: String) async throws -> [Evaluation] {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint + "?userId=\(userId)&status=available")!
        let headers = standardHeaders(authToken: authToken)
        let data = try await apiClient.get(url, headers: headers)
        let response = try JSONDecoder().decode(EvaluationListResponse.self, from: data)
        return response.data
    }

    /// 异步开始评估
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 更新后的评估
    public func startEvaluationAsync(evaluationId: UUID, authToken: String) async throws -> Evaluation {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(evaluationId.uuidString)/start")!
        let headers = standardHeaders(authToken: authToken)
        let data = try await apiClient.post(url, headers: headers, body: nil)
        let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
        return response.data
    }

    /// 异步完成评估
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 评估结果
    public func completeEvaluationAsync(evaluationId: UUID, authToken: String) async throws -> EvaluationResult {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(evaluationId.uuidString)/complete")!
        let headers = standardHeaders(authToken: authToken)
        let data = try await apiClient.post(url, headers: headers, body: nil)
        return try JSONDecoder().decode(EvaluationResult.self, from: data)
    }

    /// 异步获取用户评估历史
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - authToken: 认证令牌
    /// - Returns: 评估结果列表
    public func getUserEvaluationHistoryAsync(userId: String, authToken: String) async throws -> [EvaluationResult] {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "/users/\(userId)/evaluations")!
        let headers = standardHeaders(authToken: authToken)
        let data = try await apiClient.get(url, headers: headers)
        return try JSONDecoder().decode([EvaluationResult].self, from: data)
    }

    /// 异步获取评估结果
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - authToken: 认证令牌
    /// - Returns: 评估结果
    public func getEvaluationResultAsync(evaluationId: UUID, authToken: String) async throws -> EvaluationResult {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(evaluationId.uuidString)/result")!
        let headers = standardHeaders(authToken: authToken)
        let data = try await apiClient.get(url, headers: headers)
        return try JSONDecoder().decode(EvaluationResult.self, from: data)
    }

    /// 异步提交评估答案
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 用户答案
    ///   - userId: 用户ID
    ///   - authToken: 认证令牌
    /// - Returns: 更新后的评估
    public func submitAnswerAsync(evaluationId: UUID, questionId: String, answer: EvaluationAnswer, userId: String, authToken: String) async throws -> Evaluation {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(evaluationId.uuidString)/answer")!
        let headers = standardHeaders(authToken: authToken)
        let payload = AnswerSubmissionPayload(questionId: questionId, answer: answer, userId: userId)
        let body = try JSONEncoder().encode(payload)
        let data = try await apiClient.post(url, headers: headers, body: body)
        let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
        return response.data
    }
}

/// 创建响应
private struct CreationResponse: Codable {
    let id: String
    let success: Bool
}

// Define this struct based on what your API expects for submitting an answer.
struct AnswerSubmissionPayload: Codable {
    let questionId: String
    let answer: EvaluationAnswer
    let userId: String?
}

// Helper Response Structs (Ensure these are defined, possibly globally or scoped if not)
// ... existing code ... 