import Foundation
import SwiftUI
import Combine
import Network

/// 评估服务错误
public enum EvaluationServiceError: LocalizedError {
    case networkError(String)
    case decodingError(Error)
    case invalidResponse
    case evaluationNotFound

    public var errorDescription: String? {
        switch self {
        case .networkError(let message):
            return "网络错误: \(message)"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .invalidResponse:
            return "无效的响应"
        case .evaluationNotFound:
            return "评估未找到"
        }
    }
}

/// 评估服务
public class EvaluationService: EvaluationServiceProtocol {
    // API客户端
    private let apiClient: APIClientProtocol
    
    // API端点
    private let endpoint = "/evaluations"
    
    // 网络状态
    public var isNetworkAvailable: Bool {
        return NetworkMonitor.shared.isConnected
    }
    
    public init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }
    
    /// 获取可用评估列表
    public func getAvailableEvaluations() -> AnyPublisher<[Evaluation], Error> {
        return getAllEvaluations()
    }
    
    /// 获取评估详情
    public func getEvaluation(id: UUID) -> AnyPublisher<Evaluation, Error> {
        return getEvaluationById(id)
    }
    
    /// 开始评估
    public func startEvaluation(id: UUID) -> AnyPublisher<Evaluation, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)/start")!
        return Future<Evaluation, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: nil, body: nil)
                    let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 提交评估答案
    public func submitAnswer(id: UUID, questionId: UUID, answer: String) -> AnyPublisher<Evaluation, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)/answer")!
        let parameters: [String: Any] = [
            "questionId": questionId.uuidString,
            "answer": answer
        ]
        return Future<Evaluation, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: nil, body: try? JSONSerialization.data(withJSONObject: parameters))
                    let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 完成评估
    public func completeEvaluation(id: UUID) -> AnyPublisher<EvaluationResult, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)/complete")!
        return Future<EvaluationResult, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: nil, body: nil)
                    let response = try JSONDecoder().decode(EvaluationResultResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 获取用户评估历史
    public func getUserEvaluationHistory() -> AnyPublisher<[EvaluationResult], Error> {
        guard let userId = UserDefaults.standard.string(forKey: "userId"),
              let uuid = UUID(uuidString: userId) else {
            return Fail(error: EvaluationServiceError.invalidResponse).eraseToAnyPublisher()
        }
        return getUserEvaluations(uuid)
            .flatMap { evaluations -> AnyPublisher<[EvaluationResult], Error> in
                let resultPublishers = evaluations.compactMap { evaluation -> AnyPublisher<EvaluationResult, Error>? in
                    guard let resultId = evaluation.resultID else { return nil }
                    return self.getEvaluationResult(resultId)
                }
                return Publishers.MergeMany(resultPublishers)
                    .collect()
                    .eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
    }
    
    /// 获取评估结果
    public func getEvaluationResult(id: UUID) -> AnyPublisher<EvaluationResult, Error> {
        return getEvaluationResult(id)
    }
    
    /// 创建新评估
    public func createEvaluation() -> AnyPublisher<UUID, Error> {
        guard let userId = UserDefaults.standard.string(forKey: "userId"),
              let uuid = UUID(uuidString: userId) else {
            return Fail(error: EvaluationServiceError.invalidResponse).eraseToAnyPublisher()
        }
        return createInitialEvaluation(for: uuid)
            .map { $0.id }
            .eraseToAnyPublisher()
    }
    
    /// 获取所有评估
    /// - Returns: 包含评估列表的发布者
    public func getAllEvaluations() -> AnyPublisher<[Evaluation], Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint)!
        return Future<[Evaluation], Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: nil)
                    let response = try JSONDecoder().decode(EvaluationListResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 根据ID获取评估
    /// - Parameter id: 评估ID
    /// - Returns: 包含评估的发布者
    public func getEvaluationById(_ id: UUID) -> AnyPublisher<Evaluation, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)")!
        return Future<Evaluation, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: nil)
                    let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 创建评估
    /// - Parameter evaluation: 要创建的评估
    /// - Returns: 包含创建后评估的发布者
    public func createEvaluation(_ evaluation: Evaluation) -> AnyPublisher<Evaluation, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + endpoint)!
        let body = try? JSONEncoder().encode(evaluation)
        return Future<Evaluation, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: nil, body: body)
                    let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 更新评估
    /// - Parameter evaluation: 要更新的评估
    /// - Returns: 包含更新后评估的发布者
    public func updateEvaluation(_ evaluation: Evaluation) -> AnyPublisher<Evaluation, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(evaluation.id)")!
        let body = try? JSONEncoder().encode(evaluation)
        return Future<Evaluation, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: nil, body: body)
                    let response = try JSONDecoder().decode(EvaluationResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 删除评估
    /// - Parameter id: 要删除的评估ID
    /// - Returns: 包含删除成功标志的发布者
    public func deleteEvaluation(_ id: UUID) -> AnyPublisher<Bool, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)")!
        return Future<Bool, Error> { promise in
            Task {
                do {
                    _ = try await self.apiClient.post(url, headers: nil, body: nil)
                    promise(.success(true))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 获取用户评估
    /// - Parameter userId: 用户ID
    /// - Returns: 包含评估列表的发布者
    public func getUserEvaluations(_ userId: UUID) -> AnyPublisher<[Evaluation], Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/user/\(userId)")!
        return Future<[Evaluation], Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: nil)
                    let response = try JSONDecoder().decode(EvaluationListResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 获取评估结果
    /// - Parameter id: 评估ID
    /// - Returns: 包含评估结果的发布者
    public func getEvaluationResult(_ id: UUID) -> AnyPublisher<EvaluationResult, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)/result")!
        return Future<EvaluationResult, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.get(url, headers: nil)
                    let response = try JSONDecoder().decode(EvaluationResultResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 提交评估答案
    /// - Parameters:
    ///   - id: 评估ID
    ///   - answers: 答案列表
    /// - Returns: 包含评估结果的发布者
    public func submitEvaluationAnswers(_ id: UUID, answers: [EvaluationAnswer]) -> AnyPublisher<EvaluationResult, Error> {
        let url = URL(string: AppEnvironment.current.baseURL.absoluteString + "\(endpoint)/\(id)/submit")!
        let body = try? JSONEncoder().encode(answers)
        return Future<EvaluationResult, Error> { promise in
            Task {
                do {
                    let data = try await self.apiClient.post(url, headers: nil, body: body)
                    let response = try JSONDecoder().decode(EvaluationResultResponse.self, from: data)
                    promise(.success(response.data))
                } catch {
                    promise(.failure(error))
                }
            }
        }.eraseToAnyPublisher()
    }
    
    /// 创建初始评估
    /// - Parameter userId: 用户ID
    /// - Returns: 包含评估的发布者
    public func createInitialEvaluation(for userId: UUID) -> AnyPublisher<Evaluation, Error> {
        let evaluation = Evaluation(
            id: UUID(),
            userID: userId,
            type: .beginner,
            category: .vocabulary,
            status: .available,
            passingScore: 70,
            totalQuestions: 20,
            sections: [],
            isStarted: false,
            isCompleted: false,
            createdAt: Date(),
            updatedAt: Date()
        )
        return createEvaluation(evaluation)
    }
}
