import Foundation
import Combine
import SwiftUI

/// 评估管理器，处理评估相关的业务逻辑
public class EvaluationManager: ObservableObject, EvaluationManagerProtocol {
    // MARK: - Published Properties (EvaluationManagerProtocol)
    @Published public private(set) var availableEvaluations: [Evaluation] = []
    @Published public private(set) var currentEvaluation: Evaluation?
    @Published public private(set) var evaluationResult: EvaluationResult?
    @Published public private(set) var isLoading = false
    @Published public var error: Error?

    // MARK: - Additional Protocol Properties
    @Published public private(set) var evaluationResults: [EvaluationResult] = []
    @Published public private(set) var currentProgress: Double = 0.0
    @Published public private(set) var isEvaluating: Bool = false

    // MARK: - Private Properties
    private let repository: any EvaluationRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()
    private var currentUserID: UUID?

    // MARK: - Shared Instance
    public static let shared: EvaluationManager = {
        print("🏆 [EvaluationManager] 创建 shared 实例，使用默认 EvaluationRepository")
        // 直接使用 EvaluationRepository.shared 避免循环依赖
        // 在依赖注册完成后，repository 会通过其他方式正确设置
        return EvaluationManager(repository: EvaluationRepository.shared)
    }()

    // MARK: - Initialization
    public init(repository: any EvaluationRepositoryProtocol) {
        self.repository = repository
    }

    // MARK: - Public Methods
    public func setCurrentUser(userID: UUID) {
        self.currentUserID = userID
    }

    public func loadAvailableEvaluations() {
        guard let userID = currentUserID else {
            self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"])
            return
        }

        self.isLoading = true
        self.error = nil

        repository.getAvailableEvaluations()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.handleError(error, context: "加载可用评估")
                }
            } receiveValue: { [weak self] evaluations in
                self?.availableEvaluations = evaluations
            }
            .store(in: &cancellables)
    }

    /// 加载评估详情
    /// - Parameter id: 评估ID
    public func loadEvaluation(id: UUID) {
        self.isLoading = true
        self.error = nil

        repository.getById(id)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] evaluation in
                guard let self = self else { return }

                if let evaluation = evaluation {
                    self.currentEvaluation = evaluation
                } else {
                    self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法加载评估"])
                }
            }
            .store(in: &cancellables)
    }

    /// 开始评估
    /// - Parameter id: 评估ID
    public func startEvaluation(id: UUID) {
        self.isLoading = true
        self.error = nil

        repository.startEvaluation(id: id)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.handleError(error, context: "开始评估")
                }
            } receiveValue: { [weak self] evaluation in
                self?.currentEvaluation = evaluation
            }
            .store(in: &cancellables)
    }

    /// 提交答案
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 用户答案
    public func submitAnswer(evaluationId: UUID, questionId: UUID, answer: String) {
        self.isLoading = true
        self.error = nil

        repository.submitAnswer(id: evaluationId, questionId: questionId, answer: answer)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.handleError(error, context: "提交答案")
                }
            } receiveValue: { [weak self] evaluation in
                self?.currentEvaluation = evaluation
            }
            .store(in: &cancellables)
    }

    /// 完成评估
    /// - Parameter id: 评估ID
    public func completeEvaluation(id: UUID) {
        self.isLoading = true
        self.error = nil

        repository.completeEvaluation(id: id)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.handleError(error, context: "完成评估")
                }
            } receiveValue: { [weak self] result in
                self?.evaluationResult = result
            }
            .store(in: &cancellables)
    }

    /// 加载评估历史
    public func loadEvaluationHistory() {
        guard let userID = currentUserID else {
            self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"])
            return
        }

        self.isLoading = true
        self.error = nil

        repository.getUserEvaluationHistory(userID: userID)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.handleError(error, context: "加载评估历史")
                }
            } receiveValue: { [weak self] results in
                let evaluations = results.map { result -> Evaluation in
                    return Evaluation(
                        id: result.id,
                        userID: result.userID,
                        type: .placement,
                        category: .vocabulary,
                        status: .completed,
                        title: "评估结果",
                        description: "评估结果",
                        passingScore: 0,
                        duration: 0,
                        totalQuestions: 0,
                        sections: [],
                        isStarted: true,
                        isCompleted: true,
                        createdAt: result.createdAt,
                        updatedAt: result.completedAt
                    )
                }

                self?.availableEvaluations = evaluations
            }
            .store(in: &cancellables)
    }

    /// 加载评估结果
    /// - Parameter id: 评估ID
    public func loadEvaluationResult(id: UUID) {
        self.isLoading = true
        self.error = nil

        repository.getEvaluationResult(id: id)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] result in
                self?.evaluationResult = result
            }
            .store(in: &cancellables)
    }

    /// 创建新评估
    /// - Parameter completion: 完成回调
    public func createEvaluation(completion: @escaping (UUID?) -> Void) {
        repository.createEvaluation()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completionStatus in
                if case .failure(let error) = completionStatus {
                    self?.error = error
                    completion(nil)
                }
            } receiveValue: { evaluationId in
                completion(evaluationId)
            }
            .store(in: &cancellables)
    }

    /// 清除本地缓存
    public func clearLocalCache() {
        repository.clearLocalCache()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] success in
                if success {
                    self?.availableEvaluations = []
                    self?.currentEvaluation = nil
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 异步方法

    /// 异步加载可用评估列表
    /// - Returns: 是否成功
    @MainActor
    public func loadAvailableEvaluationsAsync() async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let evaluations = try await repository.getAvailableEvaluationsAsync()
            availableEvaluations = evaluations
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步加载评估详情
    /// - Parameter id: 评估ID
    /// - Returns: 是否成功
    @MainActor
    public func loadEvaluationAsync(id: UUID) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            if let evaluation = try await repository.getByIdAsync(id) {
                currentEvaluation = evaluation
                self.isLoading = false
                return true
            } else {
                self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法加载评估"])
                self.isLoading = false
                return false
            }
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步开始评估
    /// - Parameter id: 评估ID
    /// - Returns: 是否成功
    @MainActor
    public func startEvaluationAsync(id: UUID) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let evaluation = try await repository.startEvaluationAsync(id: id)
            currentEvaluation = evaluation
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步提交答案
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 用户答案
    /// - Returns: 是否成功
    @MainActor
    public func submitAnswerAsync(evaluationId: UUID, questionId: UUID, answer: String) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let evaluation = try await repository.submitAnswerAsync(id: evaluationId, questionId: questionId, answer: answer)
            currentEvaluation = evaluation
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步完成评估
    /// - Parameter id: 评估ID
    /// - Returns: 是否成功
    @MainActor
    public func completeEvaluationAsync(id: UUID) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let result = try await repository.completeEvaluationAsync(id: id)
            evaluationResult = result
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步加载评估历史
    /// - Returns: 是否成功
    @MainActor
    public func loadEvaluationHistoryAsync() async -> Bool {
        guard let userID = currentUserID else {
            self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"])
            return false
        }

        self.isLoading = true
        self.error = nil

        do {
            _ = try await repository.getUserEvaluationHistoryAsync(userID: userID)
            availableEvaluations = []
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步加载评估结果
    /// - Parameter id: 评估ID
    /// - Returns: 是否成功
    @MainActor
    public func loadEvaluationResultAsync(id: UUID) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let result = try await repository.getEvaluationResultAsync(id: id)
            evaluationResult = result
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步创建新评估
    /// - Returns: 新评估ID（如果成功）
    @MainActor
    public func createEvaluationAsync() async -> UUID? {
        do {
            return try await repository.createEvaluationAsync()
        } catch {
            self.error = error
            return nil
        }
    }

    /// 异步清除本地缓存
    /// - Returns: 是否成功
    @MainActor
    public func clearLocalCacheAsync() async -> Bool {
        do {
            let success = try await repository.clearLocalCacheAsync()
            if success {
                availableEvaluations = []
                currentEvaluation = nil
            }
            return success
        } catch {
            self.error = error
            return false
        }
    }

    /// 获取评估完成百分比
    /// - Parameter evaluation: 评估
    /// - Returns: 完成百分比
    public func getCompletionPercentage(for evaluation: Evaluation) -> Double {
        return evaluation.completionPercentage
    }

    /// 获取评估剩余时间（格式化）
    /// - Parameter evaluation: 评估
    /// - Returns: 格式化的剩余时间
    public func getRemainingTimeFormatted(for evaluation: Evaluation) -> String {
        guard let remainingSeconds = evaluation.remainingTime() else {
            return "已完成"
        }

        let minutes = Int(remainingSeconds) / 60
        let seconds = Int(remainingSeconds) % 60

        return String(format: "%02d:%02d", minutes, seconds)
    }

    /// 检查评估是否已完成所有问题
    /// - Parameter evaluation: 评估
    /// - Returns: 是否完成所有问题
    public func hasAnsweredAllQuestions(for evaluation: Evaluation) -> Bool {
        return evaluation.completedQuestionsCount >= evaluation.totalQuestions
    }
}

// MARK: - EvaluationManagerProtocol Implementation
extension EvaluationManager {
    /// 获取可用评估列表 (async)
    public func getAvailableEvaluations() async throws -> [Evaluation] {
        return try await withCheckedThrowingContinuation { continuation in
            repository.getAvailableEvaluations()
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                    },
                    receiveValue: { evaluations in
                        continuation.resume(returning: evaluations)
                    }
                )
                .store(in: &cancellables)
        }
    }

    /// 获取评估详情 (async)
    public func getEvaluationDetails(id: UUID) async throws -> Evaluation {
        return try await withCheckedThrowingContinuation { continuation in
            repository.getById(id)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                    },
                    receiveValue: { evaluation in
                        if let evaluation = evaluation {
                            continuation.resume(returning: evaluation)
                        } else {
                            continuation.resume(throwing: NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "评估未找到"]))
                        }
                    }
                )
                .store(in: &cancellables)
        }
    }

    /// 开始评估 (async)
    public func startEvaluation(id: UUID) async throws {
        isEvaluating = true
        currentProgress = 0.0

        return try await withCheckedThrowingContinuation { continuation in
            repository.startEvaluation(id: id)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case .failure(let error) = completion {
                            self?.isEvaluating = false
                            continuation.resume(throwing: error)
                        } else {
                            continuation.resume()
                        }
                    },
                    receiveValue: { [weak self] evaluation in
                        self?.currentEvaluation = evaluation
                    }
                )
                .store(in: &cancellables)
        }
    }

    /// 提交答案 (async)
    public func submitAnswer(evaluationId: UUID, questionId: UUID, answer: String) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            repository.submitAnswer(id: evaluationId, questionId: questionId, answer: answer)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        } else {
                            continuation.resume()
                        }
                    },
                    receiveValue: { [weak self] evaluation in
                        self?.currentEvaluation = evaluation
                        // 更新进度
                        if evaluation.totalQuestions > 0 {
                            self?.currentProgress = Double(evaluation.completedQuestionsCount) / Double(evaluation.totalQuestions)
                        }
                    }
                )
                .store(in: &cancellables)
        }
    }

    /// 完成评估 (async)
    public func completeEvaluation(id: UUID) async throws -> EvaluationResult {
        isEvaluating = false
        currentProgress = 1.0

        return try await withCheckedThrowingContinuation { continuation in
            repository.completeEvaluation(id: id)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                    },
                    receiveValue: { [weak self] result in
                        self?.evaluationResult = result
                        self?.evaluationResults.append(result)
                        continuation.resume(returning: result)
                    }
                )
                .store(in: &cancellables)
        }
    }

    /// 获取评估结果 (async)
    public func getEvaluationResult(id: UUID) async throws -> EvaluationResult {
        return try await withCheckedThrowingContinuation { continuation in
            repository.getEvaluationResult(id: id)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                    },
                    receiveValue: { result in
                        continuation.resume(returning: result)
                    }
                )
                .store(in: &cancellables)
        }
    }

    /// 获取用户评估历史 (async)
    public func getUserEvaluationHistory() async throws -> [EvaluationResult] {
        guard let userID = currentUserID else {
            throw NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"])
        }

        return try await withCheckedThrowingContinuation { continuation in
            repository.getUserEvaluationHistory(userID: userID)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                    },
                    receiveValue: { [weak self] results in
                        self?.evaluationResults = results
                        continuation.resume(returning: results)
                    }
                )
                .store(in: &cancellables)
        }
    }

    /// 保存评估结果
    public func saveEvaluationResult(_ result: EvaluationResult) {
        evaluationResult = result
        if !evaluationResults.contains(where: { $0.id == result.id }) {
            evaluationResults.append(result)
        }
    }

    /// 更新评估进度
    public func updateProgress(_ progress: Double) {
        currentProgress = max(0.0, min(1.0, progress))
    }

    /// 重置当前评估
    public func resetCurrentEvaluation() {
        currentEvaluation = nil
        currentProgress = 0.0
        isEvaluating = false
        evaluationResult = nil
    }

    /// 暂停评估
    public func pauseEvaluation() {
        isEvaluating = false
    }

    /// 恢复评估
    public func resumeEvaluation() {
        if currentEvaluation != nil {
            isEvaluating = true
        }
    }

    /// 取消评估
    public func cancelEvaluation() {
        resetCurrentEvaluation()
    }

    // MARK: - Batch Operations

    /// 批量保存评估结果
    @MainActor
    public func batchSaveEvaluationResults(_ results: [EvaluationResult]) async {
        var successCount = 0
        var failedResults: [EvaluationResult] = []

        for result in results {
            do {
                _ = try await repository.saveEvaluationResultAsync(result)
                saveEvaluationResult(result)
                successCount += 1
            } catch {
                failedResults.append(result)
                print("Failed to save evaluation result \(result.id): \(error)")
            }
        }

        if !failedResults.isEmpty {
            self.handleError(
                AppError.dataSaveFailed("批量保存失败：\(failedResults.count) 个评估结果"),
                context: "批量保存评估结果"
            )
        }

        print("批量保存评估结果：成功 \(successCount)，失败 \(failedResults.count)")
    }

    /// 批量删除评估结果
    @MainActor
    public func batchDeleteEvaluationResults(resultIds: [UUID]) async {
        var successCount = 0
        var failedIds: [UUID] = []

        for resultId in resultIds {
            do {
                _ = try await repository.deleteAsync(resultId)
                evaluationResults.removeAll { $0.id == resultId }
                successCount += 1
            } catch {
                failedIds.append(resultId)
                print("Failed to delete evaluation result \(resultId): \(error)")
            }
        }

        if !failedIds.isEmpty {
            self.handleError(
                AppError.dataSaveFailed("批量删除失败：\(failedIds.count) 个评估结果"),
                context: "批量删除评估结果"
            )
        }

        print("批量删除评估结果：成功 \(successCount)，失败 \(failedIds.count)")
    }

    /// 同步所有评估数据
    @MainActor
    public func syncAllEvaluationData() async {
        isLoading = true

        do {
            // 同步可用评估列表
            _ = await loadAvailableEvaluationsAsync()

            // 同步评估历史
            loadEvaluationHistory()

            print("评估数据同步完成")
        } catch {
            self.handleError(error, context: "同步评估数据")
        }

        isLoading = false
    }

    // MARK: - Smart Analytics

    /// 获取评估统计分析
    public func getEvaluationAnalytics() -> EvaluationAnalytics {
        let completedResults = evaluationResults.filter { $0.isCompleted }

        let totalEvaluations = completedResults.count
        let averageScore = totalEvaluations > 0 ? completedResults.map { $0.score }.reduce(0, +) / Double(totalEvaluations) : 0.0
        let passRate = totalEvaluations > 0 ? Double(completedResults.filter { $0.isPassed }.count) / Double(totalEvaluations) : 0.0

        // 按类型分组统计
        let typeStats = Dictionary(grouping: completedResults, by: { $0.evaluationType })
            .mapValues { results in
                EvaluationTypeStats(
                    count: results.count,
                    averageScore: results.map { $0.score }.reduce(0, +) / Double(results.count),
                    passRate: Double(results.filter { $0.isPassed }.count) / Double(results.count)
                )
            }

        return EvaluationAnalytics(
            totalEvaluations: totalEvaluations,
            averageScore: averageScore,
            passRate: passRate,
            typeStats: typeStats,
            recentTrend: calculateRecentTrend()
        )
    }

    /// 计算最近趋势
    private func calculateRecentTrend() -> Double {
        let recentResults = evaluationResults
            .filter { $0.isCompleted }
            .sorted { $0.completedAt > $1.completedAt }
            .prefix(10)

        guard recentResults.count >= 2 else { return 0.0 }

        let recentAverage = recentResults.prefix(5).map { $0.score }.reduce(0, +) / 5.0
        let previousAverage = recentResults.suffix(5).map { $0.score }.reduce(0, +) / 5.0

        return recentAverage - previousAverage
    }

    // MARK: - Error Handling

    /// 统一错误处理方法
    @MainActor
    private func handleError(_ error: Error, context: String) {
        let appError = AppError.from(error)
        self.error = appError

        // 使用 ErrorHandler 进行统一处理
        ErrorHandler.shared.handle(appError, context: "EvaluationManager - \(context)")

        // 记录详细错误信息
        print("❌ [EvaluationManager] \(context) 失败: \(appError.localizedDescription)")
    }

    /// 处理网络错误的重试逻辑
    @MainActor
    private func handleNetworkError(_ error: Error, retryAction: @escaping () async throws -> Void) async {
        let appError = AppError.from(error)

        // 如果是网络错误，可以考虑重试
        if case .networkError = appError, case .noInternetConnection = appError {
            // 网络错误，暂时不重试，直接处理
            handleError(error, context: "网络请求")
        } else {
            handleError(error, context: "网络请求")
        }
    }
}

// MARK: - Supporting Types

/// 评估分析数据
public struct EvaluationAnalytics {
    public let totalEvaluations: Int
    public let averageScore: Double
    public let passRate: Double
    public let typeStats: [EvaluationType: EvaluationTypeStats]
    public let recentTrend: Double // 正数表示上升趋势，负数表示下降趋势
}

/// 评估类型统计
public struct EvaluationTypeStats {
    public let count: Int
    public let averageScore: Double
    public let passRate: Double
}
