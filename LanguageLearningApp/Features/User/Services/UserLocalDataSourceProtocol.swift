import Foundation
import Combine

// Protocol for UserLocalDataSource
public protocol UserLocalDataSourceProtocol {
    func getCurrentUser() throws -> User?
    func save(user: User) throws -> User
    // Make saveAll consistent - either throwing sync or async publisher
    // For now, let's assume a synchronous throwing array return, consistent with other CoreData ops
    func saveAll(users: [User]) throws -> [User] 
    func getAll() throws -> [User]
    func getById(id: UUID) throws -> User?
    func delete(id: UUID) throws -> Bool // Or Void, let's keep Bool for now
    func clearCurrentUserData() throws 
    func clearAllData() throws -> Bool // Or Void, let's keep Bool for now

    // Async versions might also be needed if local operations are heavy
    // For now, assuming synchronous throwing methods as per CoreData nature
    // Add async counterparts if/when UserLocalDataSource implements them
    // e.g.:
    // func getCurrentUserAsync() async throws -> User?
    // func saveAsync(user: User) async throws -> User
    // etc.
} 