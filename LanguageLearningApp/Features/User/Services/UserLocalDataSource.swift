import Foundation
import CoreData
import Combine

/// 用户本地数据源
public class UserLocalDataSource: UserLocalDataSourceProtocol {
    // public typealias T = User // No longer needed from LocalDataSourceProtocol
    // public typealias ID = UUID // No longer needed from LocalDataSourceProtocol
    
    /// 单例实例
    public static let shared = UserLocalDataSource()
    
    /// Core Data 管理器
    private let coreDataManager: CoreDataManager
    
    /// 初始化方法
    /// - Parameter coreDataManager: Core Data 管理器
    public init(coreDataManager: CoreDataManager = .shared) {
        self.coreDataManager = coreDataManager
    }
    
    /// 获取所有用户
    /// - Returns: 用户列表
    public func getAll() throws -> [User] {
        var users: [User] = []
        let semaphore = DispatchSemaphore(value: 0)
        var fetchError: Error?
        
        self.coreDataManager.performBackgroundTask { context in
            let request: NSFetchRequest<UserEntity> = UserEntity.fetchRequest()
            
            do {
                let entities = try context.fetch(request)
                users = entities.map { $0.toModel() }
            } catch {
                fetchError = error
            }
            semaphore.signal()
        }
        
        semaphore.wait()
        
        if let error = fetchError {
            throw error
        }
        
        return users
    }
    
    /// 根据 ID 获取用户
    /// - Parameter id: 用户 ID
    /// - Returns: 用户（如存在）
    public func getById(id: UUID) throws -> User? {
        var user: User?
        let semaphore = DispatchSemaphore(value: 0)
        var fetchError: Error?
        
        self.coreDataManager.performBackgroundTask { context in
            let request: NSFetchRequest<UserEntity> = UserEntity.fetchRequest()
            request.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            request.fetchLimit = 1
            
            do {
                let entities = try context.fetch(request)
                user = entities.first?.toModel()
            } catch {
                fetchError = error
            }
            semaphore.signal()
        }
        
        semaphore.wait()
        
        if let error = fetchError {
            throw error
        }
        
        return user
    }
    
    /// 保存用户
    /// - Parameter user: 要保存的用户
    /// - Returns: 保存后的用户
    public func save(user: User) throws -> User {
        var savedUser: User?
        let semaphore = DispatchSemaphore(value: 0)
        var saveError: Error?
        
        self.coreDataManager.performBackgroundTask { context in
            let request: NSFetchRequest<UserEntity> = UserEntity.fetchRequest()
            request.predicate = NSPredicate(format: "id == %@", user.id as CVarArg)
            
            do {
                let entities = try context.fetch(request)
                let userEntity: UserEntity
                
                if let existingEntity = entities.first {
                    existingEntity.update(from: user)
                    userEntity = existingEntity
                } else {
                    userEntity = UserEntity(context: context)
                    userEntity.update(from: user)
                }
                
                try context.save()
                savedUser = userEntity.toModel()
            } catch {
                saveError = error
            }
            semaphore.signal()
        }
        
        semaphore.wait()
        
        if let error = saveError {
            throw error
        }
        
        guard let finalUser = savedUser else {
            throw NSError(domain: "UserLocalDataSource", code: 1, userInfo: [NSLocalizedDescriptionKey: "Save failed to return a user."])
        }
        
        return finalUser
    }
    
    /// 保存多个用户
    /// - Parameter users: 要保存的用户列表
    /// - Returns: 保存后的用户列表
    public func saveAll(users: [User]) throws -> [User] {
        var savedUsersInternal: [User] = []
        let semaphore = DispatchSemaphore(value: 0)
        var saveError: Error?
        
        self.coreDataManager.performBackgroundTask { context in
            do {
                var tempSavedUsers: [User] = []
                for userToSave in users {
                    let request: NSFetchRequest<UserEntity> = UserEntity.fetchRequest()
                    request.predicate = NSPredicate(format: "id == %@", userToSave.id as CVarArg)
                    
                    let fetchedEntities = try context.fetch(request)
                    let entityToProcess: UserEntity
                    
                    if let existingEntity = fetchedEntities.first {
                        existingEntity.update(from: userToSave)
                        entityToProcess = existingEntity
                    } else {
                        entityToProcess = UserEntity(context: context)
                        entityToProcess.update(from: userToSave)
                    }
                    tempSavedUsers.append(entityToProcess.toModel())
                }
                
                try context.save()
                savedUsersInternal = tempSavedUsers
            } catch {
                saveError = error
            }
            semaphore.signal()
        }
        
        semaphore.wait()
        
        if let error = saveError {
            throw error
        }
        
        return savedUsersInternal
    }
    
    /// 删除用户
    /// - Parameter id: 要删除的用户ID
    /// - Returns: 删除是否成功
    public func delete(id: UUID) throws -> Bool {
        var success = false
        let semaphore = DispatchSemaphore(value: 0)
        var deleteError: Error?
        
        self.coreDataManager.performBackgroundTask { context in
            let request: NSFetchRequest<UserEntity> = UserEntity.fetchRequest()
            request.predicate = NSPredicate(format: "id == %@", id as CVarArg)
            
            do {
                let entities = try context.fetch(request)
                if entities.isEmpty {
                    success = true
                } else {
                    entities.forEach { context.delete($0) }
                    try context.save()
                    success = true
                }
            } catch {
                deleteError = error
            }
            semaphore.signal()
        }
        
        semaphore.wait()
        
        if let error = deleteError {
            throw error
        }
        
        return success
    }
    
    /// 获取当前用户
    /// - Returns: 用户（如存在）
    public func getCurrentUser() throws -> User? {
        var user: User?
        let semaphore = DispatchSemaphore(value: 0)
        var fetchError: Error?
        
        self.coreDataManager.performBackgroundTask { context in
            let request: NSFetchRequest<UserEntity> = UserEntity.fetchRequest()
            request.predicate = NSPredicate(format: "isActive == YES")
            request.fetchLimit = 1
            
            do {
                let entities = try context.fetch(request)
                user = entities.first?.toModel()
            } catch {
                fetchError = error
            }
            semaphore.signal()
        }
        
        semaphore.wait()
        
        if let error = fetchError {
            throw error
        }
        
        return user
    }
    
    /// 清除当前用户的特定数据
    /// For now, this will attempt to delete the "active" user record.
    public func clearCurrentUserData() throws {
        let semaphore = DispatchSemaphore(value: 0)
        var operationError: Error?

        self.coreDataManager.performBackgroundTask { context in
            let request: NSFetchRequest<UserEntity> = UserEntity.fetchRequest()
            request.predicate = NSPredicate(format: "isActive == YES")
            request.fetchLimit = 1

            do {
                if let activeUserEntity = try context.fetch(request).first {
                    context.delete(activeUserEntity)
                    let settingsRequest: NSFetchRequest<UserSettingsEntity> = UserSettingsEntity.fetchRequest()
                    settingsRequest.predicate = NSPredicate(format: "user == %@", activeUserEntity)
                    if let userSettings = try context.fetch(settingsRequest).first {
                        context.delete(userSettings)
                    }

                    try context.save()
                } else {
                    // No active user found, nothing to clear specifically this way
                }
            } catch {
                operationError = error
            }
            semaphore.signal()
        }
        semaphore.wait()

        if let error = operationError {
            throw error
        }
    }
    
    /// 清除所有用户数据
    /// - Returns: 操作是否成功
    public func clearAllData() throws -> Bool {
        var success = false
        let semaphore = DispatchSemaphore(value: 0)
        var clearError: Error?
        
        self.coreDataManager.performBackgroundTask { context in
            let entityNames = ["UserEntity", "UserSettingsEntity", "UserStatsEntity"]
            do {
                for entityName in entityNames {
                    let fetchRequest: NSFetchRequest<NSFetchRequestResult> = NSFetchRequest(entityName: entityName)
                    let deleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)
                    try context.execute(deleteRequest)
                }
                try context.save()
                success = true
            } catch {
                clearError = error
            }
            semaphore.signal()
        }
        
        semaphore.wait()
        
        if let error = clearError {
            throw error
        }
        
        return success
    }
}

// MARK: - UserEntity Extension
extension UserEntity {
    /// 从模型更新实体
    /// - Parameter user: 用户模型
    func update(from user: User) {
        id = user.id
        username = user.username
        email = user.email
        name = user.name
        avatar = user.avatar
        createdAt = user.createdAt
        updatedAt = user.updatedAt
        lastLoginAt = user.lastLoginAt
        isActive = user.isActive ?? true
        
        // 更新设置
        if let settings = user.settings {
            if let existingSettings = self.settings {
                existingSettings.update(from: settings)
            } else {
                let newSettings = UserSettingsEntity(context: managedObjectContext!)
                newSettings.update(from: settings)
                self.settings = newSettings
            }
        }
        
        // 更新统计信息
        if let stats = user.stats {
            if let existingStats = self.stats {
                existingStats.update(from: stats)
            } else {
                let newStats = UserStatsEntity(context: managedObjectContext!)
                newStats.update(from: stats)
                self.stats = newStats
            }
        }
    }
    
    /// 转换为模型
    /// - Returns: 用户模型
    func toModel() -> User {
        guard let id = id,
              let username = username,
              let email = email else {
            fatalError("Required user fields are missing")
        }
        
        return User(
            id: id,
            username: username,
            email: email,
            name: name,
            avatar: avatar,
            createdAt: createdAt,
            updatedAt: updatedAt,
            lastLoginAt: lastLoginAt,
            isActive: isActive ?? true,
            settings: settings?.toModel(),
            stats: stats?.toModel()
        )
    }
}

// MARK: - UserSettingsEntity Extension
extension UserSettingsEntity {
    /// 从模型更新实体
    /// - Parameter settings: 用户设置
    func update(from settings: UserSettings) {
        notificationsEnabled = settings.notificationsEnabled
        darkModeEnabled = settings.darkModeEnabled
        dailyGoal = Int32(settings.dailyGoal)
        preferredLanguage = settings.preferredLanguage
        offlineModeEnabled = settings.offlineModeEnabled ?? false
        autoplayAudio = settings.autoplayAudio ?? true
        showTranslation = settings.showTranslation ?? true
        fontSize = settings.fontSize?.rawValue ?? "medium"
    }
    
    /// 转换为模型
    /// - Returns: 用户设置
    func toModel() -> UserSettings {
        return UserSettings(
            notificationsEnabled: notificationsEnabled,
            darkModeEnabled: darkModeEnabled,
            dailyGoal: Int(dailyGoal),
            preferredLanguage: preferredLanguage ?? "zh-CN",
            offlineModeEnabled: offlineModeEnabled,
            autoplayAudio: autoplayAudio,
            showTranslation: showTranslation,
            fontSize: UserSettings.FontSize(rawValue: fontSize ?? "medium")
        )
    }
}

// MARK: - UserStatsEntity Extension
extension UserStatsEntity {
    /// 从模型更新实体
    /// - Parameter stats: 用户统计
    func update(from stats: UserStats) {
        streakDays = Int32(stats.streakDays)
        vocabularyCount = Int32(stats.vocabularyCount)
        listeningExerciseCount = Int32(stats.listeningExerciseCount)
        speakingExerciseCount = Int32(stats.speakingExerciseCount)
        points = Int32(stats.points)
        completedChallenges = Int32(stats.completedChallenges)
        helpedUsers = Int32(stats.helpedUsers)
        lastLoginDate = stats.lastLoginDate
    }
    
    /// 转换为模型
    /// - Returns: 用户统计
    func toModel() -> UserStats {
        return UserStats(
            streakDays: Int(streakDays),
            vocabularyCount: Int(vocabularyCount),
            listeningExerciseCount: Int(listeningExerciseCount),
            speakingExerciseCount: Int(speakingExerciseCount),
            points: Int(points),
            completedChallenges: Int(completedChallenges),
            helpedUsers: Int(helpedUsers),
            lastLoginDate: lastLoginDate
        )
    }
} 