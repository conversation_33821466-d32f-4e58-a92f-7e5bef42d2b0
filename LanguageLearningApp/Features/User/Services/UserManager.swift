import Foundation
import Combine
import SwiftUI

/// 用户管理器，处理用户相关的业务逻辑
@MainActor
public class UserManager: ObservableObject, UserManagerProtocol {
    // 状态枚举
    public enum State {
        case idle
        case loading
        case authenticated(User)
        case unauthenticated
        case registering
        case registered(User)
        case updatingProfile
        case profileUpdated(User)
        case error(Error)
    }

    // 当前状态
    @Published public var state: State = .idle

    // 错误信息
    @Published public var errorMessage: String?

    // 当前用户
    @Published public private(set) var currentUser: User?

    // 认证令牌
    @Published public private(set) var authToken: String?

    // 是否已认证
    @Published public private(set) var isAuthenticated = false

    // 是否已登录
    public var isLoggedIn: Bool {
        return isAuthenticated
    }

    // 是否正在加载
    @Published public private(set) var isLoading = false

    // 是否正在提交
    @Published public private(set) var isSubmitting = false

    // 仓库
    private let repository: any UserRepositoryProtocol

    // 取消标记
    private var cancellables = Set<AnyCancellable>()

    // 单例实例 - 使用懒加载避免循环依赖
    public static let shared: UserManager = {
        let manager = UserManager()
        return manager
    }()

    /// 初始化方法
    /// - Parameter repository: 用户仓库
    public init(repository: (any UserRepositoryProtocol)? = nil) {
        // 延迟设置仓库以避免循环依赖
        if let repo = repository {
            self.repository = repo
        } else {
            // 使用默认仓库，但延迟初始化以避免循环依赖
            self.repository = UserRepository(userManager: nil, shouldFetchRemoteFirst: true)
        }

        // 设置仓库的用户管理器引用
        if let userRepo = self.repository as? UserRepository {
            userRepo.setUserManager(self)
        }

        // 延迟加载用户数据
        Task { @MainActor in
            await self.initializeUserData()
        }
    }

    /// 初始化用户数据
    private func initializeUserData() async {
        // Load auth token from UserDefaults if available
        self.authToken = UserDefaults.standard.string(forKey: "authToken")

        // 加载当前用户
        loadCurrentUser()
    }

    /// 加载当前用户
    private func loadCurrentUser() {
        state = .loading
        isLoading = true
        errorMessage = nil

        repository.getCurrentUser()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                guard let self = self else { return }

                self.isLoading = false

                if case .failure(let error) = completion {
                    if let repoError = error as? RepositoryError, repoError == .entityNotFound {
                        self.state = .unauthenticated
                        self.isAuthenticated = false
                    } else {
                        self.state = .error(error)
                        self.errorMessage = error.localizedDescription
                    }
                }
            } receiveValue: { [weak self] user in
                guard let self = self else { return }

                self.currentUser = user
                self.state = .authenticated(user)
                self.isAuthenticated = true
                // If user has a token, ensure it's set in UserManager as well
                if let token = user.token {
                    self.authToken = token
                    // Optionally, ensure UserDefaults is also in sync if User.token is the source of truth
                    // UserDefaults.standard.set(token, forKey: "authToken")
                } else if self.authToken == nil { // if user has no token, and we don't have one from init
                     self.authToken = UserDefaults.standard.string(forKey: "authToken")
                }
            }
            .store(in: &cancellables)
    }

    /// 登录
    /// - Parameters:
    ///   - username: 用户名
    ///   - password: 密码
    /// - Returns: 异步操作结果
    public func login(username: String, password: String) async throws {
        _ = try await loginAsync(username: username, password: password)
    }

    /// 注册
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 邮箱
    ///   - password: 密码
    /// - Returns: 异步操作结果
    public func register(username: String, email: String, password: String) async throws {
        _ = try await registerAsync(username: username, email: email, password: password)
    }

    /// 登出
    public func logout() {
        Task {
            _ = try? await logoutAsync()
        }
    }

    /// 更新用户资料
    /// - Parameters:
    ///   - name: 用户名
    ///   - email: 电子邮件
    ///   - avatar: 头像数据
    /// - Returns: 异步操作结果
    public func updateProfile(name: String?, email: String?, avatar: Data?) async throws {
        _ = try await updateUserProfileAsync(name: name, email: email, avatar: avatar)
    }

    /// 更新用户设置
    /// - Parameter settings: 用户设置
    /// - Returns: 异步操作结果
    public func updateSettings(settings: UserSettings) async throws {
        guard let user = currentUser else {
            throw RepositoryError.entityNotFound
        }

        let updatedUser = User(
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt,
            isActive: user.isActive,
            settings: settings,
            stats: user.stats
        )

        _ = try await (repository as! UserRepository).saveAsync(updatedUser)
    }

    /// 重置密码
    /// - Parameter email: 电子邮件
    /// - Returns: 异步操作结果
    public func resetPassword(email: String) async throws {
        _ = try await repository.resetPasswordAsync(email: email)
    }

    /// 更改密码
    /// - Parameters:
    ///   - oldPassword: 旧密码
    ///   - newPassword: 新密码
    /// - Returns: 异步操作结果
    public func changePassword(oldPassword: String, newPassword: String) async throws {
        _ = try await repository.changePasswordAsync(oldPassword: oldPassword, newPassword: newPassword)
    }

    /// 删除账户
    /// - Returns: 异步操作结果
    public func deleteAccount() async throws {
        _ = try await repository.deleteAccountAsync()
    }

    /// 检查每日连续登录
    public func checkDailyStreak() {
        guard let user = currentUser else { return }

        let calendar = Calendar.current
        let today = Date()

        if let lastLoginDate = user.stats?.lastLoginDate {
            let components = calendar.dateComponents([.day], from: lastLoginDate, to: today)
            if let days = components.day, days == 1 {
                // 连续登录，更新连续登录天数
                let updatedStats = UserStats(
                    streakDays: (user.stats?.streakDays ?? 0) + 1,
                    vocabularyCount: user.stats?.vocabularyCount ?? 0,
                    listeningExerciseCount: user.stats?.listeningExerciseCount ?? 0,
                    speakingExerciseCount: user.stats?.speakingExerciseCount ?? 0,
                    points: user.stats?.points ?? 0,
                    completedChallenges: user.stats?.completedChallenges ?? 0,
                    helpedUsers: user.stats?.helpedUsers ?? 0,
                    lastLoginDate: user.stats?.lastLoginDate
                )

                let updatedUser = User(
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    name: user.name,
                    avatar: user.avatar,
                    createdAt: user.createdAt,
                    updatedAt: user.updatedAt,
                    lastLoginAt: user.lastLoginAt,
                    isActive: user.isActive,
                    settings: user.settings,
                    stats: updatedStats
                )

                Task {
                    _ = try? await (repository as! UserRepository).saveAsync(updatedUser)
                }
            } else if let days = components.day, days > 1 {
                // 中断连续登录，重置连续登录天数
                let updatedStats = UserStats(
                    streakDays: 1,
                    vocabularyCount: user.stats?.vocabularyCount ?? 0,
                    listeningExerciseCount: user.stats?.listeningExerciseCount ?? 0,
                    speakingExerciseCount: user.stats?.speakingExerciseCount ?? 0,
                    points: user.stats?.points ?? 0,
                    completedChallenges: user.stats?.completedChallenges ?? 0,
                    helpedUsers: user.stats?.helpedUsers ?? 0,
                    lastLoginDate: user.stats?.lastLoginDate
                )

                let updatedUser = User(
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    name: user.name,
                    avatar: user.avatar,
                    createdAt: user.createdAt,
                    updatedAt: user.updatedAt,
                    lastLoginAt: user.lastLoginAt,
                    isActive: user.isActive,
                    settings: user.settings,
                    stats: updatedStats
                )

                Task {
                    _ = try? await (repository as! UserRepository).saveAsync(updatedUser)
                }
            }
        }

        // 更新最后登录日期
        let updatedStats = UserStats(
            streakDays: user.stats?.streakDays ?? 0,
            vocabularyCount: user.stats?.vocabularyCount ?? 0,
            listeningExerciseCount: user.stats?.listeningExerciseCount ?? 0,
            speakingExerciseCount: user.stats?.speakingExerciseCount ?? 0,
            points: user.stats?.points ?? 0,
            completedChallenges: user.stats?.completedChallenges ?? 0,
            helpedUsers: user.stats?.helpedUsers ?? 0,
            lastLoginDate: today
        )

        let updatedUser = User(
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt,
            isActive: user.isActive,
            settings: user.settings,
            stats: updatedStats
        )

        Task {
            _ = try? await (repository as! UserRepository).saveAsync(updatedUser)
        }
    }

    /// 更新用户词汇量
    /// - Parameter count: 词汇量
    public func updateUserVocabularyCount(_ count: Int) {
        guard let user = currentUser else { return }

        let updatedStats = UserStats(
            streakDays: user.stats?.streakDays ?? 0,
            vocabularyCount: count,
            listeningExerciseCount: user.stats?.listeningExerciseCount ?? 0,
            speakingExerciseCount: user.stats?.speakingExerciseCount ?? 0,
            points: user.stats?.points ?? 0,
            completedChallenges: user.stats?.completedChallenges ?? 0,
            helpedUsers: user.stats?.helpedUsers ?? 0,
            lastLoginDate: user.stats?.lastLoginDate
        )

        let updatedUser = User(
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt,
            isActive: user.isActive,
            settings: user.settings,
            stats: updatedStats
        )

        Task {
            _ = try? await (repository as! UserRepository).saveAsync(updatedUser)
        }
    }

    /// 更新用户听力练习次数
    /// - Parameter count: 听力练习次数
    public func updateUserListeningExerciseCount(_ count: Int) {
        guard let user = currentUser else { return }

        let updatedStats = UserStats(
            streakDays: user.stats?.streakDays ?? 0,
            vocabularyCount: user.stats?.vocabularyCount ?? 0,
            listeningExerciseCount: count,
            speakingExerciseCount: user.stats?.speakingExerciseCount ?? 0,
            points: user.stats?.points ?? 0,
            completedChallenges: user.stats?.completedChallenges ?? 0,
            helpedUsers: user.stats?.helpedUsers ?? 0,
            lastLoginDate: user.stats?.lastLoginDate
        )

        let updatedUser = User(
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt,
            isActive: user.isActive,
            settings: user.settings,
            stats: updatedStats
        )

        Task {
            _ = try? await (repository as! UserRepository).saveAsync(updatedUser)
        }
    }

    /// 更新用户口语练习次数
    /// - Parameter count: 口语练习次数
    public func updateUserSpeakingExerciseCount(_ count: Int) {
        guard let user = currentUser else { return }

        let updatedStats = UserStats(
            streakDays: user.stats?.streakDays ?? 0,
            vocabularyCount: user.stats?.vocabularyCount ?? 0,
            listeningExerciseCount: user.stats?.listeningExerciseCount ?? 0,
            speakingExerciseCount: count,
            points: user.stats?.points ?? 0,
            completedChallenges: user.stats?.completedChallenges ?? 0,
            helpedUsers: user.stats?.helpedUsers ?? 0,
            lastLoginDate: user.stats?.lastLoginDate
        )

        let updatedUser = User(
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt,
            isActive: user.isActive,
            settings: user.settings,
            stats: updatedStats
        )

        Task {
            _ = try? await (repository as! UserRepository).saveAsync(updatedUser)
        }
    }

    /// 更新用户积分
    /// - Parameter points: 积分
    public func updateUserPoints(_ points: Int) {
        guard let user = currentUser else { return }

        let updatedStats = UserStats(
            streakDays: user.stats?.streakDays ?? 0,
            vocabularyCount: user.stats?.vocabularyCount ?? 0,
            listeningExerciseCount: user.stats?.listeningExerciseCount ?? 0,
            speakingExerciseCount: user.stats?.speakingExerciseCount ?? 0,
            points: points,
            completedChallenges: user.stats?.completedChallenges ?? 0,
            helpedUsers: user.stats?.helpedUsers ?? 0,
            lastLoginDate: user.stats?.lastLoginDate
        )

        let updatedUser = User(
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt,
            isActive: user.isActive,
            settings: user.settings,
            stats: updatedStats
        )

        Task {
            _ = try? await (repository as! UserRepository).saveAsync(updatedUser)
        }
    }

    /// 更新用户完成的挑战数量
    /// - Parameter count: 完成的挑战数量
    public func updateUserCompletedChallenges(_ count: Int) {
        guard let user = currentUser else { return }

        let updatedStats = UserStats(
            streakDays: user.stats?.streakDays ?? 0,
            vocabularyCount: user.stats?.vocabularyCount ?? 0,
            listeningExerciseCount: user.stats?.listeningExerciseCount ?? 0,
            speakingExerciseCount: user.stats?.speakingExerciseCount ?? 0,
            points: user.stats?.points ?? 0,
            completedChallenges: count,
            helpedUsers: user.stats?.helpedUsers ?? 0,
            lastLoginDate: user.stats?.lastLoginDate
        )

        let updatedUser = User(
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt,
            isActive: user.isActive,
            settings: user.settings,
            stats: updatedStats
        )

        Task {
            _ = try? await (repository as! UserRepository).saveAsync(updatedUser)
        }
    }

    /// 更新用户帮助的用户数量
    /// - Parameter count: 帮助的用户数量
    public func updateUserHelpedUsers(_ count: Int) {
        guard let user = currentUser else { return }

        let updatedStats = UserStats(
            streakDays: user.stats?.streakDays ?? 0,
            vocabularyCount: user.stats?.vocabularyCount ?? 0,
            listeningExerciseCount: user.stats?.listeningExerciseCount ?? 0,
            speakingExerciseCount: user.stats?.speakingExerciseCount ?? 0,
            points: user.stats?.points ?? 0,
            completedChallenges: user.stats?.completedChallenges ?? 0,
            helpedUsers: count,
            lastLoginDate: user.stats?.lastLoginDate
        )

        let updatedUser = User(
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt,
            isActive: user.isActive,
            settings: user.settings,
            stats: updatedStats
        )

        Task {
            _ = try? await (repository as! UserRepository).saveAsync(updatedUser)
        }
    }

    /// 加载用户资料
    /// - Returns: 异步操作结果
    public func loadUserProfileFromAPI() async {
        do {
            let user = try await repository.getCurrentUserAsync()
            DispatchQueue.main.async {
                self.currentUser = user
                self.state = .authenticated(user)
                self.isAuthenticated = true
            }
        } catch {
            DispatchQueue.main.async {
                self.state = .error(error)
                self.errorMessage = error.localizedDescription
            }
        }
    }

    /// 加载用户统计信息
    /// - Returns: 异步操作结果
    public func loadUserStats() async {
        // 实现从API加载用户统计信息的逻辑
    }

    /// 加载用户设置
    /// - Returns: 异步操作结果
    public func loadUserSettings() async {
        // 实现从API加载用户设置的逻辑
    }

    // MARK: - 异步方法

    /// 异步登录
    /// - Parameters:
    ///   - username: 用户名
    ///   - password: 密码
    /// - Returns: 用户信息
    @MainActor
    public func loginAsync(username: String, password: String) async throws -> User {
        state = .loading
        isLoading = true
        errorMessage = nil

        do {
            // 在开发环境下，优先检查是否为 Mock 账号（无论是否启用 Mock 数据）
            #if DEBUG
            if MockAccountManager.shared.validateMockAccount(username: username, password: password) {
                if let mockAccount = MockAccountManager.shared.getMockAccount(username: username) {
                    let user = User(
                        id: UUID(),
                        username: mockAccount.username,
                        email: "\(mockAccount.username)@example.com",
                        name: mockAccount.username,
                        avatar: nil,
                        createdAt: Date(),
                        updatedAt: Date(),
                        lastLoginAt: Date(),
                        isActive: true,
                        settings: UserSettings.default,
                        stats: UserStats.sample,
                        currentStreak: 0,
                        vocabularyCount: 0,
                        listeningExerciseCount: 0,
                        speakingExerciseCount: 0,
                        points: 0,
                        completedChallenges: 0,
                        helpedUsers: 0,
                        token: "mock_token_\(mockAccount.username)"
                    )
                    currentUser = user
                    state = .authenticated(user)
                    isAuthenticated = true
                    isLoading = false

                    // 记录 demo 登录
                    Logger.shared.info("🎭 Demo 登录成功: \(mockAccount.username) (\(mockAccount.role))")

                    return user
                }
            }
            #endif

            // 使用真实 API 进行登录
            let user = try await repository.loginAsync(username: username, password: password)

            // 保存认证令牌
            if let token = user.token {
                UserDefaults.standard.set(token, forKey: "authToken")
                self.authToken = token
            }

            currentUser = user
            state = .authenticated(user)
            isAuthenticated = true
            isLoading = false
            return user
        } catch {
            state = .error(error)
            errorMessage = error.localizedDescription
            isLoading = false
            throw error
        }
    }

    /// 异步注册
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 邮箱
    ///   - password: 密码
    /// - Returns: 用户信息
    @MainActor
    public func registerAsync(username: String, email: String, password: String) async throws -> User {
        state = .registering
        isSubmitting = true
        errorMessage = nil

        do {
            let user = try await repository.registerAsync(username: username, email: email, password: password)
            currentUser = user
            state = .registered(user)
            isAuthenticated = true
            isSubmitting = false
            return user
        } catch {
            state = .error(error)
            errorMessage = error.localizedDescription
            isSubmitting = false
            throw error
        }
    }

    /// 异步登出
    /// - Returns: 成功状态
    @MainActor
    public func logoutAsync() async throws -> Bool {
        state = .loading
        isLoading = true
        errorMessage = nil

        do {
            let success = try await repository.logoutAsync()

            // 无论远程登出是否成功，都清除本地状态
            currentUser = nil
            state = .unauthenticated
            isAuthenticated = false
            self.authToken = nil

            isLoading = false
            return success
        } catch {
            // 即使发生错误，也清除本地状态
            currentUser = nil
            state = .unauthenticated
            isAuthenticated = false
            self.authToken = nil

            isLoading = false
            throw error
        }
    }

    /// 异步更新用户资料
    /// - Parameters:
    ///   - name: 姓名
    ///   - email: 邮箱
    ///   - avatar: 头像数据
    /// - Returns: 更新后的用户信息
    @MainActor
    public func updateUserProfileAsync(name: String?, email: String?, avatar: Data?) async throws -> User {
        state = .updatingProfile
        isSubmitting = true
        errorMessage = nil

        do {
            let user = try await repository.updateUserProfileAsync(name: name, email: email, avatar: avatar)
            currentUser = user
            state = .profileUpdated(user)
            isSubmitting = false
            return user
        } catch {
            state = .error(error)
            errorMessage = error.localizedDescription
            isSubmitting = false
            throw error
        }
    }

    /// 异步重置密码
    /// - Parameter email: 邮箱
    /// - Returns: 成功状态
    @MainActor
    public func resetPasswordAsync(email: String) async throws -> Bool {
        isSubmitting = true
        errorMessage = nil

        do {
            let success = try await repository.resetPasswordAsync(email: email)
            isSubmitting = false
            return success
        } catch {
            errorMessage = error.localizedDescription
            isSubmitting = false
            throw error
        }
    }

    /// 异步修改密码
    /// - Parameters:
    ///   - oldPassword: 旧密码
    ///   - newPassword: 新密码
    /// - Returns: 成功状态
    @MainActor
    public func changePasswordAsync(oldPassword: String, newPassword: String) async throws -> Bool {
        isSubmitting = true
        errorMessage = nil

        do {
            let success = try await repository.changePasswordAsync(oldPassword: oldPassword, newPassword: newPassword)
            isSubmitting = false
            return success
        } catch {
            errorMessage = error.localizedDescription
            isSubmitting = false
            throw error
        }
    }

    /// 异步删除账户
    /// - Returns: 成功状态
    @MainActor
    public func deleteAccountAsync() async throws -> Bool {
        state = .loading
        isLoading = true
        errorMessage = nil

        do {
            let success = try await repository.deleteAccountAsync()

            if success {
                currentUser = nil
                state = .unauthenticated
                isAuthenticated = false
                self.authToken = nil
            }

            isLoading = false
            return success
        } catch {
            state = .error(error)
            errorMessage = error.localizedDescription
            isLoading = false
            throw error
        }
    }
}