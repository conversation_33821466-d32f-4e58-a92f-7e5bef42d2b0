import Foundation

/// 用户统计信息
public struct UserStats: Codable {
    /// 连续学习天数
    public var streakDays: Int
    /// 词汇量
    public var vocabularyCount: Int
    /// 听力练习次数
    public var listeningExerciseCount: Int
    /// 口语练习次数
    public var speakingExerciseCount: Int
    /// 积分
    public var points: Int
    /// 完成的挑战数
    public var completedChallenges: Int
    /// 帮助的用户数
    public var helpedUsers: Int
    /// 最后登录日期
    public var lastLoginDate: Date?
    
    /// 创建用户统计信息
    /// - Parameters:
    ///   - streakDays: 连续学习天数
    ///   - vocabularyCount: 词汇量
    ///   - listeningExerciseCount: 听力练习次数
    ///   - speakingExerciseCount: 口语练习次数
    ///   - points: 积分
    ///   - completedChallenges: 完成的挑战数
    ///   - helpedUsers: 帮助的用户数
    ///   - lastLoginDate: 最后登录日期
    public init(
        streakDays: Int = 0,
        vocabularyCount: Int = 0,
        listeningExerciseCount: Int = 0,
        speakingExerciseCount: Int = 0,
        points: Int = 0,
        completedChallenges: Int = 0,
        helpedUsers: Int = 0,
        lastLoginDate: Date? = nil
    ) {
        self.streakDays = streakDays
        self.vocabularyCount = vocabularyCount
        self.listeningExerciseCount = listeningExerciseCount
        self.speakingExerciseCount = speakingExerciseCount
        self.points = points
        self.completedChallenges = completedChallenges
        self.helpedUsers = helpedUsers
        self.lastLoginDate = lastLoginDate
    }
    
    public static var sample: UserStats {
        UserStats(
            streakDays: 3,
            vocabularyCount: 120,
            listeningExerciseCount: 45,
            speakingExerciseCount: 30,
            points: 1250,
            completedChallenges: 5,
            helpedUsers: 2,
            lastLoginDate: Date()
        )
    }
} 