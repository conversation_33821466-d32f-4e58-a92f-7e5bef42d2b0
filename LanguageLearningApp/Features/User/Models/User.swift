import Foundation

/// 用户模型
public struct User: Codable, Identifiable {
    /// 用户 ID
    public let id: UUID
    /// 用户名
    public let username: String
    /// 邮箱
    public let email: String
    /// 姓名
    public let name: String?
    /// 头像 URL
    public let avatar: String?
    /// 创建时间
    public let createdAt: Date?
    /// 更新时间
    public let updatedAt: Date?
    /// 最后登录时间
    public let lastLoginAt: Date?
    /// 是否激活
    public let isActive: Bool?
    /// 用户设置
    public let settings: UserSettings?
    /// 用户统计
    public let stats: UserStats?
    /// 当前连续学习天数
    public let currentStreak: Int
    /// 词汇量
    public let vocabularyCount: Int
    /// 听力练习次数
    public let listeningExerciseCount: Int
    /// 口语练习次数
    public let speakingExerciseCount: Int
    /// 积分
    public let points: Int
    /// 完成的挑战数
    public let completedChallenges: Int
    /// 帮助的用户数
    public let helpedUsers: Int
    /// 认证令牌
    public let token: String?
    
    /// 创建用户
    /// - Parameters:
    ///   - id: 用户 ID
    ///   - username: 用户名
    ///   - email: 邮箱
    ///   - name: 姓名
    ///   - avatar: 头像 URL
    ///   - createdAt: 创建时间
    ///   - updatedAt: 更新时间
    ///   - lastLoginAt: 最后登录时间
    ///   - isActive: 是否激活
    ///   - settings: 用户设置
    ///   - stats: 用户统计
    ///   - currentStreak: 当前连续学习天数
    ///   - vocabularyCount: 词汇量
    ///   - listeningExerciseCount: 听力练习次数
    ///   - speakingExerciseCount: 口语练习次数
    ///   - points: 积分
    ///   - completedChallenges: 完成的挑战数
    ///   - helpedUsers: 帮助的用户数
    ///   - token: 认证令牌
    public init(
        id: UUID = UUID(),
        username: String,
        email: String,
        name: String? = nil,
        avatar: String? = nil,
        createdAt: Date? = nil,
        updatedAt: Date? = nil,
        lastLoginAt: Date? = nil,
        isActive: Bool? = true,
        settings: UserSettings? = nil,
        stats: UserStats? = nil,
        currentStreak: Int = 0,
        vocabularyCount: Int = 0,
        listeningExerciseCount: Int = 0,
        speakingExerciseCount: Int = 0,
        points: Int = 0,
        completedChallenges: Int = 0,
        helpedUsers: Int = 0,
        token: String? = nil
    ) {
        self.id = id
        self.username = username
        self.email = email
        self.name = name
        self.avatar = avatar
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.lastLoginAt = lastLoginAt
        self.isActive = isActive
        self.settings = settings
        self.stats = stats
        self.currentStreak = currentStreak
        self.vocabularyCount = vocabularyCount
        self.listeningExerciseCount = listeningExerciseCount
        self.speakingExerciseCount = speakingExerciseCount
        self.points = points
        self.completedChallenges = completedChallenges
        self.helpedUsers = helpedUsers
        self.token = token
    }
    
    /// 示例用户
    public static var sample: User {
        User(
            username: "testuser",
            email: "<EMAIL>",
            name: "Test User",
            avatar: "https://example.com/avatar.jpg",
            createdAt: Date(),
            updatedAt: Date(),
            lastLoginAt: Date(),
            isActive: true,
            settings: UserSettings.default,
            stats: UserStats.sample,
            currentStreak: 3,
            vocabularyCount: 120,
            listeningExerciseCount: 45,
            speakingExerciseCount: 30,
            points: 1250,
            completedChallenges: 5,
            helpedUsers: 2
        )
    }
}

/// 用户偏好设置
public struct UserPreferences: Codable {
    public var language: String
    public var theme: String
    public var notifications: Bool
    public var soundEnabled: Bool
    public var dailyGoal: Int
    public var weeklyGoal: Int
    
    public init(
        language: String = "zh-CN",
        theme: String = "system",
        notifications: Bool = true,
        soundEnabled: Bool = true,
        dailyGoal: Int = 30,
        weeklyGoal: Int = 180
    ) {
        self.language = language
        self.theme = theme
        self.notifications = notifications
        self.soundEnabled = soundEnabled
        self.dailyGoal = dailyGoal
        self.weeklyGoal = weeklyGoal
    }
}

/// 登录响应模型
public struct LoginResponse: Codable {
    /// 是否成功
    public let success: Bool
    /// 消息
    public let message: String
    /// 数据（JWT token）
    public let data: String
} 