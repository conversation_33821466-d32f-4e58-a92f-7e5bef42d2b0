import Foundation

/// 用户设置
public struct UserSettings: Codable {
    /// 是否启用通知
    public var notificationsEnabled: Bool

    /// 是否启用深色模式
    public var darkModeEnabled: Bool

    /// 每日学习目标（分钟）
    public var dailyGoal: Int

    /// 每日学习目标分钟数（与 dailyGoal 相同，为了兼容性）
    public var dailyGoalMinutes: Int {
        return dailyGoal
    }

    /// 每日练习目标次数
    public var dailyGoalPractices: Int

    /// 首选语言
    public var preferredLanguage: String

    /// 是否启用离线模式
    public var offlineModeEnabled: Bool?

    /// 是否自动播放音频
    public var autoplayAudio: Bool?

    /// 是否显示翻译
    public var showTranslation: Bool?

    /// 字体大小
    public var fontSize: FontSize?

    /// 默认设置
    public static let `default` = UserSettings(
        notificationsEnabled: true,
        darkModeEnabled: false,
        dailyGoal: 30,
        dailyGoalPractices: 5,
        preferredLanguage: "zh-CN",
        offlineModeEnabled: false,
        autoplayAudio: true,
        showTranslation: true,
        fontSize: .medium
    )

    public init(
        notificationsEnabled: Bool = true,
        darkModeEnabled: Bool = false,
        dailyGoal: Int = 30,
        dailyGoalPractices: Int = 5,
        preferredLanguage: String = "zh-CN",
        offlineModeEnabled: Bool? = false,
        autoplayAudio: Bool? = true,
        showTranslation: Bool? = true,
        fontSize: FontSize? = .medium
    ) {
        self.notificationsEnabled = notificationsEnabled
        self.darkModeEnabled = darkModeEnabled
        self.dailyGoal = dailyGoal
        self.dailyGoalPractices = dailyGoalPractices
        self.preferredLanguage = preferredLanguage
        self.offlineModeEnabled = offlineModeEnabled
        self.autoplayAudio = autoplayAudio
        self.showTranslation = showTranslation
        self.fontSize = fontSize
    }

    /// 字体大小枚举
    public enum FontSize: String, Codable {
        case small = "small"
        case medium = "medium"
        case large = "large"
        case extraLarge

        /// 字体大小缩放因子
        public var scaleFactor: CGFloat {
            switch self {
            case .small:
                return 0.8
            case .medium:
                return 1.0
            case .large:
                return 1.2
            case .extraLarge:
                return 1.4
            }
        }
    }
}