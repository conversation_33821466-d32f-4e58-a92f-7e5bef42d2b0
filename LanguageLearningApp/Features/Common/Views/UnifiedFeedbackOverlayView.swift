import SwiftUI

/// 统一的反馈覆盖视图，可以同时处理评估和练习的反馈
struct UnifiedFeedbackOverlayView: View {
    // 是否回答正确
    let isCorrect: Bool
    // 解释文本
    let explanation: String?
    // 正确答案（如果回答错误）
    let correctAnswer: String?
    // 是否显示反馈
    @Binding var showFeedback: Bool
    // 下一步操作
    var nextAction: (() -> Void)?
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.5)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    withAnimation {
                        showFeedback = false
                    }
                }
            
            // 反馈卡片
            VStack(alignment: .leading, spacing: 15) {
                // 关闭按钮
                HStack {
                    Spacer()
                    
                    Button(action: {
                        withAnimation {
                            showFeedback = false
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                }
                
                // 反馈图标和标题
                HStack {
                    Image(systemName: isCorrect ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .font(.largeTitle)
                        .foregroundColor(isCorrect ? .green : .red)
                    
                    Text(isCorrect ? "回答正确！" : "回答错误")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(isCorrect ? .green : .red)
                }
                
                // 分隔线
                Divider()
                
                // 正确答案（如果回答错误）
                if !isCorrect, let correctAnswer = correctAnswer {
                    VStack(alignment: .leading, spacing: 5) {
                        Text("正确答案:")
                            .font(.headline)
                        
                        Text(correctAnswer)
                            .font(.body)
                            .padding(.vertical, 5)
                    }
                }
                
                // 解释
                if let explanation = explanation, !explanation.isEmpty {
                    VStack(alignment: .leading, spacing: 5) {
                        Text("解释:")
                            .font(.headline)
                        
                        Text(explanation)
                            .font(.body)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                }
                
                Spacer()
                
                // 下一步按钮
                if let nextAction = nextAction {
                    Button(action: {
                        withAnimation {
                            showFeedback = false
                        }
                        nextAction()
                    }) {
                        HStack {
                            Text("继续")
                                .fontWeight(.semibold)
                            
                            Image(systemName: "arrow.right")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                }
            }
            .padding()
            .background(Color(UIColor.systemBackground))
            .cornerRadius(15)
            .shadow(radius: 10)
            .padding(.horizontal, 20)
            .transition(.opacity)
        }
    }
}

// 预览
struct UnifiedFeedbackOverlayView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 正确答案的反馈
            UnifiedFeedbackOverlayView(
                isCorrect: true,
                explanation: "Paris is indeed the capital of France, known for the Eiffel Tower and many other landmarks.",
                correctAnswer: nil,
                showFeedback: .constant(true)
            )
            
            // 错误答案的反馈
            UnifiedFeedbackOverlayView(
                isCorrect: false,
                explanation: "Berlin is the capital of Germany, not Paris. Paris is the capital of France.",
                correctAnswer: "Berlin",
                showFeedback: .constant(true)
            )
        }
    }
}
