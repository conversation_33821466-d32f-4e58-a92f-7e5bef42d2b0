import Foundation
import Combine
import SwiftUI

/// 词汇管理器，处理词汇相关的业务逻辑
public class VocabularyManager: ObservableObject, VocabularyManagerProtocol {
    // MARK: - 单例
    public static let shared = VocabularyManager()

    // MARK: - Published Properties (VocabularyManagerProtocol)
    @Published public private(set) var words: [Word] = []
    @Published public private(set) var categories: [VocabularyCategory] = []
    @Published public private(set) var learnedWordIds: Set<UUID> = []
    @Published public private(set) var favoriteWordIds: Set<UUID> = []
    @Published public private(set) var wordProgress: [UUID: WordProgress] = [:]
    @Published public var searchText: String = "" {
        didSet {
            filterWords()
        }
    }
    @Published public private(set) var filteredWords: [Word] = []
    @Published public private(set) var isLoading = false
    @Published public var error: Error?

    // MARK: - Additional Protocol Properties
    @Published public var selectedCategory: VocabularyCategory?
    @Published public var selectedDifficulty: String?

    // MARK: - Private Properties
    private let repository: VocabularyRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    public init(repository: VocabularyRepositoryProtocol = VocabularyRepository.shared) {
        self.repository = repository
        setupSubscriptions()
        loadInitialData()
    }

    // MARK: - Setup
    private func setupSubscriptions() {
        // 监听搜索文本变化
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.filterWords()
            }
            .store(in: &cancellables)
    }

    private func loadInitialData() {
        Task {
            await loadWords()
            await loadCategories()
            await loadProgress()
            await loadLearnedWords()
            await loadFavoriteWords()
        }
    }

    // MARK: - Public Methods

    /// 加载词汇列表
    @MainActor
    public func loadWords(category: String? = nil, difficulty: String? = nil) async {
        isLoading = true
        error = nil

        do {
            let loadedWords = try await repository.getWords(category: category, difficulty: difficulty)
            self.words = loadedWords
            self.filteredWords = loadedWords
        } catch {
            self.handleError(error, context: "加载词汇列表")
        }

        isLoading = false
    }

    /// 获取词汇详情
    public func getWordDetail(id: UUID) async throws -> Word {
        return try await repository.getWordDetail(id: id)
    }

    /// 加载词汇分类
    @MainActor
    public func loadCategories() async {
        do {
            let loadedCategories = try await repository.getCategories()
            self.categories = loadedCategories
        } catch {
            self.handleError(error, context: "加载词汇分类")
        }
    }

    /// 加载学习进度
    @MainActor
    public func loadProgress() async {
        do {
            let allProgress = try await repository.getAllWordProgress()
            self.wordProgress = Dictionary(uniqueKeysWithValues: allProgress.map { ($0.wordId, $0) })
        } catch {
            self.handleError(error, context: "加载学习进度")
        }
    }

    /// 更新词汇学习进度
    @MainActor
    public func updateWordProgress(wordId: UUID, correct: Bool, timeSpent: TimeInterval) async {
        do {
            let updatedProgress = try await repository.updateWordProgress(
                wordId: wordId,
                correct: correct,
                timeSpent: timeSpent
            )
            self.wordProgress[wordId] = updatedProgress

            // 如果达到学习标准，标记为已学习
            if updatedProgress.masteryLevel >= 0.8 {
                self.learnedWordIds.insert(wordId)
            }
        } catch {
            self.handleError(error, context: "更新词汇学习进度")
        }
    }

    /// 加载已学习词汇
    @MainActor
    public func loadLearnedWords() async {
        do {
            let learned = try await repository.getLearnedWords()
            self.learnedWordIds = Set(learned.map { $0.id })
        } catch {
            self.handleError(error, context: "加载已学习词汇")
        }
    }

    /// 标记词汇为已学习
    @MainActor
    public func markWordAsLearned(wordId: UUID) async {
        do {
            let success = try await repository.markWordAsLearned(wordId: wordId)
            if success {
                self.learnedWordIds.insert(wordId)
            }
        } catch {
            self.handleError(error, context: "标记词汇为已学习")
        }
    }

    /// 加载收藏词汇
    @MainActor
    public func loadFavoriteWords() async {
        do {
            let favorites = try await repository.getFavoriteWords()
            self.favoriteWordIds = Set(favorites.map { $0.id })
        } catch {
            self.handleError(error, context: "加载收藏词汇")
        }
    }

    /// 切换收藏状态
    @MainActor
    public func toggleFavorite(wordId: UUID) async {
        let isFavorite = favoriteWordIds.contains(wordId)

        do {
            let success = try await repository.toggleFavoriteWord(id: wordId, isFavorite: !isFavorite)
            if success {
                if isFavorite {
                    favoriteWordIds.remove(wordId)
                } else {
                    favoriteWordIds.insert(wordId)
                }
            }
        } catch {
            self.handleError(error, context: "切换收藏状态")
        }
    }

    /// 搜索词汇
    public func searchWords(query: String) {
        searchText = query
    }

    /// 按分类筛选词汇
    @MainActor
    public func filterByCategory(_ category: String?) {
        Task {
            await loadWords(category: category)
        }
    }

    /// 按难度筛选词汇
    @MainActor
    public func filterByDifficulty(_ difficulty: String?) {
        Task {
            await loadWords(difficulty: difficulty)
        }
    }

    /// 获取推荐学习词汇（基于智能优先级算法）
    public func getRecommendedWords(limit: Int = 10) -> [Word] {
        // 计算所有词汇的学习优先级
        let wordsWithPriority = words.compactMap { word -> (Word, Double)? in
            // 跳过已完全掌握的词汇
            if let progress = wordProgress[word.id], progress.masteryLevel >= 0.95 {
                return nil
            }

            let priority = calculateLearningPriority(word: word, progress: wordProgress[word.id])
            return (word, priority)
        }

        // 按优先级排序并返回前N个
        let sortedWords = wordsWithPriority
            .sorted { $0.1 > $1.1 } // 优先级从高到低
            .prefix(limit)
            .map { $0.0 }

        return Array(sortedWords)
    }

    /// 获取需要复习的词汇
    public func getWordsNeedingReview(limit: Int = 20) -> [Word] {
        let needReviewWords = words.filter { word in
            guard let progress = wordProgress[word.id] else { return false }

            let daysSinceLastReview = Calendar.current.dateComponents([.day],
                from: progress.lastReviewedAt, to: Date()).day ?? 0
            let expectedInterval = calculateReviewInterval(
                masteryLevel: progress.masteryLevel,
                reviewCount: progress.totalAttempts,
                lastInterval: progress.reviewInterval
            )

            return daysSinceLastReview >= expectedInterval
        }

        return Array(needReviewWords.prefix(limit))
    }

    /// 获取学习统计
    public func getLearningStats() -> VocabularyLearningStats {
        let totalWords = words.count
        let learnedCount = learnedWordIds.count
        let favoriteCount = favoriteWordIds.count

        let averageMastery = wordProgress.values.isEmpty ? 0.0 :
            wordProgress.values.map { $0.masteryLevel }.reduce(0, +) / Double(wordProgress.count)

        return VocabularyLearningStats(
            totalWords: totalWords,
            learnedWords: learnedCount,
            favoriteWords: favoriteCount,
            averageMasteryLevel: averageMastery,
            studyStreak: calculateStudyStreak()
        )
    }

    /// 检查词汇是否已学习
    public func isWordLearned(wordId: UUID) -> Bool {
        return learnedWordIds.contains(wordId)
    }

    /// 检查词汇是否收藏
    public func isWordFavorite(wordId: UUID) -> Bool {
        return favoriteWordIds.contains(wordId)
    }

    /// 获取词汇进度
    public func getWordProgress(wordId: UUID) -> WordProgress? {
        return wordProgress[wordId]
    }

    // MARK: - Batch Operations

    /// 批量标记词汇为已学习
    @MainActor
    public func batchMarkWordsAsLearned(wordIds: [UUID]) async {
        isLoading = true
        error = nil

        var successCount = 0
        var failedIds: [UUID] = []

        for wordId in wordIds {
            do {
                let success = try await repository.markWordAsLearned(wordId: wordId)
                if success {
                    self.learnedWordIds.insert(wordId)
                    successCount += 1
                } else {
                    failedIds.append(wordId)
                }
            } catch {
                failedIds.append(wordId)
                print("Failed to mark word \(wordId) as learned: \(error)")
            }
        }

        if !failedIds.isEmpty {
            self.error = AppError.progressSyncFailed("批量标记失败：\(failedIds.count) 个词汇")
        }

        isLoading = false
        print("批量标记完成：成功 \(successCount)，失败 \(failedIds.count)")
    }

    /// 批量切换收藏状态
    @MainActor
    public func batchToggleFavorites(wordIds: [UUID], isFavorite: Bool) async {
        isLoading = true
        error = nil

        var successCount = 0
        var failedIds: [UUID] = []

        for wordId in wordIds {
            do {
                let success = try await repository.toggleFavoriteWord(id: wordId, isFavorite: isFavorite)
                if success {
                    if isFavorite {
                        self.favoriteWordIds.insert(wordId)
                    } else {
                        self.favoriteWordIds.remove(wordId)
                    }
                    successCount += 1
                } else {
                    failedIds.append(wordId)
                }
            } catch {
                failedIds.append(wordId)
                print("Failed to toggle favorite for word \(wordId): \(error)")
            }
        }

        if !failedIds.isEmpty {
            self.error = AppError.progressSyncFailed("批量收藏失败：\(failedIds.count) 个词汇")
        }

        isLoading = false
        print("批量收藏完成：成功 \(successCount)，失败 \(failedIds.count)")
    }

    /// 同步离线数据到服务器
    @MainActor
    public func syncOfflineData() async {
        isLoading = true
        error = nil

        do {
            // 这里应该调用 repository 的同步方法
            // 暂时使用占位符实现
            print("开始同步离线数据...")

            // 模拟同步过程
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

            print("离线数据同步完成")
        } catch {
            self.error = AppError.progressSyncFailed("离线数据同步失败：\(error.localizedDescription)")
            print("Failed to sync offline data: \(error)")
        }

        isLoading = false
    }

    // MARK: - Error Handling

    /// 统一错误处理方法
    @MainActor
    private func handleError(_ error: Error, context: String) {
        let appError = AppError.from(error)
        self.error = appError

        // 使用 ErrorHandler 进行统一处理
        ErrorHandler.shared.handle(appError, context: "VocabularyManager - \(context)")

        // 记录详细错误信息
        print("❌ [VocabularyManager] \(context) 失败: \(appError.localizedDescription)")
    }

    /// 处理网络错误的重试逻辑
    @MainActor
    private func handleNetworkError(_ error: Error, retryAction: @escaping () async throws -> Void) async {
        let appError = AppError.from(error)

        // 如果是网络错误，可以考虑重试
        if case .networkError = appError, case .noInternetConnection = appError {
            // 网络错误，暂时不重试，直接处理
            handleError(error, context: "网络请求")
        } else {
            handleError(error, context: "网络请求")
        }
    }

    // MARK: - Private Methods

    private func filterWords() {
        if searchText.isEmpty {
            filteredWords = words
        } else {
            filteredWords = words.filter { word in
                word.text.localizedCaseInsensitiveContains(searchText) ||
                word.translation.localizedCaseInsensitiveContains(searchText) ||
                word.exampleSentence.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    /// 计算复习间隔（基于改进的遗忘曲线算法）
    private func calculateReviewInterval(masteryLevel: Double, reviewCount: Int = 0, lastInterval: Int = 1) -> Int {
        // 改进的SuperMemo SM-2算法
        let easeFactor = max(1.3, 2.5 - (1.0 - masteryLevel) * 2.0)

        switch reviewCount {
        case 0:
            return 1 // 首次复习：1天
        case 1:
            return 6 // 第二次复习：6天
        default:
            // 后续复习：基于遗忘曲线和掌握程度
            let baseInterval = Double(lastInterval) * easeFactor
            let masteryBonus = masteryLevel * 0.5 + 0.5 // 0.5-1.0的加成
            let finalInterval = Int(baseInterval * masteryBonus)
            return max(1, min(finalInterval, 365)) // 限制在1-365天之间
        }
    }

    /// 计算学习优先级
    private func calculateLearningPriority(word: Word, progress: WordProgress?) -> Double {
        guard let progress = progress else { return 1.0 } // 新词汇优先级最高

        let daysSinceLastReview = Calendar.current.dateComponents([.day],
            from: progress.lastReviewedAt, to: Date()).day ?? 0
        let expectedInterval = calculateReviewInterval(
            masteryLevel: progress.masteryLevel,
            reviewCount: progress.totalAttempts,
            lastInterval: progress.reviewInterval
        )

        // 优先级 = 超期程度 + 难度权重 + 错误率权重
        let overdueRatio = Double(daysSinceLastReview) / Double(expectedInterval)
        let difficultyWeight: Double = {
            switch word.difficulty {
            case 3: return 1.5  // 困难
            case 2: return 1.2  // 中等
            default: return 1.0 // 简单
            }
        }()
        let errorRateWeight = progress.incorrectCount > 0 ?
            Double(progress.incorrectCount) / Double(progress.totalAttempts) : 0.0

        return overdueRatio * difficultyWeight + errorRateWeight
    }

    private func calculateStudyStreak() -> Int {
        // 计算连续学习天数
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        var streak = 0
        var currentDate = today

        // 检查最近的学习记录
        let sortedProgress = wordProgress.values.sorted { $0.lastReviewedAt > $1.lastReviewedAt }

        for progress in sortedProgress {
            let progressDate = calendar.startOfDay(for: progress.lastReviewedAt)
            if calendar.isDate(progressDate, inSameDayAs: currentDate) {
                streak += 1
                currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else {
                break
            }
        }

        return streak
    }
}

// MARK: - Combine Support

extension VocabularyManager {
    /// 获取词汇列表的发布者
    public func getWordsPublisher(category: String? = nil, difficulty: String? = nil) -> AnyPublisher<[Word], Error> {
        return repository.getWordsPublisher(category: category, difficulty: difficulty)
    }

    /// 获取词汇详情的发布者
    public func getWordDetailPublisher(id: UUID) -> AnyPublisher<Word, Error> {
        return repository.getWordDetailPublisher(id: id)
    }
}

// MARK: - VocabularyManagerProtocol Implementation
extension VocabularyManager {
    /// 已学习的词汇（计算属性）
    public var learnedWords: [Word] {
        return words.filter { learnedWordIds.contains($0.id) }
    }

    /// 收藏的词汇（计算属性）
    public var favoriteWords: [Word] {
        return words.filter { favoriteWordIds.contains($0.id) }
    }

    /// 添加词汇
    public func addWord(_ word: Word) {
        words.append(word)
        filterWords()
    }

    /// 更新词汇
    public func updateWord(_ word: Word) {
        if let index = words.firstIndex(where: { $0.id == word.id }) {
            words[index] = word
            filterWords()
        }
    }

    /// 删除词汇
    public func deleteWord(_ word: Word) {
        words.removeAll { $0.id == word.id }
        learnedWordIds.remove(word.id)
        favoriteWordIds.remove(word.id)
        wordProgress.removeValue(forKey: word.id)
        filterWords()
    }

    /// 获取词汇进度
    public func getWordProgress(for wordId: UUID) -> WordProgress? {
        return wordProgress[wordId]
    }

    /// 更新词汇进度
    public func updateWordProgress(_ progress: WordProgress) {
        wordProgress[progress.wordId] = progress

        // 如果达到学习标准，标记为已学习
        if progress.masteryLevel >= 0.8 {
            learnedWordIds.insert(progress.wordId)
        }
    }

    /// 标记词汇为已学习
    public func markWordAsLearned(wordId: UUID) {
        Task {
            await markWordAsLearned(wordId: wordId)
        }
    }

    /// 切换收藏状态
    public func toggleFavorite(wordId: UUID) {
        Task {
            await toggleFavorite(wordId: wordId)
        }
    }

    /// 检查是否收藏
    public func isFavorite(wordId: UUID) -> Bool {
        return favoriteWordIds.contains(wordId)
    }

    /// 过滤词汇
    public func filterWords(searchText: String) {
        self.searchText = searchText
        filterWords()
    }

    /// 按分类过滤
    public func filterByCategory(_ category: VocabularyCategory?) {
        selectedCategory = category
        filterWords()
    }



    /// 获取推荐词汇
    public func getRecommendedWords() -> [Word] {
        // 基于学习进度和错误率推荐词汇
        let unlearned = words.filter { !learnedWordIds.contains($0.id) }
        let needReview = words.filter { word in
            if let progress = wordProgress[word.id] {
                let errorRate = progress.totalAttempts > 0 ? Double(progress.incorrectCount) / Double(progress.totalAttempts) : 0.0
                return progress.masteryLevel < 0.6 || errorRate > 0.3
            }
            return false
        }

        return Array((unlearned + needReview).prefix(10))
    }

    /// 获取今日词汇
    public func getTodayWords() -> [Word] {
        // 返回今日推荐学习的词汇
        let calendar = Calendar.current
        let today = Date()

        // 获取今日还未学习的词汇
        let todayWords = words.filter { word in
            if let progress = wordProgress[word.id] {
                return !calendar.isDate(progress.lastReviewedAt, inSameDayAs: today)
            }
            return true
        }

        return Array(todayWords.prefix(5))
    }

    /// 从API加载词汇
    public func loadWordsFromAPI(completion: (() -> Void)?) async {
        await loadWords()
        completion?()
    }

    /// 从API加载分类
    public func loadCategoriesFromAPI(completion: (() -> Void)?) async {
        await loadCategories()
        completion?()
    }

    /// 从API加载词汇进度
    public func loadWordProgressFromAPI(completion: (() -> Void)?) async {
        await loadProgress()
        completion?()
    }

    /// 同步词汇进度到API
    public func syncWordProgressToAPI(_ progress: WordProgress) async {
        // 实现同步逻辑
        do {
            _ = try await repository.updateWordProgress(
                wordId: progress.wordId,
                correct: progress.correctCount > progress.totalAttempts / 2,
                timeSpent: progress.totalTimeSpent
            )
        } catch {
            self.error = error
            print("Failed to sync word progress: \(error)")
        }
    }

    /// 同步收藏状态到API
    public func syncFavoriteStatusToAPI(wordId: UUID, isFavorite: Bool) async {
        // 实现同步逻辑
        do {
            _ = try await repository.toggleFavoriteWord(id: wordId, isFavorite: isFavorite)
        } catch {
            self.error = error
            print("Failed to sync favorite status: \(error)")
        }
    }
}

// MARK: - Supporting Models

public struct VocabularyCategory: Identifiable, Codable {
    public let id: UUID
    public let name: String
    public let description: String
    public let iconName: String
    public let color: String
    public let wordCount: Int

    public init(id: UUID, name: String, description: String, iconName: String, color: String, wordCount: Int) {
        self.id = id
        self.name = name
        self.description = description
        self.iconName = iconName
        self.color = color
        self.wordCount = wordCount
    }
}

public struct VocabularyLearningStats: Codable {
    public let totalWords: Int
    public let learnedWords: Int
    public let favoriteWords: Int
    public let averageMasteryLevel: Double
    public let studyStreak: Int

    public init(totalWords: Int, learnedWords: Int, favoriteWords: Int, averageMasteryLevel: Double, studyStreak: Int) {
        self.totalWords = totalWords
        self.learnedWords = learnedWords
        self.favoriteWords = favoriteWords
        self.averageMasteryLevel = averageMasteryLevel
        self.studyStreak = studyStreak
    }
}
