import Foundation
import Combine

/// 词汇远程数据源实现
public class VocabularyRemoteDataSource: VocabularyRemoteDataSourceProtocol {
    // MARK: - Private Properties
    private let apiClient: APIClientProtocol

    // MARK: - API Endpoints
    private enum Endpoints {
        static let words = "/vocabulary/words"
        static let wordDetail = "/vocabulary/words/{id}"
        static let categories = "/vocabulary/categories"
        static let progress = "/vocabulary/progress"
        static let wordProgress = "/vocabulary/words/{id}/progress"
        static let learnedWords = "/vocabulary/learned"
        static let markLearned = "/vocabulary/words/{id}/learned"
        static let favorites = "/vocabulary/favorites"
        static let toggleFavorite = "/vocabulary/words/{id}/favorite"
    }

    // MARK: - Initialization
    public init(apiClient: APIClientProtocol = APIClient.shared) {
        self.apiClient = apiClient
    }

    // MARK: - Public Methods

    public func getWords(category: String? = nil, difficulty: String? = nil, authToken: String) async throws -> [Word] {
        var urlComponents = URLComponents(url: APIEndpoint.baseURL.appendingPathComponent(Endpoints.words), resolvingAgainstBaseURL: false)!

        var queryItems: [URLQueryItem] = []
        if let category = category {
            queryItems.append(URLQueryItem(name: "category", value: category))
        }
        if let difficulty = difficulty {
            queryItems.append(URLQueryItem(name: "difficulty", value: difficulty))
        }
        urlComponents.queryItems = queryItems.isEmpty ? nil : queryItems

        let endpoint = APIEndpoint.custom(
            url: urlComponents.url!,
            method: "GET",
            headers: [
                "Authorization": "Bearer \\(authToken)",
                "Content-Type": "application/json"
            ],
            bodyData: nil
        )

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(WordListResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw VocabularyRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    public func getWordDetail(id: UUID, authToken: String) async throws -> Word {
        let url = Endpoints.wordDetail.replacingOccurrences(of: "{id}", with: id.uuidString)
        let endpoint = createAuthenticatedEndpoint(url: url, method: "GET", authToken: authToken, userID: nil)

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(WordResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw VocabularyRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    public func getCategories(authToken: String) async throws -> [VocabularyCategory] {
        let endpoint = createAuthenticatedEndpoint(url: Endpoints.categories, method: "GET", authToken: authToken, userID: nil)

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(VocabularyCategoryListResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw VocabularyRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    public func getAllWordProgress(authToken: String, userID: String) async throws -> [WordProgress] {
        let endpoint = createAuthenticatedEndpoint(url: Endpoints.progress, method: "GET", authToken: authToken, userID: userID)

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(WordProgressListResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw VocabularyRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    public func getWordProgress(id: UUID, authToken: String, userID: String) async throws -> WordProgress {
        let url = Endpoints.wordProgress.replacingOccurrences(of: "{id}", with: id.uuidString)
        let endpoint = createAuthenticatedEndpoint(url: url, method: "GET", authToken: authToken, userID: userID)

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(WordProgressResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw VocabularyRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    public func updateWordProgress(_ progress: WordProgress, authToken: String, userID: String) async throws -> WordProgress {
        let url = Endpoints.wordProgress.replacingOccurrences(of: "{id}", with: progress.wordId.uuidString)
        let body = try JSONEncoder().encode(progress)
        let endpoint = createAuthenticatedEndpoint(url: url, method: "PUT", authToken: authToken, userID: userID, body: body)

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(WordProgressResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw VocabularyRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    public func getLearnedWords(authToken: String, userID: String) async throws -> [Word] {
        let endpoint = createAuthenticatedEndpoint(url: Endpoints.learnedWords, method: "GET", authToken: authToken, userID: userID)

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(WordListResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw VocabularyRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    public func markWordAsLearned(wordId: UUID, authToken: String, userID: String) async throws -> Bool {
        let url = Endpoints.markLearned.replacingOccurrences(of: "{id}", with: wordId.uuidString)
        let endpoint = createAuthenticatedEndpoint(url: url, method: "POST", authToken: authToken, userID: userID)

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(VocabularySuccessResponse.self, from: data)

            return response.success
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    public func getFavoriteWords(authToken: String, userID: String) async throws -> [Word] {
        let endpoint = createAuthenticatedEndpoint(url: Endpoints.favorites, method: "GET", authToken: authToken, userID: userID)

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(WordListResponse.self, from: data)

            if response.success {
                return response.data
            } else {
                throw VocabularyRemoteDataSourceError.apiError(response.message ?? "Unknown error")
            }
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    public func toggleFavoriteWord(id: UUID, isFavorite: Bool, authToken: String, userID: String) async throws -> Bool {
        let url = Endpoints.toggleFavorite.replacingOccurrences(of: "{id}", with: id.uuidString)
        let method = isFavorite ? "POST" : "DELETE"
        let endpoint = createAuthenticatedEndpoint(url: url, method: method, authToken: authToken, userID: userID)

        do {
            let dataPublisher = apiClient.request(endpoint: endpoint)
            let data = try await dataPublisher.async()
            let response = try JSONDecoder().decode(VocabularySuccessResponse.self, from: data)

            return response.success
        } catch let decodingError as DecodingError {
            throw VocabularyRemoteDataSourceError.decodingError(decodingError)
        } catch {
            throw VocabularyRemoteDataSourceError.networkError(error)
        }
    }

    // MARK: - Private Helper Methods

    private func createAuthenticatedEndpoint(url: String, method: String, authToken: String, userID: String?, body: Data? = nil) -> APIEndpoint {
        var headers = [
            "Authorization": "Bearer \\(authToken)",
            "Content-Type": "application/json"
        ]
        if let userID = userID {
            headers["User-ID"] = userID
        }

        return APIEndpoint.custom(
            url: APIEndpoint.baseURL.appendingPathComponent(url),
            method: method,
            headers: headers,
            bodyData: body
        )
    }
}

// MARK: - Response Models

public struct WordListResponse: Codable {
    public let success: Bool
    public let data: [Word]
    public let message: String?
    public let error: String?
}

public struct WordResponse: Codable {
    public let success: Bool
    public let data: Word
    public let message: String?
    public let error: String?
}

public struct VocabularyCategoryListResponse: Codable {
    public let success: Bool
    public let data: [VocabularyCategory]
    public let message: String?
    public let error: String?
}

public struct WordProgressListResponse: Codable {
    public let success: Bool
    public let data: [WordProgress]
    public let message: String?
    public let error: String?
}

public struct WordProgressResponse: Codable {
    public let success: Bool
    public let data: WordProgress
    public let message: String?
    public let error: String?
}

// MARK: - Local Response Types

private struct VocabularySuccessResponse: Codable {
    let success: Bool
    let message: String?
    let error: String?
}

// MARK: - Errors

public enum VocabularyRemoteDataSourceError: Error, LocalizedError {
    case apiError(String)
    case networkError(Error)
    case decodingError(Error)
    case invalidResponse

    public var errorDescription: String? {
        switch self {
        case .apiError(let message):
            return "API错误: \(message)"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .invalidResponse:
            return "无效的响应数据"
        }
    }
}
