import Foundation
import Combine

/// 课程仓库协议
public protocol LessonRepositoryProtocol {
    /// 获取课程列表
    func getLessons() async throws -> [Lesson]
    
    /// 获取课程详情
    func getLessonDetail(id: String) async throws -> Lesson
    
    /// 获取所有课程进度
    func getAllProgress() async throws -> [LessonProgress]
    
    /// 获取课程进度
    func getLessonProgress(id: String) async throws -> LessonProgress
    
    /// 更新课程进度
    func updateLessonProgress(id: String, progress: Double, completed: Bool) async throws -> LessonProgress
    
    /// 获取收藏课程
    func getFavoriteLessons() async throws -> [Lesson]
    
    /// 切换收藏状态
    func toggleFavoriteLesson(id: String, isFavorite: Bool) async throws -> Bool
    
    /// Combine 支持
    func getLessonsPublisher() -> AnyPublisher<[Lesson], Error>
    func getLessonDetailPublisher(id: String) -> AnyPublisher<Lesson, Error>
    func getLessonProgressPublisher(id: String) -> AnyPublisher<LessonProgress, Error>
}

/// 课程仓库实现
@MainActor
public class LessonRepository: LessonRepositoryProtocol {
    // MARK: - 单例
    public static let shared = LessonRepository()
    
    // MARK: - Private Properties
    private let remoteDataSource: LessonRemoteDataSourceProtocol
    private let localDataSource: LessonLocalDataSourceProtocol
    
    // MARK: - Initialization
    public init(
        remoteDataSource: LessonRemoteDataSourceProtocol = LessonRemoteDataSource(),
        localDataSource: LessonLocalDataSourceProtocol = LessonLocalDataSource()
    ) {
        self.remoteDataSource = remoteDataSource
        self.localDataSource = localDataSource
    }
    
    // MARK: - Public Methods
    
    public func getLessons() async throws -> [Lesson] {
        guard let authToken = getAuthToken() else { 
            throw LessonRepositoryError.authenticationRequired
        }
        do {
            // 优先从远程获取
            let remoteLessons = try await remoteDataSource.getLessons(authToken: authToken)
            
            // 缓存到本地
            try await localDataSource.saveLessons(remoteLessons)
            
            return remoteLessons
        } catch {
            // 远程失败时从本地获取
            print("Remote fetch failed, falling back to local: \(error)")
            return try await localDataSource.getLessons()
        }
    }
    
    public func getLessonDetail(id: String) async throws -> Lesson {
        guard let authToken = getAuthToken() else { 
            throw LessonRepositoryError.authenticationRequired
        }
        do {
            // 尝试从本地获取
            let localLesson = try await localDataSource.getLessonDetail(id: id)
            print("Fetched lesson detail from local data source: \(localLesson.title)")
            return localLesson
        } catch {
            // 如果本地没有或出错，从远程获取
            print("Failed to get lesson detail from local, fetching from remote: \(error)")
            let remoteLesson = try await remoteDataSource.getLessonDetail(id: id, authToken: authToken)
            // 保存到本地
            try await localDataSource.saveLesson(remoteLesson)
            print("Fetched and saved lesson detail from remote data source: \(remoteLesson.title)")
            return remoteLesson
        }
    }
    
    public func getAllProgress() async throws -> [LessonProgress] {
        guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { 
            throw LessonRepositoryError.authenticationRequired
        }
        do {
            // 优先从远程获取
            let remoteProgress = try await remoteDataSource.getAllProgress(authToken: authToken, userID: userID)
            
            // 缓存到本地
            try await localDataSource.saveAllProgress(remoteProgress)
            
            return remoteProgress
        } catch {
            // 远程失败时从本地获取
            print("Remote fetch failed, falling back to local: \(error)")
            return try await localDataSource.getAllProgress()
        }
    }
    
    public func getLessonProgress(id: String) async throws -> LessonProgress {
        // First try local, then remote if needed
        do {
            return try await localDataSource.getLessonProgress(id: id)
        } catch {
            guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { 
                throw LessonRepositoryError.authenticationRequired
            }
            // 如果本地没有，尝试从远程获取 (如果远程有此功能)
            let remoteProgress = try await remoteDataSource.getLessonProgress(lessonID: id, authToken: authToken, userID: userID)
            try await localDataSource.saveLessonProgress(remoteProgress) // 保存到本地
            return remoteProgress
        }
    }
    
    public func updateLessonProgress(id: String, progress: Double, completed: Bool) async throws -> LessonProgress {
        guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { 
            throw LessonRepositoryError.authenticationRequired
        }
        // 创建或更新 LessonProgress 对象
        var lessonProgress = try await getLessonProgress(id: id)
        lessonProgress.progress = progress
        lessonProgress.completed = completed
        lessonProgress.lastAccessedAt = Date()
        if completed {
            lessonProgress.completedAt = Date()
        }
        
        // 更新本地数据源
        try await localDataSource.saveLessonProgress(lessonProgress)
        
        // 更新远程数据源
        _ = try await remoteDataSource.updateLessonProgress(lessonID: id, progress: progress, completed: completed, authToken: authToken, userID: userID)
        
        return lessonProgress
    }
    
    public func getFavoriteLessons() async throws -> [Lesson] {
        guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { 
            throw LessonRepositoryError.authenticationRequired
        }
        do {
            // 优先从远程获取
            let remoteFavorites = try await remoteDataSource.getFavoriteLessons(authToken: authToken, userID: userID)
            
            // 缓存到本地
            try await localDataSource.saveFavoriteLessons(remoteFavorites)
            
            return remoteFavorites
        } catch {
            // 远程失败时从本地获取
            print("Remote fetch failed, falling back to local: \(error)")
            return try await localDataSource.getFavoriteLessons()
        }
    }
    
    public func toggleFavoriteLesson(id: String, isFavorite: Bool) async throws -> Bool {
        guard let authToken = getAuthToken(), let userID = getCurrentUserIdString() else { 
            throw LessonRepositoryError.authenticationRequired
        }
        // 更新本地数据源
        try await localDataSource.toggleFavoriteLesson(id: id, isFavorite: isFavorite)
        
        // 更新远程数据源
        let success = try await remoteDataSource.toggleFavoriteLesson(lessonID: id, isFavorite: isFavorite, authToken: authToken, userID: userID)
        
        // 如果远程操作成功，标记本地同步状态（如果需要）
        if success {
            try await localDataSource.markFavoriteForSync(id: id, isFavorite: isFavorite)
        }
        return success
    }
    
    // MARK: - Combine Support
    
    public func getLessonsPublisher() -> AnyPublisher<[Lesson], Error> {
        return Deferred {
            Future { promise in
                Task {
                    do {
                        let lessons = try await self.getLessons()
                        promise(.success(lessons))
                    } catch {
                        promise(.failure(error))
                    }
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    public func getLessonDetailPublisher(id: String) -> AnyPublisher<Lesson, Error> {
        return Deferred {
            Future { promise in
                Task {
                    do {
                        let lessonDetail = try await self.getLessonDetail(id: id)
                        promise(.success(lessonDetail))
                    } catch {
                        promise(.failure(error))
                    }
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    public func getLessonProgressPublisher(id: String) -> AnyPublisher<LessonProgress, Error> {
        return Deferred {
            Future { promise in
                Task {
                    do {
                        let progress = try await self.getLessonProgress(id: id)
                        promise(.success(progress))
                    } catch {
                        promise(.failure(error))
                    }
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Private Methods
    
    private func getCurrentUserId() -> UUID? {
        return UserManager.shared.currentUser?.id
    }

    private func getCurrentUserIdString() -> String? {
        return getCurrentUserId()?.uuidString
    }

    private func getAuthToken() -> String? {
        return UserManager.shared.authToken
    }
}

// MARK: - Repository Errors

public enum LessonRepositoryError: Error, LocalizedError {
    case noDataAvailable
    case invalidResponse
    case networkError(Error)
    case localStorageError(Error)
    case authenticationRequired
    
    public var errorDescription: String? {
        switch self {
        case .noDataAvailable:
            return "没有可用的课程数据"
        case .invalidResponse:
            return "无效的响应数据"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .localStorageError(let error):
            return "本地存储错误: \(error.localizedDescription)"
        case .authenticationRequired:
            return "用户认证失败或未登录，无法完成操作。"
        }
    }
}
