import Foundation
import Combine
import SwiftUI

/// 课程管理器，处理课程相关的业务逻辑
public class LessonManager: ObservableObject, LessonManagerProtocol {
    // MARK: - 单例
    public static let shared = LessonManager()

    // MARK: - Published Properties
    @Published public private(set) var lessons: [Lesson] = []
    @Published public private(set) var categories: [LessonCategoryModel] = []
    @Published public private(set) var progress: [String: LessonProgress] = [:]
    @Published public private(set) var completedLessons: [Lesson] = []
    @Published public private(set) var favoriteLessons: Set<String> = []
    @Published public var searchText: String = "" {
        didSet {
            filterLessons()
        }
    }
    @Published public private(set) var filteredLessons: [Lesson] = []
    @Published public private(set) var isLoading = false
    @Published public var error: Error?

    // MARK: - Private Properties
    private let repository: LessonRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    public init(repository: LessonRepositoryProtocol = LessonRepository.shared) {
        self.repository = repository
        setupSubscriptions()
        loadInitialData()
    }

    // MARK: - Setup
    private func setupSubscriptions() {
        // 监听搜索文本变化
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.filterLessons()
            }
            .store(in: &cancellables)
    }

    private func loadInitialData() {
        Task {
            await loadLessons()
            await loadCategories()
            await loadProgress()
            await loadFavorites()
        }
    }

    // MARK: - Public Methods

    /// 加载课程列表
    @MainActor
    public func loadLessons() async {
        isLoading = true
        error = nil

        do {
            let loadedLessons = try await repository.getLessons()
            self.lessons = loadedLessons
            self.filteredLessons = loadedLessons
            updateCompletedLessons()
        } catch {
            self.handleError(error, context: "加载课程列表")
        }

        isLoading = false
    }

    /// 获取课程详情
    public func getLessonDetail(id: String) async throws -> Lesson {
        return try await repository.getLessonDetail(id: id)
    }

    /// 加载课程进度
    @MainActor
    public func loadProgress() async {
        do {
            let allProgress = try await repository.getAllProgress()
            self.progress = Dictionary(uniqueKeysWithValues: allProgress.map { ($0.lessonId, $0) })
        } catch {
            self.handleError(error, context: "加载课程进度")
        }
    }

    /// 更新课程进度
    @MainActor
    public func updateProgress(lessonId: String, progress: Double, completed: Bool) async {
        do {
            let updatedProgress = try await repository.updateLessonProgress(
                id: lessonId,
                progress: progress,
                completed: completed
            )
            self.progress[lessonId] = updatedProgress
            updateCompletedLessons()
        } catch {
            self.handleError(error, context: "更新课程进度")
        }
    }

    /// 加载收藏课程
    @MainActor
    public func loadFavorites() async {
        do {
            let favorites = try await repository.getFavoriteLessons()
            self.favoriteLessons = Set(favorites.map { $0.id })
        } catch {
            self.handleError(error, context: "加载收藏课程")
        }
    }

    /// 切换收藏状态
    @MainActor
    public func toggleFavorite(lessonId: String) async {
        let isFavorite = favoriteLessons.contains(lessonId)

        do {
            let success = try await repository.toggleFavoriteLesson(id: lessonId, isFavorite: !isFavorite)
            if success {
                if isFavorite {
                    favoriteLessons.remove(lessonId)
                } else {
                    favoriteLessons.insert(lessonId)
                }
            }
        } catch {
            self.handleError(error, context: "切换收藏状态")
        }
    }

    /// 加载课程分类
    @MainActor
    public func loadCategories() async {
        // 从课程中提取分类
        let uniqueCategories = Set(lessons.map { $0.category.rawValue })
        self.categories = uniqueCategories.map { categoryName in
            LessonCategoryModel(
                id: UUID(),
                name: categoryName,
                description: "课程分类：\(categoryName)",
                iconName: getCategoryIcon(for: categoryName),
                color: getCategoryColor(for: categoryName)
            )
        }.sorted { $0.name < $1.name }
    }

    /// 搜索课程
    public func searchLessons(query: String) {
        searchText = query
    }

    /// 按分类筛选课程
    @MainActor
    public func filterByCategory(_ category: String?) {
        if let category = category {
            filteredLessons = lessons.filter { $0.category.rawValue == category }
        } else {
            filteredLessons = lessons
        }
    }

    /// 按难度筛选课程
    @MainActor
    public func filterByDifficulty(_ difficulty: String?) {
        if let difficulty = difficulty {
            filteredLessons = lessons.filter { $0.difficulty.rawValue == difficulty }
        } else {
            filteredLessons = lessons
        }
    }

    /// 获取课程进度
    public func getProgress(for lessonId: String) -> LessonProgress? {
        return progress[lessonId]
    }

    /// 检查课程是否收藏
    public func isFavorite(lessonId: String) -> Bool {
        return favoriteLessons.contains(lessonId)
    }

    /// 获取推荐课程（基于智能算法）
    public func getRecommendedLessons(limit: Int = 5) -> [Lesson] {
        // 计算所有课程的推荐优先级
        let lessonsWithPriority = lessons.compactMap { lesson -> (Lesson, Double)? in
            // 跳过已完成的课程
            if getProgress(for: lesson.id)?.completed == true {
                return nil
            }

            let priority = calculateLessonPriority(lesson: lesson)
            return (lesson, priority)
        }

        // 按优先级排序并返回前N个
        let sortedLessons = lessonsWithPriority
            .sorted { $0.1 > $1.1 } // 优先级从高到低
            .prefix(limit)
            .map { $0.0 }

        return Array(sortedLessons)
    }

    // MARK: - Protocol Implementation

    /// 加载课程进度（协议方法）
    public func loadProgress() {
        Task {
            await loadProgress()
        }
    }

    /// 获取已完成的课程（协议方法）
    public func getCompletedLessons() -> [Lesson] {
        return completedLessons
    }

    /// 获取进行中的课程
    public func getInProgressLessons() -> [Lesson] {
        return lessons.filter { lesson in
            let lessonProgress = getProgress(for: lesson.id)
            return lessonProgress?.progress ?? 0 > 0 && lessonProgress?.completed != true
        }
    }

    /// 完成课程
    public func completeLesson(_ lesson: Lesson) {
        Task {
            await updateProgress(lessonId: lesson.id, progress: 1.0, completed: true)
        }
    }

    /// 完成练习
    public func completeExercise(lessonId: String, exerciseId: String, isCorrect: Bool) {
        // 更新练习进度
        let currentProgressValue = getProgress(for: lessonId)?.progress ?? 0
        let newProgress = min(currentProgressValue + 0.1, 1.0) // 每个练习增加10%进度

        Task {
            await updateProgress(lessonId: lessonId, progress: newProgress, completed: newProgress >= 1.0)
        }
    }

    /// 保存进度
    public func saveProgress() {
        // 进度已通过repository自动保存
    }

    /// 添加课程
    public func addLesson(_ lesson: Lesson) {
        lessons.append(lesson)
        filteredLessons = lessons
    }

    /// 更新课程
    public func updateLesson(_ lesson: Lesson) {
        if let index = lessons.firstIndex(where: { $0.id == lesson.id }) {
            lessons[index] = lesson
            filterLessons()
        }
    }

    /// 删除课程
    public func deleteLesson(_ lesson: Lesson) {
        lessons.removeAll { $0.id == lesson.id }
        filterLessons()
    }

    /// 过滤课程（协议方法）
    public func filterLessons(searchText: String) {
        self.searchText = searchText
    }

    /// 切换收藏状态（String版本）
    public func toggleFavorite(lessonId: String) {
        Task {
            await toggleFavorite(lessonId: lessonId)
        }
    }

    /// 获取推荐课程（协议方法）
    public func getRecommendedLessons() -> [Lesson] {
        return getRecommendedLessons(limit: 5)
    }

    /// 从API加载课程
    public func loadLessonsFromAPI(completion: (() -> Void)?) async {
        await loadLessons()
        completion?()
    }

    /// 从API加载收藏课程
    public func loadFavoriteLessonsFromAPI() async {
        await loadFavorites()
    }

    /// 更新课程进度到API
    public func updateLessonProgressToAPI(lessonId: String, progress: Double, completed: Bool) async {
        await updateProgress(lessonId: lessonId, progress: progress, completed: completed)
    }

    /// 更新收藏状态到API
    public func updateFavoriteStatusToAPI(lessonId: String, isFavorite: Bool) async {
        await toggleFavorite(lessonId: lessonId)
    }

    // MARK: - Batch Operations

    /// 批量标记课程为已完成
    @MainActor
    public func batchMarkLessonsAsCompleted(lessonIds: [String]) async {
        isLoading = true
        error = nil

        var successCount = 0
        var failedIds: [String] = []

        for lessonId in lessonIds {
            do {
                try await repository.updateLessonProgress(id: lessonId, progress: 1.0, completed: true)
                successCount += 1
            } catch {
                failedIds.append(lessonId)
                print("Failed to mark lesson \(lessonId) as completed: \(error)")
            }
        }

        // 重新加载进度以更新UI
        await loadProgress()

        if !failedIds.isEmpty {
            self.error = AppError.progressSyncFailed("批量完成失败：\(failedIds.count) 个课程")
        }

        isLoading = false
        print("批量完成课程：成功 \(successCount)，失败 \(failedIds.count)")
    }

    /// 批量切换收藏状态
    @MainActor
    public func batchToggleFavorites(lessonIds: [String], isFavorite: Bool) async {
        isLoading = true
        error = nil

        var successCount = 0
        var failedIds: [String] = []

        for lessonId in lessonIds {
            do {
                try await repository.toggleFavoriteLesson(id: lessonId, isFavorite: isFavorite)
                if isFavorite {
                    self.favoriteLessons.insert(lessonId)
                } else {
                    self.favoriteLessons.remove(lessonId)
                }
                successCount += 1
            } catch {
                failedIds.append(lessonId)
                print("Failed to toggle favorite for lesson \(lessonId): \(error)")
            }
        }

        if !failedIds.isEmpty {
            self.error = AppError.progressSyncFailed("批量收藏失败：\(failedIds.count) 个课程")
        }

        isLoading = false
        print("批量收藏完成：成功 \(successCount)，失败 \(failedIds.count)")
    }

    /// 同步离线数据到服务器
    @MainActor
    public func syncOfflineData() async {
        isLoading = true
        error = nil

        do {
            // 这里应该调用 repository 的同步方法
            // 暂时使用占位符实现
            print("开始同步课程离线数据...")

            // 模拟同步过程
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

            print("课程离线数据同步完成")
        } catch {
            self.error = AppError.progressSyncFailed("课程离线数据同步失败：\(error.localizedDescription)")
            print("Failed to sync lesson offline data: \(error)")
        }

        isLoading = false
    }

    // MARK: - Error Handling

    /// 统一错误处理方法
    @MainActor
    private func handleError(_ error: Error, context: String) {
        let appError = AppError.from(error)
        self.error = appError

        // 使用 ErrorHandler 进行统一处理
        ErrorHandler.shared.handle(appError, context: "LessonManager - \(context)")

        // 记录详细错误信息
        print("❌ [LessonManager] \(context) 失败: \(appError.localizedDescription)")
    }

    /// 处理网络错误的重试逻辑
    @MainActor
    private func handleNetworkError(_ error: Error, retryAction: @escaping () async throws -> Void) async {
        let appError = AppError.from(error)

        // 如果是网络错误，可以考虑重试
        if case .networkError = appError, case .noInternetConnection = appError {
            // 网络错误，暂时不重试，直接处理
            handleError(error, context: "网络请求")
        } else {
            handleError(error, context: "网络请求")
        }
    }

    // MARK: - Smart Recommendation Algorithm

    /// 计算课程学习优先级
    private func calculateLessonPriority(lesson: Lesson) -> Double {
        let progress = getProgress(for: lesson.id)

        // 基础权重
        var priority: Double = 1.0

        // 1. 进度权重 - 已开始但未完成的课程优先级更高
        if let progress = progress {
            let progressValue = progress.progress
            if progressValue > 0 && progressValue < 1.0 {
                priority += 2.0 // 进行中的课程优先级最高
            } else if progressValue == 0 {
                priority += 1.0 // 未开始的课程次之
            }
        } else {
            priority += 1.0 // 新课程
        }

        // 2. 难度权重 - 根据用户当前水平调整
        let userLevel = getCurrentUserLevel()
        let difficultyMatch = calculateDifficultyMatch(lesson: lesson, userLevel: userLevel)
        priority += difficultyMatch

        // 3. 类别权重 - 根据用户学习偏好
        let categoryWeight = getCategoryWeight(lesson.category)
        priority += categoryWeight

        // 4. 时间权重 - 最近更新的课程优先级稍高
        let timeWeight = calculateTimeWeight(lesson: lesson)
        priority += timeWeight

        return priority
    }

    /// 获取当前用户水平
    private func getCurrentUserLevel() -> Int {
        let completedCount = completedLessons.count
        let totalCount = lessons.count

        if totalCount == 0 { return 1 }

        let completionRate = Double(completedCount) / Double(totalCount)

        switch completionRate {
        case 0.0..<0.2: return 1 // 初级
        case 0.2..<0.5: return 2 // 中级
        case 0.5..<0.8: return 3 // 高级
        default: return 4 // 专家级
        }
    }

    /// 计算难度匹配度
    private func calculateDifficultyMatch(lesson: Lesson, userLevel: Int) -> Double {
        let lessonDifficultyLevel = getDifficultyLevel(lesson.difficulty)
        let levelDifference = abs(lessonDifficultyLevel - userLevel)

        switch levelDifference {
        case 0: return 1.0 // 完全匹配
        case 1: return 0.5 // 接近匹配
        default: return 0.0 // 不匹配
        }
    }

    /// 将 LessonDifficulty 转换为数字级别
    private func getDifficultyLevel(_ difficulty: LessonDifficulty) -> Int {
        switch difficulty {
        case .easy: return 1
        case .medium: return 2
        case .hard: return 3
        }
    }

    /// 获取类别权重
    private func getCategoryWeight(_ category: LessonCategory) -> Double {
        // 可以根据用户的学习历史和偏好来调整
        // 这里使用简单的权重分配
        switch category {
        case .vocabulary: return 0.7
        case .grammar: return 0.6
        case .listening: return 0.8
        case .speaking: return 0.9
        case .reading: return 0.5
        case .writing: return 0.6
        case .uncategorized: return 0.3
        }
    }

    /// 计算时间权重
    private func calculateTimeWeight(lesson: Lesson) -> Double {
        // 简单的时间权重，可以根据课程的创建时间或更新时间来调整
        // 这里返回固定值，实际应用中可以根据具体需求调整
        return 0.1
    }

    // MARK: - Private Methods

    private func filterLessons() {
        if searchText.isEmpty {
            filteredLessons = lessons
        } else {
            filteredLessons = lessons.filter { lesson in
                lesson.title.localizedCaseInsensitiveContains(searchText) ||
                lesson.description.localizedCaseInsensitiveContains(searchText) ||
                lesson.category.rawValue.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    private func updateCompletedLessons() {
        completedLessons = lessons.filter { lesson in
            getProgress(for: lesson.id)?.completed == true
        }
    }

    private func getCategoryIcon(for category: String) -> String {
        switch category.lowercased() {
        case "基础", "beginner":
            return "star.fill"
        case "语法", "grammar":
            return "text.book.closed"
        case "词汇", "vocabulary":
            return "textformat.abc"
        case "对话", "conversation":
            return "bubble.left.and.bubble.right"
        case "听力", "listening":
            return "ear"
        case "阅读", "reading":
            return "book"
        default:
            return "folder"
        }
    }

    private func getCategoryColor(for category: String) -> String {
        switch category.lowercased() {
        case "基础", "beginner":
            return "blue"
        case "语法", "grammar":
            return "green"
        case "词汇", "vocabulary":
            return "orange"
        case "对话", "conversation":
            return "purple"
        case "听力", "listening":
            return "red"
        case "阅读", "reading":
            return "pink"
        default:
            return "gray"
        }
    }

    // MARK: - Enhanced Batch Operations

    /// 批量添加课程到收藏
    @MainActor
    public func batchAddToFavorites(lessonIds: [String]) async {
        isLoading = true
        error = nil

        var successCount = 0
        var failedIds: [String] = []

        for lessonId in lessonIds {
            do {
                _ = try await repository.toggleFavoriteLesson(id: lessonId, isFavorite: true)
                favoriteLessons.insert(lessonId)
                successCount += 1
            } catch {
                failedIds.append(lessonId)
                print("Failed to add lesson \(lessonId) to favorites: \(error)")
            }
        }

        if !failedIds.isEmpty {
            self.handleError(
                AppError.dataSaveFailed("批量收藏失败：\(failedIds.count) 个课程"),
                context: "批量添加收藏"
            )
        }

        isLoading = false
        print("批量添加收藏：成功 \(successCount)，失败 \(failedIds.count)")
    }

    /// 批量从收藏中移除课程
    @MainActor
    public func batchRemoveFromFavorites(lessonIds: [String]) async {
        isLoading = true
        error = nil

        var successCount = 0
        var failedIds: [String] = []

        for lessonId in lessonIds {
            do {
                _ = try await repository.toggleFavoriteLesson(id: lessonId, isFavorite: false)
                favoriteLessons.remove(lessonId)
                successCount += 1
            } catch {
                failedIds.append(lessonId)
                print("Failed to remove lesson \(lessonId) from favorites: \(error)")
            }
        }

        if !failedIds.isEmpty {
            self.handleError(
                AppError.dataSaveFailed("批量取消收藏失败：\(failedIds.count) 个课程"),
                context: "批量移除收藏"
            )
        }

        isLoading = false
        print("批量移除收藏：成功 \(successCount)，失败 \(failedIds.count)")
    }

    /// 批量删除课程（仅从本地移除）
    @MainActor
    public func batchDeleteLessons(lessonIds: [String]) async {
        isLoading = true
        error = nil

        var successCount = 0

        for lessonId in lessonIds {
            // 仅从本地数组中移除，不调用远程API
            if lessons.contains(where: { $0.id == lessonId }) {
                lessons.removeAll { $0.id == lessonId }
                favoriteLessons.remove(lessonId)
                successCount += 1
            }
        }

        // 更新过滤后的课程列表
        filterLessons()
        updateCompletedLessons()

        isLoading = false
        print("批量删除课程（本地）：成功 \(successCount)")
    }

    /// 同步所有课程数据
    @MainActor
    public func syncAllLessonData() async {
        isLoading = true
        error = nil

        do {
            // 同步课程列表
            await loadLessons()

            // 同步分类
            await loadCategories()

            // 同步进度
            await loadProgress()

            // 同步收藏
            await loadFavorites()

            print("课程数据同步完成")
        } catch {
            self.handleError(error, context: "同步课程数据")
        }

        isLoading = false
    }

    // MARK: - Enhanced Smart Algorithms

    /// 获取个性化学习路径
    public func getPersonalizedLearningPath(targetLevel: Int = 3, maxLessons: Int = 20) -> [Lesson] {
        let userLevel = getCurrentUserLevel()
        let availableLessons = lessons.filter { lesson in
            // 排除已完成的课程
            getProgress(for: lesson.id)?.completed != true
        }

        // 按学习路径优先级排序
        let pathLessons = availableLessons.compactMap { lesson -> (Lesson, Double)? in
            let pathPriority = calculateLearningPathPriority(
                lesson: lesson,
                userLevel: userLevel,
                targetLevel: targetLevel
            )
            return (lesson, pathPriority)
        }
        .sorted { $0.1 > $1.1 }
        .prefix(maxLessons)
        .map { $0.0 }

        return Array(pathLessons)
    }

    /// 计算学习路径优先级
    private func calculateLearningPathPriority(lesson: Lesson, userLevel: Int, targetLevel: Int) -> Double {
        var priority: Double = 1.0

        // 1. 难度匹配度 - 适合当前水平的课程优先级更高
        let difficultyMatch = calculateDifficultyMatch(lesson: lesson, userLevel: userLevel)
        priority += difficultyMatch * 3.0

        // 2. 目标导向 - 朝向目标水平的课程优先级更高
        let targetAlignment = calculateTargetAlignment(lesson: lesson, targetLevel: targetLevel)
        priority += targetAlignment * 2.0

        // 3. 前置课程完成度 - 前置课程完成的优先级更高
        let prerequisiteCompletion = calculatePrerequisiteCompletion(lesson: lesson)
        priority += prerequisiteCompletion * 2.5

        // 4. 学习效果预测 - 基于历史数据预测学习效果
        let effectivenessPrediction = predictLearningEffectiveness(lesson: lesson)
        priority += effectivenessPrediction * 1.5

        return priority
    }

    /// 计算目标对齐度
    private func calculateTargetAlignment(lesson: Lesson, targetLevel: Int) -> Double {
        let lessonLevel = getDifficultyLevel(lesson.difficulty)

        // 如果课程难度在当前水平和目标水平之间，优先级更高
        if lessonLevel <= targetLevel {
            return 1.0 - (Double(targetLevel - lessonLevel) / Double(targetLevel))
        } else {
            return 0.2 // 超出目标水平的课程优先级较低
        }
    }

    /// 计算前置课程完成度
    private func calculatePrerequisiteCompletion(lesson: Lesson) -> Double {
        // 简化实现：基于同类别的已完成课程数量
        let sameCategoryLessons = lessons.filter { $0.category == lesson.category }
        let completedInCategory = sameCategoryLessons.filter {
            getProgress(for: $0.id)?.completed == true
        }.count

        if sameCategoryLessons.count == 0 { return 1.0 }

        return Double(completedInCategory) / Double(sameCategoryLessons.count)
    }

    /// 预测学习效果
    private func predictLearningEffectiveness(lesson: Lesson) -> Double {
        // 基于用户在相似课程上的表现预测
        let similarLessons = lessons.filter {
            $0.category == lesson.category &&
            $0.difficulty == lesson.difficulty &&
            getProgress(for: $0.id)?.completed == true
        }

        if similarLessons.isEmpty { return 0.5 } // 默认中等效果

        let averageScore = similarLessons.compactMap {
            getProgress(for: $0.id)?.score
        }.reduce(0, +) / Double(similarLessons.count)

        return averageScore / 100.0 // 标准化到0-1
    }

    /// 获取学习统计分析
    public func getLearningAnalytics() -> LearningAnalytics {
        let totalLessons = lessons.count
        let completedCount = completedLessons.count
        let inProgressCount = getInProgressLessons().count

        let completionRate = totalLessons > 0 ? Double(completedCount) / Double(totalLessons) : 0.0

        // 按类别统计
        let categoryStats = Dictionary(grouping: lessons, by: { $0.category })
            .mapValues { categoryLessons in
                let completed = categoryLessons.filter {
                    getProgress(for: $0.id)?.completed == true
                }.count
                return CategoryStats(
                    total: categoryLessons.count,
                    completed: completed,
                    completionRate: Double(completed) / Double(categoryLessons.count)
                )
            }

        return LearningAnalytics(
            totalLessons: totalLessons,
            completedLessons: completedCount,
            inProgressLessons: inProgressCount,
            completionRate: completionRate,
            categoryStats: categoryStats,
            currentLevel: getCurrentUserLevel(),
            recommendedNextSteps: getPersonalizedLearningPath(maxLessons: 3)
        )
    }

    // MARK: - Error Handling

    /// 统一错误处理方法
    @MainActor
    private func handleError(_ error: Error, context: String) {
        let appError = AppError.from(error)
        self.error = appError

        // 使用 ErrorHandler 进行统一处理
        ErrorHandler.shared.handle(appError, context: "LessonManager - \(context)")

        // 记录详细错误信息
        print("❌ [LessonManager] \(context) 失败: \(appError.localizedDescription)")
    }

    /// 处理网络错误的重试逻辑
    @MainActor
    private func handleNetworkError(_ error: Error, retryAction: @escaping () async throws -> Void) async {
        let appError = AppError.from(error)

        // 如果是网络错误，可以考虑重试
        if case .networkError = appError, case .noInternetConnection = appError {
            // 网络错误，暂时不重试，直接处理
            handleError(error, context: "网络请求")
        } else {
            handleError(error, context: "网络请求")
        }
    }
}

// MARK: - Supporting Types

/// 学习分析数据
public struct LearningAnalytics {
    public let totalLessons: Int
    public let completedLessons: Int
    public let inProgressLessons: Int
    public let completionRate: Double
    public let categoryStats: [LessonCategory: CategoryStats]
    public let currentLevel: Int
    public let recommendedNextSteps: [Lesson]
}

/// 类别统计
public struct CategoryStats {
    public let total: Int
    public let completed: Int
    public let completionRate: Double
}

// MARK: - Combine Support

extension LessonManager {
    /// 获取课程列表的发布者
    public func getLessonsPublisher() -> AnyPublisher<[Lesson], Error> {
        return repository.getLessonsPublisher()
    }

    /// 获取课程详情的发布者
    public func getLessonDetailPublisher(id: String) -> AnyPublisher<Lesson, Error> {
        return repository.getLessonDetailPublisher(id: id)
    }

    /// 获取课程进度的发布者
    public func getLessonProgressPublisher(id: String) -> AnyPublisher<LessonProgress, Error> {
        return repository.getLessonProgressPublisher(id: id)
    }
}


