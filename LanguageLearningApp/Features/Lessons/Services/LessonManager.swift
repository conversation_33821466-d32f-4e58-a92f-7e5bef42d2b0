import Foundation
import Combine
import SwiftUI

/// 课程管理器，处理课程相关的业务逻辑
public class LessonManager: ObservableObject, LessonManagerProtocol {
    // MARK: - 单例
    public static let shared = LessonManager()

    // MARK: - Published Properties
    @Published public private(set) var lessons: [Lesson] = []
    @Published public private(set) var categories: [LessonCategoryModel] = []
    @Published public private(set) var progress: [String: LessonProgress] = [:]
    @Published public private(set) var completedLessons: [Lesson] = []
    @Published public private(set) var favoriteLessons: Set<String> = []
    @Published public var searchText: String = "" {
        didSet {
            filterLessons()
        }
    }
    @Published public private(set) var filteredLessons: [Lesson] = []
    @Published public private(set) var isLoading = false
    @Published public var error: Error?

    // MARK: - Private Properties
    private let repository: LessonRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    public init(repository: LessonRepositoryProtocol = LessonRepository.shared) {
        self.repository = repository
        setupSubscriptions()
        loadInitialData()
    }

    // MARK: - Setup
    private func setupSubscriptions() {
        // 监听搜索文本变化
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.filterLessons()
            }
            .store(in: &cancellables)
    }

    private func loadInitialData() {
        Task {
            await loadLessons()
            await loadCategories()
            await loadProgress()
            await loadFavorites()
        }
    }

    // MARK: - Public Methods

    /// 加载课程列表
    @MainActor
    public func loadLessons() async {
        isLoading = true
        error = nil

        do {
            let loadedLessons = try await repository.getLessons()
            self.lessons = loadedLessons
            self.filteredLessons = loadedLessons
            updateCompletedLessons()
        } catch {
            self.error = error
            print("Failed to load lessons: \(error)")
        }

        isLoading = false
    }

    /// 获取课程详情
    public func getLessonDetail(id: String) async throws -> Lesson {
        return try await repository.getLessonDetail(id: id)
    }

    /// 加载课程进度
    @MainActor
    public func loadProgress() async {
        do {
            let allProgress = try await repository.getAllProgress()
            self.progress = Dictionary(uniqueKeysWithValues: allProgress.map { ($0.lessonId, $0) })
        } catch {
            self.error = error
            print("Failed to load progress: \(error)")
        }
    }

    /// 更新课程进度
    @MainActor
    public func updateProgress(lessonId: String, progress: Double, completed: Bool) async {
        do {
            let updatedProgress = try await repository.updateLessonProgress(
                id: lessonId,
                progress: progress,
                completed: completed
            )
            self.progress[lessonId] = updatedProgress
            updateCompletedLessons()
        } catch {
            self.error = error
            print("Failed to update progress: \(error)")
        }
    }

    /// 加载收藏课程
    @MainActor
    public func loadFavorites() async {
        do {
            let favorites = try await repository.getFavoriteLessons()
            self.favoriteLessons = Set(favorites.map { $0.id })
        } catch {
            self.error = error
            print("Failed to load favorites: \(error)")
        }
    }

    /// 切换收藏状态
    @MainActor
    public func toggleFavorite(lessonId: String) async {
        let isFavorite = favoriteLessons.contains(lessonId)

        do {
            let success = try await repository.toggleFavoriteLesson(id: lessonId, isFavorite: !isFavorite)
            if success {
                if isFavorite {
                    favoriteLessons.remove(lessonId)
                } else {
                    favoriteLessons.insert(lessonId)
                }
            }
        } catch {
            self.error = error
            print("Failed to toggle favorite: \(error)")
        }
    }

    /// 加载课程分类
    @MainActor
    public func loadCategories() async {
        // 从课程中提取分类
        let uniqueCategories = Set(lessons.map { $0.category.rawValue })
        self.categories = uniqueCategories.map { categoryName in
            LessonCategoryModel(
                id: UUID(),
                name: categoryName,
                description: "课程分类：\(categoryName)",
                iconName: getCategoryIcon(for: categoryName),
                color: getCategoryColor(for: categoryName)
            )
        }.sorted { $0.name < $1.name }
    }

    /// 搜索课程
    public func searchLessons(query: String) {
        searchText = query
    }

    /// 按分类筛选课程
    @MainActor
    public func filterByCategory(_ category: String?) {
        if let category = category {
            filteredLessons = lessons.filter { $0.category.rawValue == category }
        } else {
            filteredLessons = lessons
        }
    }

    /// 按难度筛选课程
    @MainActor
    public func filterByDifficulty(_ difficulty: String?) {
        if let difficulty = difficulty {
            filteredLessons = lessons.filter { $0.difficulty.rawValue == difficulty }
        } else {
            filteredLessons = lessons
        }
    }

    /// 获取课程进度
    public func getProgress(for lessonId: String) -> LessonProgress? {
        return progress[lessonId]
    }

    /// 检查课程是否收藏
    public func isFavorite(lessonId: String) -> Bool {
        return favoriteLessons.contains(lessonId)
    }

    /// 获取推荐课程
    public func getRecommendedLessons(limit: Int = 5) -> [Lesson] {
        // 基于用户进度和偏好推荐课程
        let incompleteLessons = lessons.filter { lesson in
            let lessonProgress = getProgress(for: lesson.id)
            return lessonProgress?.completed != true
        }

        // 优先推荐用户当前难度级别的课程
        let sortedLessons = incompleteLessons.sorted { lesson1, lesson2 in
            let progress1 = getProgress(for: lesson1.id)?.progress ?? 0
            let progress2 = getProgress(for: lesson2.id)?.progress ?? 0
            return progress1 > progress2
        }

        return Array(sortedLessons.prefix(limit))
    }

    // MARK: - Protocol Implementation

    /// 加载课程进度（协议方法）
    public func loadProgress() {
        Task {
            await loadProgress()
        }
    }

    /// 获取已完成的课程（协议方法）
    public func getCompletedLessons() -> [Lesson] {
        return completedLessons
    }

    /// 获取进行中的课程
    public func getInProgressLessons() -> [Lesson] {
        return lessons.filter { lesson in
            let lessonProgress = getProgress(for: lesson.id)
            return lessonProgress?.progress ?? 0 > 0 && lessonProgress?.completed != true
        }
    }

    /// 完成课程
    public func completeLesson(_ lesson: Lesson) {
        Task {
            await updateProgress(lessonId: lesson.id, progress: 1.0, completed: true)
        }
    }

    /// 完成练习
    public func completeExercise(lessonId: String, exerciseId: String, isCorrect: Bool) {
        // 更新练习进度
        let currentProgressValue = getProgress(for: lessonId)?.progress ?? 0
        let newProgress = min(currentProgressValue + 0.1, 1.0) // 每个练习增加10%进度

        Task {
            await updateProgress(lessonId: lessonId, progress: newProgress, completed: newProgress >= 1.0)
        }
    }

    /// 保存进度
    public func saveProgress() {
        // 进度已通过repository自动保存
    }

    /// 添加课程
    public func addLesson(_ lesson: Lesson) {
        lessons.append(lesson)
        filteredLessons = lessons
    }

    /// 更新课程
    public func updateLesson(_ lesson: Lesson) {
        if let index = lessons.firstIndex(where: { $0.id == lesson.id }) {
            lessons[index] = lesson
            filterLessons()
        }
    }

    /// 删除课程
    public func deleteLesson(_ lesson: Lesson) {
        lessons.removeAll { $0.id == lesson.id }
        filterLessons()
    }

    /// 过滤课程（协议方法）
    public func filterLessons(searchText: String) {
        self.searchText = searchText
    }

    /// 切换收藏状态（String版本）
    public func toggleFavorite(lessonId: String) {
        Task {
            await toggleFavorite(lessonId: lessonId)
        }
    }

    /// 获取推荐课程（协议方法）
    public func getRecommendedLessons() -> [Lesson] {
        return getRecommendedLessons(limit: 5)
    }

    /// 从API加载课程
    public func loadLessonsFromAPI(completion: (() -> Void)?) async {
        await loadLessons()
        completion?()
    }

    /// 从API加载收藏课程
    public func loadFavoriteLessonsFromAPI() async {
        await loadFavorites()
    }

    /// 更新课程进度到API
    public func updateLessonProgressToAPI(lessonId: String, progress: Double, completed: Bool) async {
        await updateProgress(lessonId: lessonId, progress: progress, completed: completed)
    }

    /// 更新收藏状态到API
    public func updateFavoriteStatusToAPI(lessonId: String, isFavorite: Bool) async {
        await toggleFavorite(lessonId: lessonId)
    }

    // MARK: - Private Methods

    private func filterLessons() {
        if searchText.isEmpty {
            filteredLessons = lessons
        } else {
            filteredLessons = lessons.filter { lesson in
                lesson.title.localizedCaseInsensitiveContains(searchText) ||
                lesson.description.localizedCaseInsensitiveContains(searchText) ||
                lesson.category.rawValue.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    private func updateCompletedLessons() {
        completedLessons = lessons.filter { lesson in
            getProgress(for: lesson.id)?.completed == true
        }
    }

    private func getCategoryIcon(for category: String) -> String {
        switch category.lowercased() {
        case "基础", "beginner":
            return "star.fill"
        case "语法", "grammar":
            return "text.book.closed"
        case "词汇", "vocabulary":
            return "textformat.abc"
        case "对话", "conversation":
            return "bubble.left.and.bubble.right"
        case "听力", "listening":
            return "ear"
        case "阅读", "reading":
            return "book"
        default:
            return "folder"
        }
    }

    private func getCategoryColor(for category: String) -> String {
        switch category.lowercased() {
        case "基础", "beginner":
            return "blue"
        case "语法", "grammar":
            return "green"
        case "词汇", "vocabulary":
            return "orange"
        case "对话", "conversation":
            return "purple"
        case "听力", "listening":
            return "red"
        case "阅读", "reading":
            return "pink"
        default:
            return "gray"
        }
    }
}

// MARK: - Combine Support

extension LessonManager {
    /// 获取课程列表的发布者
    public func getLessonsPublisher() -> AnyPublisher<[Lesson], Error> {
        return repository.getLessonsPublisher()
    }

    /// 获取课程详情的发布者
    public func getLessonDetailPublisher(id: String) -> AnyPublisher<Lesson, Error> {
        return repository.getLessonDetailPublisher(id: id)
    }

    /// 获取课程进度的发布者
    public func getLessonProgressPublisher(id: String) -> AnyPublisher<LessonProgress, Error> {
        return repository.getLessonProgressPublisher(id: id)
    }
}


