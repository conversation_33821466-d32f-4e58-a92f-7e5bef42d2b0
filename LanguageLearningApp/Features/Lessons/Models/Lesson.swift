import Foundation

/// 课程模型
public struct Lesson: Codable, Identifiable {
    /// 课程ID
    public let id: String
    /// 课程标题
    public let title: String
    /// 课程描述
    public let description: String
    /// 课程类别
    public let category: LessonCategory
    /// 课程级别
    public let level: LessonLevel
    /// 课程难度
    public let difficulty: LessonDifficulty
    /// 课程时长（分钟）
    public let duration: Int
    /// 课程积分
    public let points: Int
    /// 课程标签
    public let tags: [String]
    /// 课程内容
    public let content: [LessonContent]
    /// 课程图片URL
    public let imageURL: URL?
    /// 课程音频URL
    public let audioURL: URL?
    /// 课程视频URL
    public let videoURL: URL?
    /// 创建时间
    public let createdAt: Date
    /// 更新时间
    public let updatedAt: Date
    /// 是否发布
    public let isPublished: Bool
    /// 是否推荐
    public let isRecommended: Bool
    /// 完成人数
    public let completedCount: Int
    /// 平均评分
    public let averageRating: Double
    /// 评分人数
    public let ratingCount: Int
    
    /// 示例课程
    public static let sample = Lesson(
        id: "sample1",
        title: "Sample Lesson",
        description: "This is a sample lesson for preview purposes.",
        category: .vocabulary,
        level: .beginner,
        difficulty: .easy,
        duration: 15,
        points: 10,
        tags: ["sample", "preview", "test"]
    )
    
    /// 初始化方法
    public init(
        id: String,
        title: String,
        description: String,
        category: LessonCategory,
        level: LessonLevel,
        difficulty: LessonDifficulty,
        duration: Int,
        points: Int,
        tags: [String],
        content: [LessonContent] = [],
        imageURL: URL? = nil,
        audioURL: URL? = nil,
        videoURL: URL? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        isPublished: Bool = true,
        isRecommended: Bool = false,
        completedCount: Int = 0,
        averageRating: Double = 0,
        ratingCount: Int = 0
    ) {
        self.id = id
        self.title = title
        self.description = description
        self.category = category
        self.level = level
        self.difficulty = difficulty
        self.duration = duration
        self.points = points
        self.tags = tags
        self.content = content
        self.imageURL = imageURL
        self.audioURL = audioURL
        self.videoURL = videoURL
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.isPublished = isPublished
        self.isRecommended = isRecommended
        self.completedCount = completedCount
        self.averageRating = averageRating
        self.ratingCount = ratingCount
    }
}

/// 课程内容类型
public enum LessonContentType: String, Codable {
    case text = "text"
    case image = "image"
    case audio = "audio"
    case video = "video"
    case quiz = "quiz"
    case exercise = "exercise"
}

/// 课程内容
public struct LessonContent: Codable, Identifiable {
    /// 内容ID
    public let id: UUID
    /// 内容类型
    public let type: LessonContentType
    /// 内容标题
    public let title: String
    /// 内容描述
    public let description: String
    /// 内容数据
    public let data: [String: String]
    /// 排序顺序
    public let order: Int
    
    /// 初始化方法
    public init(
        id: UUID = UUID(),
        type: LessonContentType,
        title: String,
        description: String,
        data: [String: String],
        order: Int
    ) {
        self.id = id
        self.type = type
        self.title = title
        self.description = description
        self.data = data
        self.order = order
    }
}

// Note: LessonCategory, LessonLevel, LessonDifficulty enums are expected to be found in SharedModels or similar.
// If they are defined in LanguageLearningApp/SharedModels/LessonTypes.swift, ensure imports if necessary. 