import Foundation

/// 课程分类模型，用于 UI 显示
public struct LessonCategoryModel: Identifiable, Codable {
    public let id: UUID
    public let name: String
    public let description: String
    public let iconName: String
    public let color: String
    
    public init(id: UUID, name: String, description: String, iconName: String, color: String) {
        self.id = id
        self.name = name
        self.description = description
        self.iconName = iconName
        self.color = color
    }
}
