import SwiftUI
import Combine
import UIKit

// MARK: - Styled Container
struct StyledContainer<Content: View>: View {
    let content: Content

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        ZStack {
            // Background gradient
            AppTheme.Colors.background
                .ignoresSafeArea()

            // Decorative elements - glowing circles
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            AppTheme.Colors.secondary.opacity(0.3),
                            AppTheme.Colors.secondary.opacity(0.0)
                        ]),
                        center: .center,
                        startRadius: 1,
                        endRadius: 150
                    )
                )
                .frame(width: 300, height: 300)
                .offset(x: 150, y: -100)
                .blur(radius: 30)

            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            AppTheme.Colors.primary.opacity(0.3),
                            AppTheme.Colors.primary.opacity(0.0)
                        ]),
                        center: .center,
                        startRadius: 1,
                        endRadius: 120
                    )
                )
                .frame(width: 250, height: 250)
                .offset(x: -150, y: 200)
                .blur(radius: 30)

            // Content
            ScrollView {
                VStack(spacing: 24) {
                    content
                }
                .padding(.horizontal)
                .padding(.top, 20)
                .padding(.bottom, 50)
            }
        }
    }
}

// MARK: - Styled Card
struct StyledCard<Content: View>: View {
    let title: String
    let content: Content

    init(title: String = "", @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            if !title.isEmpty {
                Text(title)
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .padding(.leading, 16)
                    .padding(.top, 8)
            }

            VStack(spacing: 1) {
                content
            }
            .cardStyle()
        }
    }
}

// MARK: - Styled Button
struct StyledButton: View {
    let title: String
    let action: () -> Void
    var icon: String? = nil
    var isPrimary: Bool = true
    var isDestructive: Bool = false
    var isLoading: Bool = false
    var isDisabled: Bool = false
    var allowLongPressGesture: Bool = true
    @State private var isPressed: Bool = false

    var body: some View {
        Button(action: action) {
            ZStack {
                if isPrimary {
                    // Primary button with gradient
                    LinearGradient(
                        gradient: Gradient(colors: isDestructive ?
                                          [AppTheme.Colors.secondary, AppTheme.Colors.error] :
                                          [AppTheme.Colors.primary, AppTheme.Colors.accent3]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                    .opacity(isDisabled ? 0.5 : 1.0)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.3),
                                        Color.white.opacity(0.1)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .scaleEffect(isPressed ? 0.98 : 1)
                    .shadow(color: isDestructive ?
                            AppTheme.Colors.secondary.opacity(0.5) :
                            AppTheme.Colors.primary.opacity(0.5),
                            radius: isPressed ? 5 : 10, x: 0, y: isPressed ? 2 : 5)
                } else {
                    // Secondary button with glass effect
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .fill(AppTheme.Colors.card)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            isDestructive ? AppTheme.Colors.secondary.opacity(0.5) : Color.white.opacity(0.2),
                                            isDestructive ? AppTheme.Colors.error.opacity(0.2) : Color.white.opacity(0.05)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1.5
                                )
                        )
                        .opacity(isDisabled ? 0.5 : 1.0)
                        .scaleEffect(isPressed ? 0.98 : 1)
                }

                if isLoading {
                    // Loading indicator
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                } else {
                    // Button content
                    HStack(spacing: 12) {
                        if let icon = icon {
                            Image(systemName: icon)
                                .font(.system(size: AppTheme.Dimensions.iconSizeSmall + 2, weight: .semibold))
                        }

                        Text(title)
                            .font(AppTheme.Typography.headline)
                    }
                    .foregroundColor(isDestructive && !isPrimary ? AppTheme.Colors.secondary : (isPrimary ? .white : AppTheme.Colors.textPrimary))
                    .padding(.vertical, 16)
                    .frame(maxWidth: .infinity)
                }
            }
            .frame(height: AppTheme.Dimensions.buttonHeight)
            .animation(.spring(response: 0.3), value: isPressed)
            // if allowLongPressGesture {
            //     self.onLongPressGesture(minimumDuration: .infinity, maximumDistance: .infinity, pressing: { pressing in
            //         withAnimation(.easeInOut(duration: 0.2)) {
            //             self.isPressed = pressing
            //         }
            //     }, perform: {})
            // }
        }
        .disabled(isDisabled || isLoading)
    }
}

// MARK: - Styled Text Field
struct StyledTextField: View {
    let title: String
    @Binding var text: String
    var placeholder: String = ""
    var icon: String? = nil
    var isSecure: Bool = false
    var isDisabled: Bool = false
    var keyboardType: UIKeyboardType = .default
    var autocapitalization: UITextAutocapitalizationType = .sentences
    @State private var isEditing: Bool = false
    @State private var showSecureText: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(AppTheme.Typography.subheadline)
                .foregroundColor(AppTheme.Colors.secondary)

            HStack(spacing: 12) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: AppTheme.Dimensions.iconSizeSmall + 2))
                        .foregroundColor(isEditing ? AppTheme.Colors.primary : AppTheme.Colors.textTertiary)
                }

                ZStack(alignment: .leading) {
                    if text.isEmpty {
                        Text(placeholder)
                            .font(AppTheme.Typography.body)
                            .foregroundColor(AppTheme.Colors.textTertiary)
                    }

                    if isSecure && !showSecureText {
                        SecureField("", text: $text)
                            .font(AppTheme.Typography.body)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .disabled(isDisabled)
                            .keyboardType(keyboardType)
                            .autocapitalization(autocapitalization)
                            .onTapGesture {
                                isEditing = true
                            }
                    } else {
                        TextField("", text: $text)
                            .font(AppTheme.Typography.body)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .disabled(isDisabled)
                            .keyboardType(keyboardType)
                            .autocapitalization(autocapitalization)
                            .onTapGesture {
                                isEditing = true
                            }
                    }
                }

                if isSecure {
                    Button(action: {
                        showSecureText.toggle()
                    }) {
                        Image(systemName: showSecureText ? "eye.slash.fill" : "eye.fill")
                            .font(.system(size: AppTheme.Dimensions.iconSizeSmall))
                            .foregroundColor(AppTheme.Colors.textTertiary)
                    }
                }
            }
            .padding(.vertical, 14)
            .padding(.horizontal, 16)
            .background(AppTheme.Colors.background)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                isEditing ? AppTheme.Colors.primary.opacity(0.8) : Color.white.opacity(0.2),
                                isEditing ? AppTheme.Colors.secondary.opacity(0.8) : Color.white.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1.5
                    )
            )
            .shadow(color: isEditing ? AppTheme.Colors.primary.opacity(0.3) : Color.clear, radius: 8, x: 0, y: 4)
        }
    }
}

// MARK: - Modern Toggle Style
struct ModernToggleStyle: ToggleStyle {
    func makeBody(configuration: Configuration) -> some View {
        HStack {
            configuration.label

            Spacer()

            ZStack {
                Capsule()
                    .fill(configuration.isOn ? AppTheme.Colors.primary : AppTheme.Colors.card)
                    .frame(width: 50, height: 30)
                    .overlay(
                        Capsule()
                            .stroke(
                                configuration.isOn ?
                                    AppTheme.Colors.primary.opacity(0.5) :
                                    Color.white.opacity(0.2),
                                lineWidth: 1
                            )
                    )
                    .shadow(color: configuration.isOn ? AppTheme.Colors.primary.opacity(0.5) : Color.clear, radius: 5)

                Circle()
                    .fill(configuration.isOn ? Color.white : Color.white.opacity(0.7))
                    .frame(width: 22, height: 22)
                    .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)
                    .offset(x: configuration.isOn ? 10 : -10)
                    .animation(.spring(response: 0.3), value: configuration.isOn)
            }
            .onTapGesture {
                withAnimation {
                    configuration.isOn.toggle()
                }
            }
        }
    }
}

// MARK: - Styled Toggle
struct StyledToggle: View {
    let title: String
    @Binding var isOn: Bool
    var icon: String? = nil

    var body: some View {
        Toggle(isOn: $isOn) {
            HStack(spacing: 12) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: AppTheme.Dimensions.iconSizeSmall + 2))
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }

                Text(title)
                    .font(AppTheme.Typography.body)
                    .foregroundColor(AppTheme.Colors.textPrimary)
            }
        }
        .toggleStyle(ModernToggleStyle())
    }
}

// MARK: - Styled Picker
struct StyledPicker<T: Hashable>: View {
    let title: String
    let options: [T]
    let labels: [String]
    @Binding var selection: T
    var icon: String? = nil

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(AppTheme.Typography.subheadline)
                .foregroundColor(AppTheme.Colors.secondary)

            HStack {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: AppTheme.Dimensions.iconSizeSmall + 2))
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }

                Picker("", selection: $selection) {
                    ForEach(0..<options.count, id: \.self) { index in
                        Text(labels[index])
                            .tag(options[index])
                    }
                }
                .pickerStyle(MenuPickerStyle())
                .foregroundColor(AppTheme.Colors.textPrimary)
            }
            .padding(.vertical, 14)
            .padding(.horizontal, 16)
            .background(AppTheme.Colors.background)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1.5
                    )
            )
        }
    }
}

// MARK: - Styled Section Header
struct StyledSectionHeader: View {
    let title: String
    var subtitle: String? = nil
    var action: (() -> Void)? = nil
    var actionTitle: String = "查看全部"

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(AppTheme.Typography.title3)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(AppTheme.Typography.footnote)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }
            }

            Spacer()

            if let action = action {
                Button(action: action) {
                    Text(actionTitle)
                        .font(AppTheme.Typography.footnote)
                        .foregroundColor(AppTheme.Colors.primary)
                }
            }
        }
        .padding(.horizontal, 4)
    }
}

// MARK: - Gradient Icon
struct GradientIcon: View {
    let systemName: String
    var size: CGFloat = 24
    var gradient: LinearGradient = AppTheme.Colors.primaryGradient

    var body: some View {
        Image(systemName: systemName)
            .font(.system(size: size, weight: .semibold))
            .foregroundStyle(gradient)
    }
}

// MARK: - Avatar View
struct AvatarView: View {
    var image: UIImage? = nil
    var initials: String = ""
    var size: CGFloat = 50

    var body: some View {
        ZStack {
            Circle()
                .fill(AppTheme.Colors.primaryGradient)
                .frame(width: size, height: size)
                .shadow(color: AppTheme.Colors.primary.opacity(0.5), radius: 10, x: 0, y: 5)

            if let image = image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(width: size - 4, height: size - 4)
                    .clipShape(Circle())
            } else {
                Text(initials)
                    .font(.system(size: size * 0.4, weight: .bold, design: .rounded))
                    .foregroundColor(AppTheme.Colors.textPrimary)
            }
        }
    }
}

// MARK: - Progress Bar
struct StyledProgressBar: View {
    var progress: Double // 0.0 to 1.0
    var height: CGFloat = 8
    var showPercentage: Bool = false
    var showProgressText: Bool = false
    var currentValue: Int? = nil
    var maxValue: Int? = nil

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ZStack {
                // Background
                RoundedRectangle(cornerRadius: height / 2)
                    .fill(AppTheme.Colors.card)
                    .frame(height: height)

                // Progress
                ZStack(alignment: .leading) {
                    // 使用GeometryReader获取父视图的宽度，确保进度条正确对齐
                    GeometryReader { geometry in
                        RoundedRectangle(cornerRadius: height / 2)
                            .fill(AppTheme.Colors.primaryGradient)
                            .frame(width: max(height, CGFloat(progress) * geometry.size.width), height: height)
                            .animation(.easeInOut(duration: 0.6), value: progress)
                    }
                    .frame(height: height) // 确保GeometryReader不会改变高度
                }

                // Progress text overlay (if enabled)
                if showProgressText, let current = currentValue, let max = maxValue {
                    GeometryReader { geometry in
                        HStack {
                            Spacer()
                            Text("\(current)/\(max)")
                                .font(AppTheme.Typography.caption2)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.black.opacity(0.4))
                                .cornerRadius(height)
                                .padding(.trailing, 4)
                        }
                        .frame(width: geometry.size.width, height: geometry.size.height)
                    }
                }
            }

            if showPercentage {
                Text("\(Int(progress * 100))%")
                    .font(AppTheme.Typography.caption1)
                    .foregroundColor(AppTheme.Colors.textSecondary)
            }
        }
    }
}