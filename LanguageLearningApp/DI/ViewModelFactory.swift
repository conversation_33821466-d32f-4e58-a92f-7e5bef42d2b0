import Foundation
import SwiftUI

/// ViewModelFactory 协议定义
/// 负责创建所有 ViewModel 实例，确保依赖注入的一致性
/// 采用渐进式重构，先实现基础功能
protocol ViewModelFactory: ObservableObject {
    // MARK: - 基础 ViewModel 创建方法
    // 渐进式添加 ViewModel 支持

    // Phase 1B-1C: 渐进式添加 ViewModel
    func makeWordLearningViewModel() -> WordLearningViewModel
    func makeAchievementViewModel() -> AchievementViewModel
    func makeDailyPracticeViewModel() -> DailyPracticeViewModel
    func makePersonalizedPracticeViewModel(practiceId: UUID?) -> PersonalizedPracticeViewModel
    func makeProgressTrackingViewModel() -> ProgressTrackingViewModel
    func makeEvaluationViewModel() -> EvaluationViewModel
    func makeEvaluationResultViewModel(resultId: UUID) -> EvaluationResultViewModel
    func makeEvaluationResultViewModel(result: EvaluationResult) -> EvaluationResultViewModel
    func makeEvaluationEntryViewModel() -> EvaluationEntryViewModel

    func makeDailyPracticeDashboardViewModel() -> DailyPracticeDashboardViewModel

    // TODO: 继续添加更多 ViewModel
    // func makeListeningViewModel() -> ListeningViewModel
    // func makeSpeakingViewModel() -> SpeakingViewModel
    // func makeGrammarViewModel() -> GrammarViewModel
}

/// 默认 ViewModelFactory 实现
/// 使用依赖注入容器来创建 ViewModel 实例
/// 采用渐进式重构，先实现基础功能
@MainActor
class DefaultViewModelFactory: ViewModelFactory {
    private let container: DependencyContainer

    init(container: DependencyContainer = .shared) {
        self.container = container
    }

    // MARK: - 基础实现
    // Phase 1B: 渐进式添加 ViewModel 创建方法

    func makeWordLearningViewModel() -> WordLearningViewModel {
        return WordLearningViewModel(
            networkService: container.resolve(NetworkServiceProtocol.self),
            userManager: container.resolve((any UserManagerProtocol).self) as! UserManager, // 使用协议
            errorManager: container.resolve((any ErrorManagerProtocol).self) as! ErrorManager, // 使用协议
            ttsManager: TTSManager.shared // 暂时使用具体类型
        )
    }

    func makeAchievementViewModel() -> AchievementViewModel {
        return AchievementViewModel(
            networkService: container.resolve(NetworkServiceProtocol.self),
            userManager: container.resolve((any UserManagerProtocol).self) as! UserManager // 使用协议
        )
    }

    func makeDailyPracticeViewModel() -> DailyPracticeViewModel {
        return DailyPracticeViewModel(
            vocabularyManager: container.resolve((any VocabularyManagerProtocol).self) as! VocabularyManager, // 使用协议
            errorManager: container.resolve((any ErrorManagerProtocol).self) as! ErrorManager, // 使用协议
            practiceManager: container.resolve(PracticeManager.self) // 从容器解析
        )
    }

    func makePersonalizedPracticeViewModel(practiceId: UUID?) -> PersonalizedPracticeViewModel {
        return PersonalizedPracticeViewModel(
            practiceId: practiceId,
            personalizedLearningService: container.resolve(PersonalizedLearningServiceProtocol.self)
        )
    }

    func makeProgressTrackingViewModel() -> ProgressTrackingViewModel {
        return ProgressTrackingViewModel(
            personalizedLearningService: container.resolve(PersonalizedLearningServiceProtocol.self)
        )
    }

    func makeEvaluationViewModel() -> EvaluationViewModel {
        return EvaluationViewModel(
            evaluationManager: container.resolve((any EvaluationManagerProtocol).self) as! EvaluationManager // 使用协议
        )
    }

    func makeEvaluationResultViewModel(resultId: UUID) -> EvaluationResultViewModel {
        return EvaluationResultViewModel(resultId: resultId)
    }

    func makeEvaluationResultViewModel(result: EvaluationResult) -> EvaluationResultViewModel {
        return EvaluationResultViewModel(result: result)
    }

    func makeEvaluationEntryViewModel() -> EvaluationEntryViewModel {
        return EvaluationEntryViewModel(
            evaluationManager: container.resolve((any EvaluationManagerProtocol).self) as! EvaluationManager // 使用协议
        )
    }

    func makeDailyPracticeDashboardViewModel() -> DailyPracticeDashboardViewModel {
        return DailyPracticeDashboardViewModel(
            practiceManager: container.resolve((any PracticeManagerProtocol).self) as! PracticeManager, // 使用协议
            evaluationManager: container.resolve((any EvaluationManagerProtocol).self) as! EvaluationManager, // 使用协议
            userManager: container.resolve((any UserManagerProtocol).self) as! UserManager, // 使用协议
            learningPathService: container.resolve(PersonalizedLearningServiceProtocol.self),
            statsService: container.resolve(DailyPracticeStatsServiceProtocol.self)
        )
    }

    // TODO: 继续添加更多 ViewModel
    // 1. 确保每次添加后都能编译通过
    // 2. 逐步完善依赖注入
    // 3. 替换具体类型为协议类型
}

/// ViewModelFactory 的环境键
struct ViewModelFactoryKey: EnvironmentKey {
    @MainActor
    static var defaultValue: any ViewModelFactory {
        // 创建一个空的 ViewModelFactory，避免在依赖项注册之前解析
        return EmptyViewModelFactory()
    }
}

/// 空的 ViewModelFactory 实现，用于默认值
@MainActor
class EmptyViewModelFactory: ViewModelFactory {
    func makeWordLearningViewModel() -> WordLearningViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }

    func makeAchievementViewModel() -> AchievementViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }

    func makeDailyPracticeViewModel() -> DailyPracticeViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }

    func makePersonalizedPracticeViewModel(practiceId: UUID?) -> PersonalizedPracticeViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }

    func makeProgressTrackingViewModel() -> ProgressTrackingViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }

    func makeEvaluationViewModel() -> EvaluationViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }

    func makeEvaluationResultViewModel(resultId: UUID) -> EvaluationResultViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }

    func makeEvaluationResultViewModel(result: EvaluationResult) -> EvaluationResultViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }

    func makeEvaluationEntryViewModel() -> EvaluationEntryViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }

    func makeDailyPracticeDashboardViewModel() -> DailyPracticeDashboardViewModel {
        fatalError("ViewModelFactory not properly initialized. Use the one provided through environment.")
    }
}

extension EnvironmentValues {
    var viewModelFactory: any ViewModelFactory {
        get { self[ViewModelFactoryKey.self] }
        set { self[ViewModelFactoryKey.self] = newValue }
    }
}
