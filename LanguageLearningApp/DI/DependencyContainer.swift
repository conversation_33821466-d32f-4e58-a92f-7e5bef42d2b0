import Foundation
import SwiftUI

/// 依赖项错误
public enum DependencyError: Error, LocalizedError {
    case notRegistered(String)
    case creationFailed(String)

    public var errorDescription: String? {
        switch self {
        case .notRegistered(let key):
            return "依赖项未注册: \(key)"
        case .creationFailed(let key):
            return "无法创建依赖项: \(key)"
        }
    }
}

/// 依赖项工厂协议
public protocol DependencyFactory {
    /// 创建依赖项
    /// - Returns: 依赖项实例
    func create() -> Any
}

/// 类型擦除的依赖项工厂
private struct AnyDependencyFactory<T>: DependencyFactory {
    private let factory: () -> T

    init(factory: @escaping () -> T) {
        self.factory = factory
    }

    func create() -> Any {
        return factory()
    }
}

/// 依赖项容器
public class DependencyContainer {
    // 单例实例
    public static let shared = DependencyContainer()

    // 依赖项注册表
    private var factories: [String: DependencyFactory] = [:]

    // 单例实例注册表
    private var singletons: [String: Any] = [:]

    // 初始化方法为私有，确保单例模式
    private init() {}

    /// 注册依赖项
    /// - Parameters:
    ///   - type: 依赖项类型
    ///   - factory: 依赖项工厂
    public func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        factories[key] = AnyDependencyFactory(factory: factory)
    }

    /// 注册单例依赖项
    /// - Parameters:
    ///   - type: 依赖项类型
    ///   - factory: 依赖项工厂
    public func registerSingleton<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        let instance = factory()
        singletons[key] = instance
    }

    /// 解析依赖项
    /// - Parameter type: 依赖项类型
    /// - Returns: 依赖项实例
    public func resolve<T>(_ type: T.Type) -> T {
        let key = String(describing: type)

        // 首先检查是否已注册为单例
        if let singleton = singletons[key] as? T {
            return singleton
        }

        // 然后检查是否已注册为工厂
        guard let factory = factories[key] else {
            fatalError("依赖项未注册: \(key)")
        }

        guard let instance = factory.create() as? T else {
            fatalError("无法创建依赖项: \(key)")
        }

        return instance
    }

    /// 尝试解析依赖项（不抛出 fatalError）
    /// - Parameter type: 依赖项类型
    /// - Returns: 依赖项实例或 nil
    public func tryResolve<T>(_ type: T.Type) throws -> T {
        let key = String(describing: type)

        // 首先检查是否已注册为单例
        if let singleton = singletons[key] as? T {
            return singleton
        }

        // 然后检查是否已注册为工厂
        guard let factory = factories[key] else {
            throw DependencyError.notRegistered(key)
        }

        guard let instance = factory.create() as? T else {
            throw DependencyError.creationFailed(key)
        }

        return instance
    }
}

/// 依赖项注入容器的SwiftUI扩展
public extension DependencyContainer {
    /// 创建具有注入依赖项的视图
    /// - Parameters:
    ///   - type: 视图类型
    ///   - dependencies: 依赖项
    /// - Returns: 具有注入依赖项的视图
    func makeView<V: View, D>(_ type: V.Type, withDependency dependency: D) -> some View {
        let viewWithDependency = self.resolve(type) as? (D) -> V
        guard let view = viewWithDependency?(dependency) else {
            fatalError("无法创建视图: \(type)")
        }
        return view
    }
}

/// 依赖项提供器
@propertyWrapper
public struct Inject<T> {
    private let container: DependencyContainer
    public var wrappedValue: T

    public init(container: DependencyContainer = .shared) {
        self.container = container
        self.wrappedValue = container.resolve(T.self)
    }
}

/// 工厂依赖项提供器
@propertyWrapper
public struct InjectFactory<T, D> {
    private let container: DependencyContainer
    public var wrappedValue: (D) -> T

    public init(container: DependencyContainer = .shared) {
        self.container = container
        self.wrappedValue = { dependency in
            let factory = container.resolve(((D) -> T).self)
            return factory(dependency)
        }
    }
}