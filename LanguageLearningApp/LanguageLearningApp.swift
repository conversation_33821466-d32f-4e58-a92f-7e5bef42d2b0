import SwiftUI
import Combine
import UIKit

@main
struct LanguageLearningApp: App {
    // 依赖注入容器
    private let container = DependencyContainer.shared

    // 通过依赖注入获取管理器
    private let userManager: any UserManagerProtocol
    private let achievementManager: any AchievementManagerProtocol
    private let lessonManager: any LessonManagerProtocol
    private let errorManager: any ErrorManagerProtocol
    private let localizationManager: any LocalizationManagerProtocol
    private let viewModelFactory: any ViewModelFactory

    // 环境变更订阅
    private var environmentCancellable: AnyCancellable?

    init() {
        // 注册所有依赖项（包括模块特定的依赖项）
        DependencyRegistry.registerDependencies()

        // 验证关键依赖项是否已注册
        do {
            let _ = try container.tryResolve(UserManager.self)
            print("✅ UserManager 依赖项注册成功")
        } catch {
            print("❌ UserManager 依赖项注册失败: \(error)")
        }

        // 从容器中解析管理器 (暂时使用具体类型，渐进式重构)
        self.userManager = UserManager.shared
        self.achievementManager = AchievementManager.shared
        self.errorManager = ErrorManager.shared
        self.localizationManager = LocalizationManager.shared

        // 延迟解析 LessonManager，避免循环依赖
        do {
            self.lessonManager = try container.tryResolve(LessonManager.self)
            print("✅ LessonManager 解析成功")
        } catch {
            print("❌ LessonManager 解析失败: \(error)")
            // 使用默认实例作为后备
            self.lessonManager = LessonManager()
        }

        // 最后创建 ViewModelFactory，确保所有依赖项都已注册
        self.viewModelFactory = DefaultViewModelFactory(container: container)
        print("✅ ViewModelFactory 创建成功")

        // 配置应用环境
        configureAppEnvironment()

        // 初始化Logger
        let _ = Logger.shared
        print("应用程序初始化")

        // 初始化环境配置
        let _ = AppEnvironment.current
        print("环境配置: \(AppEnvironment.current.rawValue)")

        // 配置应用外观
        #if os(iOS)
        AppearanceManager.configureAppearance()
        print("已配置iOS应用外观")
        #endif

        // 监听环境变更通知
        environmentCancellable = NotificationCenter.default.publisher(for: .environmentChanged)
            .sink { [lessonManager, achievementManager, userManager] _ in
                // 环境变更时重新加载数据
                print("检测到环境变更: \(AppEnvironment.current.rawValue)")
                Task {
                    await lessonManager.loadLessonsFromAPI(completion: nil)
                    await achievementManager.loadAchievementsFromAPI(completion: nil)
                    await userManager.loadUserProfileFromAPI()
                    print("已完成环境变更数据重新加载")
                }
            }
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(userManager as! UserManager)
                .environmentObject(achievementManager as! AchievementManager)
                .environmentObject(lessonManager as! LessonManager)
                .environmentObject(errorManager as! ErrorManager)
                .environmentObject(localizationManager as! LocalizationManager)
                .environmentObject(viewModelFactory as! DefaultViewModelFactory)
                .withErrorHandling()
                .onAppear {
                    print("主界面已加载")
                }
        }
        #if os(macOS)
        .windowStyle(HiddenTitleBarWindowStyle())
        .commands {
            CommandGroup(replacing: .newItem) { }
            CommandGroup(replacing: .help) {
                Button("帮助") {
                    NSWorkspace.shared.open(URL(string: "https://help.languagelearningapp.com")!)
                    print("用户请求帮助页面")
                }
            }
        }
        #endif
    }

    // 配置应用环境
    private func configureAppEnvironment() {
        // 根据情况设置环境
        #if DEBUG
        if ProcessInfo.processInfo.environment["UI_TESTING"] == "1" {
            // UI测试环境配置
            AppEnvironment.current = .development
        } else {
            // 开发环境配置
            AppEnvironment.current = .development
        }
        #else
        // 生产环境配置
        AppEnvironment.current = .production
        #endif

        // 配置日志级别
        configureLogging()
    }

    // 配置日志级别
    private func configureLogging() {
        switch AppEnvironment.current {
        case .development:
            // 开发环境使用详细日志
            Logger.shared.debug("配置开发环境日志级别")
        case .staging:
            // 测试环境使用信息级别日志
            Logger.shared.info("配置测试环境日志级别")
        case .production:
            // 生产环境只使用警告和错误日志
            Logger.shared.warning("配置生产环境日志级别")
        }
    }
}

// MARK: - 预览支持

// 预览支持，确保在 Preview 中使用模拟数据
struct LanguageLearningApp_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .onAppear {
                // 为预览环境配置应用
                AppEnvironment.current = .development

                // 注册预览环境的依赖项
                registerPreviewDependencies()
            }
    }

    // 为预览注册依赖项
    static func registerPreviewDependencies() {
        // 根据需要注册模拟服务
        DependencyRegistry.registerDependencies()
    }
}