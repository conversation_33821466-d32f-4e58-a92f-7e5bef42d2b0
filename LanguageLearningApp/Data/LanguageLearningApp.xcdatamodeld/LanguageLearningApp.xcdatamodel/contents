<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22522" systemVersion="24.5.0" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithSwiftData="YES" userDefinedModelVersionIdentifier="">
    <entity name="UserEntity" representedClassName="UserEntity" syncable="YES" codeGenerationType="class">
        <attribute name="avatar" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="email" attributeType="String"/>
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="isActive" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="lastLoginAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="username" attributeType="String"/>
        <relationship name="settings" optional="YES" maxCount="1" deletionRule="Cascade" destinationEntity="UserSettingsEntity" inverseName="user" inverseEntity="UserSettingsEntity"/>
        <relationship name="stats" optional="YES" maxCount="1" deletionRule="Cascade" destinationEntity="UserStatsEntity" inverseName="user" inverseEntity="UserStatsEntity"/>
    </entity>
    <entity name="UserSettingsEntity" representedClassName="UserSettingsEntity" syncable="YES" codeGenerationType="class">
        <attribute name="autoplayAudio" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="dailyGoal" attributeType="Integer 32" defaultValueString="30" usesScalarValueType="YES"/>
        <attribute name="darkModeEnabled" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="fontSize" optional="YES" attributeType="String"/>
        <attribute name="notificationsEnabled" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="offlineModeEnabled" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="preferredLanguage" optional="YES" attributeType="String"/>
        <attribute name="showTranslation" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="UserEntity" inverseName="settings" inverseEntity="UserEntity"/>
    </entity>
    <entity name="UserStatsEntity" representedClassName="UserStatsEntity" syncable="YES" codeGenerationType="class">
        <attribute name="completedChallenges" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="helpedUsers" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="lastLoginDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="listeningExerciseCount" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="points" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="speakingExerciseCount" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="streakDays" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="vocabularyCount" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="UserEntity" inverseName="stats" inverseEntity="UserEntity"/>
    </entity>
</model> 