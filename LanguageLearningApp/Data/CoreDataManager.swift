import CoreData
import Foundation

/// Core Data 管理器
public class CoreDataManager {
    /// 单例实例
    public static let shared = CoreDataManager()
    
    /// 持久化容器
    private let persistentContainer: NSPersistentContainer
    
    /// 主上下文
    public var viewContext: NSManagedObjectContext {
        persistentContainer.viewContext
    }
    
    /// 私有初始化方法
    private init() {
        persistentContainer = NSPersistentContainer(name: "LanguageLearningApp")
        persistentContainer.loadPersistentStores { description, error in
            if let error = error {
                fatalError("无法加载 Core Data 存储: \(error)")
            }
        }
        
        // 配置自动合并策略
        viewContext.automaticallyMergesChangesFromParent = true
        viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
    }
    
    /// 创建新的后台上下文
    /// - Returns: 后台上下文
    public func newBackgroundContext() -> NSManagedObjectContext {
        let context = persistentContainer.newBackgroundContext()
        context.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        return context
    }
    
    /// 保存上下文
    /// - Parameter context: 要保存的上下文
    public func saveContext(_ context: NSManagedObjectContext) {
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                print("保存上下文失败: \(error)")
            }
        }
    }
    
    /// 在后台上下文中执行操作
    /// - Parameter block: 要执行的操作
    public func performBackgroundTask(_ block: @escaping (NSManagedObjectContext) -> Void) {
        let context = newBackgroundContext()
        context.perform {
            block(context)
            self.saveContext(context)
        }
    }
} 