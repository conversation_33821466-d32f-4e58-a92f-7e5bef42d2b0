import SwiftUI
// Import Color+Hex extension

// MARK: - Theme Mode
enum ThemeMode: String, CaseIterable {
    case light
    case dark
    case system

    var displayName: String {
        let localizationManager = LocalizationManager.shared
        switch self {
        case .light:
            return localizationManager.localizedString(LocalizationKey.light_theme)
        case .dark:
            return localizationManager.localizedString(LocalizationKey.dark_theme)
        case .system:
            return localizationManager.localizedString(LocalizationKey.system_theme)
        }
    }
}

// MARK: - App Theme
struct AppTheme {
    // MARK: - Colors
    struct Colors {
        // Background Colors
        static var background: Color {
            Color(uiColor: UIColor { traitCollection in
                switch traitCollection.userInterfaceStyle {
                case .dark:
                    return UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 1.0)
                case .light:
                    return UIColor(red: 0.96, green: 0.96, blue: 0.96, alpha: 1.0)
                case .unspecified:
                    return UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 1.0)
                @unknown default:
                    return UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 1.0)
                }
            })
        }

        static var backgroundSecondary: Color {
            Color(uiColor: UIColor { traitCollection in
                switch traitCollection.userInterfaceStyle {
                case .dark:
                    return UIColor(red: 0.15, green: 0.16, blue: 0.21, alpha: 1.0)
                case .light:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
                case .unspecified:
                    return UIColor(red: 0.15, green: 0.16, blue: 0.21, alpha: 1.0)
                @unknown default:
                    return UIColor(red: 0.15, green: 0.16, blue: 0.21, alpha: 1.0)
                }
            })
        }

        static var card: Color {
            Color(uiColor: UIColor { traitCollection in
                switch traitCollection.userInterfaceStyle {
                case .dark:
                    return UIColor(red: 0.16, green: 0.18, blue: 0.23, alpha: 1.0)
                case .light:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
                case .unspecified:
                    return UIColor(red: 0.16, green: 0.18, blue: 0.23, alpha: 1.0)
                @unknown default:
                    return UIColor(red: 0.16, green: 0.18, blue: 0.23, alpha: 1.0)
                }
            })
        }

        // Primary Colors
        static let primary = Color(red: 0.48, green: 0.38, blue: 1.0)
        static let secondary = Color(red: 1.0, green: 0.29, blue: 0.51)

        // Accent Colors
        static let accent = Color(red: 0.0, green: 0.82, blue: 1.0)
        static let accent1 = Color(red: 0.0, green: 0.82, blue: 1.0)
        static let accent2 = Color(red: 1.0, green: 0.83, blue: 0.0)
        static let accent3 = Color(red: 0.0, green: 1.0, blue: 0.64)

        // Text Colors
        static var textPrimary: Color {
            Color(uiColor: UIColor { traitCollection in
                switch traitCollection.userInterfaceStyle {
                case .dark:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
                case .light:
                    return UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 1.0)
                case .unspecified:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
                @unknown default:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
                }
            })
        }

        static var textSecondary: Color {
            Color(uiColor: UIColor { traitCollection in
                switch traitCollection.userInterfaceStyle {
                case .dark:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 0.7)
                case .light:
                    return UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 0.7)
                case .unspecified:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 0.7)
                @unknown default:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 0.7)
                }
            })
        }

        static var textTertiary: Color {
            Color(uiColor: UIColor { traitCollection in
                switch traitCollection.userInterfaceStyle {
                case .dark:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 0.5)
                case .light:
                    return UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 0.5)
                case .unspecified:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 0.5)
                @unknown default:
                    return UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 0.5)
                }
            })
        }

        // Status Colors
        static let success = Color(red: 0.0, green: 1.0, blue: 0.64)
        static let warning = Color(red: 1.0, green: 0.83, blue: 0.0)
        static let error = Color(red: 1.0, green: 0.29, blue: 0.51)

        // Additional Category Colors
        static let writing = Color(red: 0.61, green: 0.15, blue: 0.69) // #9C27B0
        static let uncategorized = Color(red: 0.47, green: 0.33, blue: 0.28) // #795548

        // Environment Colors
        static let environmentDevelopment = Color(red: 1.0, green: 0.34, blue: 0.2) // #FF5733
        static let environmentStaging = Color(red: 1.0, green: 0.76, blue: 0.0) // #FFC300
        static let environmentProduction = Color(red: 0.3, green: 0.69, blue: 0.31) // #4CAF50

        // Gradients
        static let primaryGradient = LinearGradient(
            gradient: Gradient(colors: [primary, secondary]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )

        static let accentGradient = LinearGradient(
            gradient: Gradient(colors: [accent1, accent3]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    // MARK: - Dimensions
    struct Dimensions {
        // Spacing
        static let spacingSmall: CGFloat = 8
        static let spacingMedium: CGFloat = 16
        static let spacingLarge: CGFloat = 24
        static let spacingExtraLarge: CGFloat = 32

        static let cornerRadiusSmall: CGFloat = 12
        static let cornerRadiusMedium: CGFloat = 16
        static let cornerRadiusLarge: CGFloat = 24

        static let paddingSmall: CGFloat = 8
        static let paddingMedium: CGFloat = 16
        static let paddingLarge: CGFloat = 24

        static let iconSizeSmall: CGFloat = 16
        static let iconSizeMedium: CGFloat = 24
        static let iconSizeLarge: CGFloat = 32

        static let buttonHeight: CGFloat = 56
    }

    // MARK: - Typography
    struct Typography {
        static let largeTitle = Font.system(size: 34, weight: .bold, design: .rounded)
        static let title1 = Font.system(size: 28, weight: .bold, design: .rounded)
        static let title2 = Font.system(size: 22, weight: .bold, design: .rounded)
        static let title3 = Font.system(size: 20, weight: .semibold, design: .rounded)

        static let headline = Font.system(size: 17, weight: .semibold, design: .rounded)
        static let body = Font.system(size: 17, weight: .regular, design: .rounded)
        static let callout = Font.system(size: 16, weight: .regular, design: .rounded)
        static let subheadline = Font.system(size: 15, weight: .regular, design: .rounded)

        static let footnote = Font.system(size: 13, weight: .regular, design: .rounded)
        static let caption = Font.system(size: 12, weight: .regular, design: .rounded)
        static let caption1 = Font.system(size: 12, weight: .regular, design: .rounded)
        static let caption2 = Font.system(size: 11, weight: .regular, design: .rounded)
    }

    // MARK: - Shadows
    struct Shadows {
        static let small = Shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        static let medium = Shadow(color: .black.opacity(0.15), radius: 8, x: 0, y: 4)
        static let large = Shadow(color: .black.opacity(0.2), radius: 16, x: 0, y: 8)

        static let primaryGlow = Shadow(color: Colors.primary.opacity(0.5), radius: 15, x: 0, y: 5)
        static let secondaryGlow = Shadow(color: Colors.secondary.opacity(0.5), radius: 15, x: 0, y: 5)
    }
}

// MARK: - Theme Manager
class ThemeManager: ObservableObject {
    static let shared = ThemeManager()

    @Published var currentTheme: ThemeMode {
        didSet {
            saveTheme()
            applyTheme()
        }
    }

    private let themeKey = "AppTheme"

    private init() {
        // 从 UserDefaults 加载保存的主题
        if let savedTheme = UserDefaults.standard.string(forKey: themeKey),
           let theme = ThemeMode(rawValue: savedTheme) {
            self.currentTheme = theme
        } else {
            // 如果没有保存的主题，使用系统默认
            self.currentTheme = .system
        }
        applyTheme()
    }

    private func saveTheme() {
        UserDefaults.standard.set(currentTheme.rawValue, forKey: themeKey)
        // 确保立即同步到磁盘
        UserDefaults.standard.synchronize()
    }

    func resetToDefault() {
        currentTheme = .system
    }

    private func applyTheme() {
        // 获取所有窗口并应用主题
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            windowScene.windows.forEach { window in
                switch currentTheme {
                case .light:
                    window.overrideUserInterfaceStyle = .light
                case .dark:
                    window.overrideUserInterfaceStyle = .dark
                case .system:
                    window.overrideUserInterfaceStyle = .unspecified
                }
            }
        }

        // 设置导航栏外观
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()

        switch currentTheme {
        case .light:
            appearance.backgroundColor = UIColor(red: 0.96, green: 0.96, blue: 0.96, alpha: 1.0)
            appearance.titleTextAttributes = [.foregroundColor: UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 1.0)]
            appearance.largeTitleTextAttributes = [.foregroundColor: UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 1.0)]
        case .dark:
            appearance.backgroundColor = UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 1.0)
            appearance.titleTextAttributes = [.foregroundColor: UIColor.white]
            appearance.largeTitleTextAttributes = [.foregroundColor: UIColor.white]
        case .system:
            // 使用系统默认外观
            appearance.configureWithDefaultBackground()
        }

        // 应用导航栏外观
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance

        // 设置 TabBar 外观
        let tabBarAppearance = UITabBarAppearance()
        tabBarAppearance.configureWithOpaqueBackground()

        switch currentTheme {
        case .light:
            tabBarAppearance.backgroundColor = UIColor(red: 0.96, green: 0.96, blue: 0.96, alpha: 1.0)
            // 设置选中和未选中状态的颜色
            tabBarAppearance.stackedLayoutAppearance.selected.iconColor = UIColor(red: 0.48, green: 0.38, blue: 1.0, alpha: 1.0)
            tabBarAppearance.stackedLayoutAppearance.selected.titleTextAttributes = [.foregroundColor: UIColor(red: 0.48, green: 0.38, blue: 1.0, alpha: 1.0)]
            tabBarAppearance.stackedLayoutAppearance.normal.iconColor = UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 0.5)
            tabBarAppearance.stackedLayoutAppearance.normal.titleTextAttributes = [.foregroundColor: UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 0.5)]
        case .dark:
            tabBarAppearance.backgroundColor = UIColor(red: 0.12, green: 0.11, blue: 0.17, alpha: 1.0)
            // 设置选中和未选中状态的颜色
            tabBarAppearance.stackedLayoutAppearance.selected.iconColor = UIColor(red: 0.48, green: 0.38, blue: 1.0, alpha: 1.0)
            tabBarAppearance.stackedLayoutAppearance.selected.titleTextAttributes = [.foregroundColor: UIColor(red: 0.48, green: 0.38, blue: 1.0, alpha: 1.0)]
            tabBarAppearance.stackedLayoutAppearance.normal.iconColor = UIColor.white.withAlphaComponent(0.5)
            tabBarAppearance.stackedLayoutAppearance.normal.titleTextAttributes = [.foregroundColor: UIColor.white.withAlphaComponent(0.5)]
        case .system:
            // 使用系统默认外观
            tabBarAppearance.configureWithDefaultBackground()
        }

        // 应用 TabBar 外观
        UITabBar.appearance().standardAppearance = tabBarAppearance
        if #available(iOS 15.0, *) {
            UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
        }

        // 强制更新视图
        objectWillChange.send()
    }
}

// MARK: - Shadow Struct
struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat

    func toSwiftUIShadow() -> some View {
        return AnyView(
            EmptyView()
                .shadow(color: color, radius: radius, x: x, y: y)
        )
    }
}

// MARK: - View Extensions
extension View {
    func primaryBackground() -> some View {
        self.background(AppTheme.Colors.background)
    }

    func cardStyle() -> some View {
        self
            .background(AppTheme.Colors.card)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(
                color: Color.black.opacity(0.2),
                radius: 10,
                x: 0,
                y: 5
            )
    }

    func glowEffect(color: Color = AppTheme.Colors.primary, radius: CGFloat = 15) -> some View {
        self
            .shadow(color: color.opacity(0.5), radius: radius, x: 0, y: 0)
    }
}

// Color Extension for Hex is now in Color+Hex.swift
