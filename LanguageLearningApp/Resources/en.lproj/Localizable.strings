// General
"ok" = "OK";
"cancel" = "Cancel";
"next" = "Next";
"previous" = "Previous";
"submit" = "Submit";
"play" = "Play";
"pause" = "Pause";
"stop" = "Stop";
"done" = "Done";
"settings" = "Settings";
"language" = "Language";
"confirm" = "Confirm";
"back" = "Back";
"save" = "Save";
"delete" = "Delete";
"edit" = "Edit";
"loading" = "Loading...";
"error" = "Error";
"success" = "Success";

// Navigation
"home" = "Home";
"lessons" = "Lessons";
"practice" = "Practice";
"profile" = "Profile";
"achievements" = "Achievements";

// Profile
"streak" = "Learning streak: %d days";
"vocabulary" = "Vocabulary";
"listening_exercises" = "Listening";
"speaking_exercises" = "Speaking";
"logout" = "Logout";
"logout_confirm" = "Confirm Logout";
"logout_message" = "Are you sure you want to logout?";
"progress" = "Progress: %d/%d";
"profile_edit" = "Edit Profile";
"profile_save" = "Save Profile";
"profile_cancel" = "Cancel";
"profile_delete" = "Delete Profile";
"profile_delete_confirm" = "Are you sure you want to delete your profile?";
"profile_delete_message" = "This action cannot be undone";
"profile_delete_success" = "Profile deleted successfully";
"profile_delete_failed" = "Failed to delete profile";
"statistics" = "Statistics";

// Practice Types
"practice_types" = "Practice Types";
"daily_practice" = "Daily Practice";
"swipe_instruction" = "Swipe right to practice, left to skip";
"confirmed" = "Confirmed";
"skipped" = "Skipped";
"no_practice_cards" = "No practice cards available";
"pull_to_refresh" = "Pull down to refresh or check back later";
"all_done" = "All Done!";
"completed_all_cards" = "You've completed all practice cards for today";
"refresh_cards" = "Refresh Cards";
"swipe_to_practice" = "Swipe to practice";
"word_learning" = "Word Learning";
"listening_practice" = "Listening Practice";
"speaking_practice" = "Speaking Practice";
"recommended_practice" = "Recommended Practice";
"daily_words" = "Daily Words";
"listening_challenge" = "Listening Challenge";
"developer_tools" = "Developer Tools";
"error_test" = "Error Test";

// Listening Exercise
"listening_exercise" = "Listening Exercise";
"show_transcript" = "Show Transcript";
"hide_transcript" = "Hide Transcript";
"exercise_prompt" = "Listen carefully and repeat";
"target_phrase" = "Target Phrase";
"your_answer" = "Your Answer";
"feedback" = "Feedback";
"start_listening" = "Start Listening";
"stop_listening" = "Stop Listening";
"no_exercise" = "No exercise available";
"check_back_later" = "Please check back later for new exercises";
"exercise_progress" = "Progress: %d%%";
"correct_answer" = "Correct!";
"incorrect_answer" = "Incorrect. Try again.";

// Speaking Exercise
"speaking_exercise" = "Speaking Exercise";
"start_recording" = "Start Recording";
"stop_recording" = "Stop Recording";
"recording" = "Recording...";
"pronunciation_accuracy" = "Pronunciation Accuracy";
"speak_now" = "Speak Now";
"processing" = "Processing...";

// Settings
"notifications" = "Notifications";
"enable_notifications" = "Enable Notifications";
"learning_goals" = "Learning Goals";
"daily_goal" = "Daily Goal: %d exercises";
"appearance" = "Appearance";
"dark_mode" = "Dark Mode";
"test_error" = "Test Error";
"settings_saved" = "Settings saved successfully";
"settings_error" = "Failed to save settings";
"settings_reset" = "Reset Settings";
"settings_reset_confirm" = "Are you sure you want to reset all settings?";
"settings_reset_success" = "Settings reset successfully";
"settings_reset_failed" = "Failed to reset settings";

// Error Messages
"error_network" = "Network Error";
"error_server" = "Server Error";
"error_timeout" = "Connection Timeout";
"error_no_internet" = "No Internet Connection";
"error_api" = "API Error";
"error_audio" = "Audio Error";
"error_speech" = "Speech Error";
"error_microphone" = "Microphone Error";
"error_permission" = "Permission Error";
"error_unknown" = "Unknown Error";
"error_data" = "Data Error";
"error_data_save" = "Failed to Save Data";
"error_data_not_found" = "Data Not Found";
"error_invalid_data" = "Invalid Data";
"error_auth" = "Authentication Error";
"error_user_not_found" = "User Not Found";
"error_invalid_credentials" = "Invalid Credentials";
"error_registration" = "Registration Error";
"error_audio_recording" = "Audio Recording Error";
"error_not_implemented" = "Feature Not Implemented";
"error_cancelled" = "Operation Cancelled";
"error_unauthorized" = "Unauthorized";
"error_not_found" = "Resource Not Found";
"error_bad_request" = "Bad Request";
"error_forbidden" = "Access Forbidden";
"error_conflict" = "Resource Conflict";
"error_too_many_requests" = "Too Many Requests";
"error_internal_server" = "Internal Server Error";
"error_bad_gateway" = "Bad Gateway";
"error_service_unavailable" = "Service Unavailable";
"error_gateway_timeout" = "Gateway Timeout";

// Business Logic Errors
"error_invalid_input" = "Invalid Input";
"error_operation_not_allowed" = "Operation Not Allowed";
"error_resource_not_found" = "Resource Not Found";
"error_validation" = "Validation Failed";
"error_business_rule" = "Business Rule Violation";

// Learning Related Errors
"error_lesson_not_available" = "Lesson Not Available";
"error_practice_expired" = "Practice Session Expired";
"error_evaluation_not_found" = "Evaluation Not Found";
"error_progress_sync" = "Progress Sync Failed";
"error_achievement_unlock" = "Achievement Unlock Failed";

// Storage Errors
"error_core_data" = "Core Data Error";
"error_file_system" = "File System Error";
"error_cache" = "Cache Error";

// Permission Errors
"error_feature_not_available" = "Feature Not Available";

// Error Types
"error_type_network" = "Network Error";
"error_type_data" = "Data Error";
"error_type_user" = "User Error";
"error_type_audio" = "Audio Error";
"error_type_system" = "System Error";

// Error Actions
"error_copied" = "Error message copied to clipboard";
"error_copy" = "Copy Error Message";
"error_close" = "Close";

// Error Test
"error_test_title" = "Error Test";
"test_network_error" = "Test Network Error";
"test_data_error" = "Test Data Error";
"test_audio_error" = "Test Audio Error";
"test_auth_error" = "Test Authentication Error";
"test_unknown_error" = "Test Unknown Error";
"clear_error" = "Clear Error";

// Lessons
"lesson_detail" = "Lesson Detail";
"start_lesson" = "Start Lesson";
"complete_lesson" = "Complete Lesson";
"lesson_progress" = "Lesson Progress";
"next_lesson" = "Next Lesson";
"previous_lesson" = "Previous Lesson";
"recommended_lessons" = "Recommended Lessons";
"duration_minutes" = "%d minutes";
"points" = "%d points";

// Word Learning
"new_words" = "New Words";
"review_words" = "Review Words";
"mastered_words" = "Mastered Words";
"add_word" = "Add Word";
"remove_word" = "Remove Word";
"word_meaning" = "Word Meaning";
"word_example" = "Example";
"no_words" = "No words available";
"show_translation" = "Show Translation";
"hide_translation" = "Hide Translation";
"show_example" = "Show Example";
"hide_example" = "Hide Example";
"translate_word" = "Translate the following word:";
"enter_translation" = "Enter translation";
"practice_complete" = "Practice Complete!";
"score" = "Score: %d/%d";
"word_practice" = "Word Practice";
"exit" = "Exit";

// Learning Progress
"daily_goal_title" = "Daily Goal";
"daily_goal_description" = "Complete %d exercises";
"learning_progress" = "Learning Progress";
"weekly_study_time" = "Weekly Study Time";

// Login and Registration
"app_name" = "Language Learning";
"username" = "Username";
"password" = "Password";
"confirm_password" = "Confirm Password";
"email" = "Email";
"login" = "Login";
"register" = "Register";
"enter_username_password" = "Please enter username and password";
"fill_all_fields" = "Please fill in all required fields";
"login_success" = "Login successful";
"login_failed" = "Login failed";
"register_success" = "Registration successful";
"register_failed" = "Registration failed";
"invalid_email" = "Invalid email format";
"username_taken" = "Username already taken";
"email_taken" = "Email already registered";
"remember_me" = "Remember me";
"forgot_password" = "Forgot password?";
"no_account" = "Don't have an account?";
"login_with" = "Or login with";
"enter_email" = "Please enter your email";
"reset_password" = "Reset Password";
"reset_password_instruction" = "Enter your email address and we'll send you a link to reset your password";
"send_reset_link" = "Send Reset Link";
"reset_link_sent" = "Reset link has been sent to your email";
"back_to_login" = "Back to login";
"welcome_message" = "Start your language learning journey";
"create_account_message" = "Create your account to start learning";
"username_placeholder" = "Enter username";
"password_placeholder" = "Enter password";
"email_placeholder" = "Enter email address";
"confirm_password_placeholder" = "Confirm your password";

// Achievements
"achievement_streak" = "Streak";
"achievement_vocabulary" = "Vocabulary";
"achievement_listening" = "Listening";
"achievement_speaking" = "Speaking";
"achievement_lessons" = "Lessons";
"achievement_points" = "Points";
"achievement_challenges" = "Challenges";
"achievement_social" = "Social";
"reward_points" = "Reward: %d points";
"all_achievements" = "All Achievements";
"items" = "items";
"progress" = "%d/%d completed";

// Exercise
"exercise_title" = "Exercise";
"exercise_instruction" = "Instructions";
"enter_answer" = "Enter your answer";
"example" = "Example";
"submit_answer" = "Submit Answer";
"answer_correct" = "Correct!";
"answer_incorrect" = "Incorrect. Try again.";
"explanation" = "Explanation";
"play_audio" = "Play Audio";
"pause_audio" = "Pause Audio";
"stop_audio" = "Stop Audio";
"next_exercise" = "Next Exercise";
"grammar_exercise" = "Grammar Exercise";

// Recommendation Reasons
"recommendation_level_match" = "Matches your learning level";
"recommendation_difficulty_match" = "Appropriate difficulty";
"recommendation_category_match" = "Your frequent category";
"recommendation_progress" = "You've started learning";
"recommendation_duration" = "Fits your study time";

// Word Learning
"category" = "Category";

// Account Settings
"account" = "Account";
"account_settings" = "Account Settings";
"account_settings_subtitle" = "Manage your profile and security";
"personal_info" = "Personal Information";
"username_title" = "Username";
"username_placeholder" = "Enter username";
"email_title" = "Email";
"email_placeholder" = "Email address";
"update_success" = "Update Successful";
"update_success_message" = "Your account information has been updated";
"update_failed" = "Update Failed";
"delete_account" = "Delete Account";
"delete_failed" = "Delete Account Failed";
"change_password" = "Change Password";
"change_password_subtitle" = "Please enter your current password and new password";
"current_password" = "Current Password";
"new_password" = "New Password";
"confirm_new_password" = "Confirm New Password";
"password_changed" = "Password Changed Successfully";
"password_change_failed" = "Failed to Change Password";
"security" = "Security";
"password_requirements" = "Password Requirements";
"min_8_characters" = "At least 8 characters";
"passwords_match" = "Passwords match";
"update_password" = "Update Password";
"passwords_not_match" = "Passwords Don't Match";
"passwords_not_match_message" = "New password and confirmation password don't match";
"password_too_short" = "Password Too Short";
"password_min_chars_message" = "New password must contain at least 8 characters";
"password_changed_message" = "Your password has been successfully updated";

// Notification Settings
"notification_settings" = "Notification Settings";
"notification_types" = "Notification Types";
"daily_reminder" = "Daily Reminder";
"daily_reminder_time" = "Daily Reminder Time";
"weekly_progress_notification" = "Weekly Progress Report";
"new_content_notification" = "New Content Notifications";
"reminder_time" = "Reminder Time";
"notification_permissions" = "Notification Permissions";
"manage_notification_preferences" = "Manage your notification preferences";
"manage_notification_permissions" = "Manage notification permissions in system settings";
"save_settings" = "Save Settings";
"notification_settings_saved" = "Your notification settings have been saved";
"settings_saved" = "Settings Saved";
"language_settings_saved" = "Your language settings have been saved";
"settings_error" = "Settings Error";
"learning_reminder" = "Learning Reminder";
"learning_reminder_message" = "You haven't completed today's learning tasks. Now is a good time to learn!";

// Environment Settings
"environment_settings" = "Environment Settings";
"current_environment" = "Current Environment";
"environment" = "Environment";
"current_selection" = "Current selection: %@";
"apply_settings" = "Apply Settings";
"switch_environment" = "Switch Environment";
"switch_environment_message" = "Switching environments may require restarting the app to take full effect. Are you sure you want to switch?";
"close" = "Close";

// About Page
"about" = "About";
"about_title" = "About";
"app_name_title" = "Language Learning";
"version_format" = "Version %@ (%@)";
"third_party_libraries" = "Third-Party Libraries";
"open_source_components" = "Open Source Components";
"developer" = "Developer";
"development_team" = "Development Team";
"view_project" = "View Project";
"contact_us" = "Contact Us";
"legal_information" = "Legal Information";
"terms_of_service" = "Terms of Service";
"privacy_policy" = "Privacy Policy";
"copyright_notice" = "© %d Language Learning Team. All rights reserved.";

// App Settings
"app_settings" = "App Settings";
"change_app_language" = "Change app language";
"manage_notification_preferences" = "Manage notification preferences";
"about_support" = "About & Support";
"app_information_credits" = "App information and credits";
"help_support" = "Help & Support";
"get_help_app" = "Get help with the app";
"version" = "Version";

// Language Settings
"language_preference_subtitle" = "Choose your preferred interface language";
"language_settings_info_title" = "Language Settings Information";
"language_settings_info_description" = "Select your preferred interface language. After changing the language, the app interface will immediately update to the selected language.";
// TTS Settings
"tts_engine_selection_header" = "TTS Engine Selection";
"tts_select_engine_label" = "Select Engine";
"tts_engine_details_header" = "Engine Details";
"tts_status_available" = "Status: Available";
"tts_status_not_available" = "Status: Not Available (SDK not integrated or configured)";
"tts_sample_voices_for_lang" = "Sample voices for language";
"tts_no_sample_voices_for_lang" = "No sample voices listed for current language.";
"tts_test_playback_header" = "Test Playback";
"tts_play_sample" = "Play Sample Sound";
"tts_stop_sample" = "Stop Sample Sound";
"tts_playback_error_prefix" = "Playback Error:";
"tts_settings_title" = "Text-to-Speech Settings";
"tts_engine_config" = "Engine Configuration";
"tts_test_sample" = "Test Voice Sample";
"tts_voice_selection_label" = "Select Voice";

// Practice Related
"start_learning" = "Start Learning";
"practice_description" = "Practice Description";
"completed_exercises" = "Completed Exercises";
"strongest_skill" = "Strongest Skill";
"weakest_skill" = "Weakest Skill";
"continue_learning" = "Continue Learning";
"today_practice" = "Today's Practice";
"weekly_average" = "Weekly Average";
"skill_analysis" = "Skill Analysis";
"grammar" = "Grammar";
"listening" = "Listening";
"speaking" = "Speaking";
"reading" = "Reading";
"writing" = "Writing";
"mixed" = "Mixed";
"start_evaluation" = "Start Evaluation";
"minutes" = "minutes";
"view_detailed_stats" = "View Detailed Statistics";
"no_skill_data" = "No skill data available";
"start_personalized_learning" = "Start Personalized Learning";
"personalized_learning_description" = "Click the button below to start the personalized learning process. The system will create an assessment that you need to complete before starting your learning journey.";
"starting_learning" = "Starting Personalized Learning...";
"learning_started" = "Personalized Learning Started Successfully!";
"evaluation_prompt" = "You will be redirected to the assessment page. Please complete the assessment to create your personalized learning path";
"practice_again" = "Practice Again";
"start_practice" = "Start Practice";
"practice_completed" = "Practice Completed";
"practice_completed_message" = "You have completed today's practice. Come back tomorrow for new exercises!";

// Assessment
"language_assessment" = "Language Assessment";
"language_level_assessment" = "Language Level Assessment";
"assessment_description" = "Get personalized learning recommendations through professional language assessment.";
"assessment_duration" = "About 15 minutes";
"assessment_questions" = "50 questions";
"start_new_assessment" = "Start New Assessment";
"recent_assessments" = "Recent Assessments";
"view_all" = "View All";
"assessment_benefits" = "Assessment Benefits";
"accurate_assessment" = "Accurate Assessment";
"accurate_assessment_desc" = "Comprehensive assessment of listening, speaking, reading, and writing skills";
"personalized_learning" = "Personalized Learning";
"personalized_learning_desc" = "Customized learning plan based on assessment results";
"progress_tracking" = "Progress Tracking";
"progress_tracking_desc" = "Regular assessments to track your learning progress";
"professional_standards" = "Professional Standards";
"professional_standards_desc" = "Based on CEFR international language standards";

/* Assessment Related */
"assessment_history" = "Assessment History";
"no_assessment_history" = "No Assessment History";
"no_assessment_history_desc" = "Your assessment records will appear here after you complete an assessment";
"loading" = "Loading...";
"error_loading_question" = "Unable to load question content";
"error_loading_assessment" = "Unable to load assessment";
"confirm_exit" = "Are you sure you want to exit?";
"confirm_exit_message" = "Your progress will be saved and you can continue later";
"exit" = "Exit";
"continue_assessment" = "Continue Assessment";
"question" = "Question";
"unsupported_question_type" = "This question type is not supported";
"enter_your_answer" = "Enter your answer";
"previous" = "Previous";
"next" = "Next";
"submit" = "Submit";

// Theme Settings
"theme_settings" = "Theme Settings";
"theme_settings_subtitle" = "Choose your preferred app appearance";
"light_theme" = "Light Theme";
"dark_theme" = "Dark Theme";
"system_theme" = "System Theme";
"theme_changed" = "Theme Changed";
"theme_changed_message" = "The app appearance has been updated";
