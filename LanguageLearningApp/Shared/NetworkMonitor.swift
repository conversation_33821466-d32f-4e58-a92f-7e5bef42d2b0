import Foundation
import Network

/// 网络状态监控器
public class NetworkMonitor {
    /// 共享实例
    public static let shared = NetworkMonitor()
    
    /// 网络状态
    private let monitor: NWPathMonitor
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    /// 是否已连接
    public private(set) var isConnected: Bool = false
    
    private init() {
        monitor = NWPathMonitor()
        startMonitoring()
    }
    
    /// 开始监控
    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            self?.isConnected = path.status == .satisfied
        }
        monitor.start(queue: queue)
    }
    
    /// 停止监控
    public func stopMonitoring() {
        monitor.cancel()
    }
} 