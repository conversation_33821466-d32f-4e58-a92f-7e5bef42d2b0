import SwiftUI

struct AppSettingsView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject private var errorManager: ErrorManager
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var showNotifications = true
    @State private var dailyGoal = 3
    @State private var darkMode = false
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text(localizationManager.localizedString(LocalizationKey.language))) {
                    ForEach(localizationManager.supportedLanguages, id: \.0) { language in
                        Button(action: {
                            withAnimation {
                                localizationManager.currentLanguage = language.0
                            }
                        }) {
                            HStack {
                                Text(language.1)
                                Spacer()
                                if localizationManager.currentLanguage == language.0 {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.blue)
                                        .transition(.scale)
                                }
                            }
                        }
                        .foregroundColor(.primary)
                    }
                }
                
                Section(header: Text(localizationManager.localizedString(LocalizationKey.notifications))) {
                    Toggle(localizationManager.localizedString(LocalizationKey.enableNotifications), isOn: $showNotifications)
                }
                
                Section(header: Text(localizationManager.localizedString(LocalizationKey.learningGoals))) {
                    Stepper(String(format: localizationManager.localizedString(LocalizationKey.dailyGoal), dailyGoal), value: $dailyGoal, in: 1...10)
                }
                
                Section(header: Text(localizationManager.localizedString(LocalizationKey.appearance))) {
                    Toggle(localizationManager.localizedString(LocalizationKey.darkMode), isOn: $darkMode)
                }
                
                Section {
                    Button(localizationManager.localizedString(LocalizationKey.testError)) {
                        errorManager.showError(.networkError(localizationManager.localizedString(LocalizationKey.errorNetwork)))
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle(localizationManager.localizedString(LocalizationKey.settings))
            .navigationBarItems(trailing: Button(localizationManager.localizedString(LocalizationKey.done)) {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

#Preview {
    AppSettingsView()
        .environmentObject(ErrorManager.shared)
}
