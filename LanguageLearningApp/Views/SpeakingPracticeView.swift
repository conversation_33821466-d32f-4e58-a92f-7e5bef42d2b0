import SwiftUI

struct SpeakingPracticeView: View {
    @StateObject private var practiceManager = PracticeManager.shared
    @Environment(\.dismiss) private var dismiss
    @StateObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        SpeakingView()
            .environmentObject(ErrorManager.shared)
    }
}

#Preview {
    NavigationView {
        SpeakingPracticeView()
    }
} 