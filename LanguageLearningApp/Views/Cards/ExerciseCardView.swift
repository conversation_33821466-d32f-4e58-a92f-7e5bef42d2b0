import SwiftUI

/// 练习卡片内容视图
struct ExerciseCardView: View {
    let card: PracticeCardModel
    let exercise: Exercise
    @State private var animateGradient: Bool = false
    @State private var selectedOption: String? = nil
    
    var body: some View {
        VStack(spacing: AppTheme.Dimensions.paddingMedium) {
            // Exercise header card
            ZStack {
                // Background with gradient
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                card.color.opacity(0.15),
                                card.color.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        card.color.opacity(0.3),
                                        card.color.opacity(0.1)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1.5
                            )
                    )
                
                VStack(spacing: 16) {
                    // Exercise type badge and question
                    VStack(spacing: AppTheme.Dimensions.paddingSmall) {
                        // Exercise type badge
                        HStack {
                            Text(exercise.type.rawValue.capitalized)
                                .font(AppTheme.Typography.footnote.bold())
                                .foregroundColor(.white)
                                .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                                .padding(.vertical, 4)
                                .background(
                                    Capsule()
                                        .fill(
                                            LinearGradient(
                                                gradient: Gradient(colors: [card.color, card.color.opacity(0.7)]),
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                )
                            
                            Spacer()
                            
                            // Audio button if available
                            if exercise.audioURL != nil {
                                Button(action: {
                                    // Play audio
                                }) {
                                    HStack(spacing: 4) {
                                        // Audio waveform visualization
                                        HStack(spacing: 2) {
                                            ForEach(0..<3, id: \.self) { index in
                                                RoundedRectangle(cornerRadius: 1)
                                                    .fill(card.color)
                                                    .frame(width: 2, height: CGFloat(4 + index % 3 * 3))
                                                    .opacity(animateGradient ? 0.7 : 0.3)
                                                    .animation(
                                                        Animation.easeInOut(duration: 0.5)
                                                            .repeatForever()
                                                            .delay(Double(index) * 0.15),
                                                        value: animateGradient
                                                    )
                                            }
                                        }
                                        .frame(height: 12)
                                        
                                        Image(systemName: "speaker.wave.2.fill")
                                            .font(.system(size: AppTheme.Dimensions.iconSizeSmall))
                                            .foregroundColor(card.color)
                                    }
                                    .padding(8)
                                    .background(
                                        Circle()
                                            .fill(AppTheme.Colors.background)
                                            .overlay(
                                                Circle()
                                                    .stroke(card.color.opacity(0.3), lineWidth: 1)
                                            )
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        
                        // Question
                        Text(exercise.question)
                            .font(AppTheme.Typography.title3)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .multilineTextAlignment(.leading)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    
                    // Instruction if available
                    if let instruction = exercise.instruction {
                        Text(instruction)
                            .font(AppTheme.Typography.callout)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .multilineTextAlignment(.leading)
                            .padding(.bottom, AppTheme.Dimensions.paddingSmall)
                    }
                    
                    // Image if available
                    if let imageURL = exercise.imageURL {
                        // Image placeholder
                        ZStack {
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                .fill(AppTheme.Colors.background)
                                .frame(height: 120)
                            
                            VStack(spacing: 8) {
                                Image(systemName: "photo")
                                    .font(.system(size: AppTheme.Dimensions.iconSizeLarge))
                                    .foregroundColor(AppTheme.Colors.textTertiary)
                                
                                Text("Image Exercise")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            }
                        }
                    }
                }
                .padding(AppTheme.Dimensions.paddingMedium)
            }
            
            // Exercise content
            VStack(alignment: .leading, spacing: AppTheme.Dimensions.paddingMedium) {
                // Content based on what's available
                Group {
                    if let example = exercise.exampleSentence {
                        // Example sentence
                        VStack(alignment: .leading, spacing: AppTheme.Dimensions.paddingSmall) {
                            HStack {
                                Image(systemName: "text.quote")
                                    .font(.system(size: 12))
                                    .foregroundColor(card.color)
                                
                                Text("Example")
                                    .font(AppTheme.Typography.footnote.bold())
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                                
                                Spacer()
                            }
                            
                            Text(example)
                                .font(AppTheme.Typography.body)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                                .padding(AppTheme.Dimensions.paddingSmall)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(
                                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                        .fill(AppTheme.Colors.background)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                .stroke(card.color.opacity(0.2), lineWidth: 1)
                                        )
                                )
                        }
                    } else if !exercise.options.isEmpty {
                        // Options
                        VStack(alignment: .leading, spacing: AppTheme.Dimensions.paddingSmall) {
                            Text("Options")
                                .font(AppTheme.Typography.headline)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                            
                            // Display options in a grid if there are 4 or more
                            if exercise.options.count >= 4 {
                                LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: AppTheme.Dimensions.paddingSmall) {
                                    ForEach(exercise.options.indices.prefix(4), id: \.self) { index in
                                        Button(action: {
                                            withAnimation(.spring()) {
                                                selectedOption = exercise.options[index]
                                            }
                                        }) {
                                            Text(exercise.options[index])
                                                .font(AppTheme.Typography.subheadline)
                                                .foregroundColor(optionTextColor(exercise.options[index]))
                                                .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                                                .padding(.vertical, 8)
                                                .frame(maxWidth: .infinity, alignment: .center)
                                                .background(
                                                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                        .fill(optionBackgroundColor(exercise.options[index]))
                                                        .overlay(
                                                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                                .stroke(optionBorderColor(exercise.options[index]), lineWidth: isOptionSelected(exercise.options[index]) ? 2 : 1)
                                                        )
                                                )
                                        }
                                        .buttonStyle(PlainButtonStyle())
                                    }
                                }
                                
                                if exercise.options.count > 4 {
                                    Text("+ \(exercise.options.count - 4) more options")
                                        .font(AppTheme.Typography.caption1)
                                        .foregroundColor(AppTheme.Colors.textSecondary)
                                        .padding(.top, 4)
                                }
                            } else {
                                // Vertical list for fewer options
                                ForEach(exercise.options.indices, id: \.self) { index in
                                    Button(action: {
                                        withAnimation(.spring()) {
                                            selectedOption = exercise.options[index]
                                        }
                                    }) {
                                        Text(exercise.options[index])
                                            .font(AppTheme.Typography.subheadline)
                                            .foregroundColor(optionTextColor(exercise.options[index]))
                                            .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                                            .padding(.vertical, 8)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .background(
                                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                    .fill(optionBackgroundColor(exercise.options[index]))
                                                    .overlay(
                                                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                            .stroke(optionBorderColor(exercise.options[index]), lineWidth: isOptionSelected(exercise.options[index]) ? 2 : 1)
                                                    )
                                            )
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                            }
                        }
                    } else if let targetPhrase = exercise.targetPhrase {
                        // Target phrase (for speaking exercises)
                        VStack(alignment: .leading, spacing: AppTheme.Dimensions.paddingSmall) {
                            HStack {
                                Image(systemName: "mic.fill")
                                    .font(.system(size: 14))
                                    .foregroundColor(card.color)
                                
                                Text("Target Phrase")
                                    .font(AppTheme.Typography.headline)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                                
                                Spacer()
                            }
                            
                            Text(targetPhrase)
                                .font(AppTheme.Typography.title3)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                                .padding(AppTheme.Dimensions.paddingSmall)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(
                                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                        .fill(AppTheme.Colors.background)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                .stroke(card.color.opacity(0.2), lineWidth: 1)
                                        )
                                )
                        }
                    }
                }
                
                // Explanation if available
                if let explanation = exercise.explanation {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "lightbulb.fill")
                                .font(.system(size: 12))
                                .foregroundColor(card.color)
                            
                            Text("Explanation")
                                .font(AppTheme.Typography.footnote.bold())
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }
                        
                        Text(explanation)
                            .font(AppTheme.Typography.callout)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .padding(AppTheme.Dimensions.paddingSmall)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                    .fill(card.color.opacity(0.1))
                            )
                    }
                    .padding(.top, 4)
                }
                
                // Exercise type indicator
                HStack {
                    // Exercise type
                    HStack(spacing: 4) {
                        Image(systemName: exerciseTypeIcon(exercise.type))
                            .font(.system(size: 12))
                            .foregroundColor(card.color)
                        
                        Text(exercise.type.rawValue.capitalized)
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    // Check answer button
                    if selectedOption != nil {
                        Button(action: {
                            // Check answer logic would go here
                        }) {
                            Text("Check Answer")
                                .font(AppTheme.Typography.subheadline.bold())
                                .foregroundColor(.white)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    Capsule()
                                        .fill(card.color)
                                )
                                .shadow(color: card.color.opacity(0.3), radius: 3, x: 0, y: 2)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.top, 8)
            }
            .padding(AppTheme.Dimensions.paddingMedium)
            .background(AppTheme.Colors.backgroundSecondary)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
        }
        .onAppear {
            withAnimation(Animation.linear(duration: 2.0).repeatForever(autoreverses: true)) {
                animateGradient.toggle()
            }
        }
    }
    
    // Helper function to get icon for exercise type
    private func exerciseTypeIcon(_ type: Exercise.ExerciseType) -> String {
        switch type {
        case .multipleChoice:
            return "list.bullet"
        case .fillInTheBlank:
            return "text.cursor"
        case .translation:
            return "arrow.left.arrow.right"
        case .listening:
            return "ear"
        case .speaking:
            return "mic"
        case .writing:
            return "pencil"
        }
    }
    
    // Helper functions for option styling
    private func isOptionSelected(_ option: String) -> Bool {
        return selectedOption == option
    }
    
    private func isCorrectOption(_ option: String) -> Bool {
        return option == exercise.correctAnswer
    }
    
    private func optionBackgroundColor(_ option: String) -> Color {
        if selectedOption == nil {
            return AppTheme.Colors.background
        }
        
        if isOptionSelected(option) {
            return isCorrectOption(option) ? Color.green.opacity(0.1) : Color.red.opacity(0.1)
        }
        
        if isCorrectOption(option) && selectedOption != nil {
            return Color.green.opacity(0.1)
        }
        
        return AppTheme.Colors.background
    }
    
    private func optionBorderColor(_ option: String) -> Color {
        if selectedOption == nil {
            return isOptionSelected(option) ? card.color : AppTheme.Colors.textTertiary.opacity(0.3)
        }
        
        if isOptionSelected(option) {
            return isCorrectOption(option) ? Color.green : Color.red
        }
        
        if isCorrectOption(option) && selectedOption != nil {
            return Color.green
        }
        
        return AppTheme.Colors.textTertiary.opacity(0.3)
    }
    
    private func optionTextColor(_ option: String) -> Color {
        if selectedOption == nil {
            return AppTheme.Colors.textPrimary
        }
        
        if isOptionSelected(option) {
            return isCorrectOption(option) ? Color.green : Color.red
        }
        
        if isCorrectOption(option) && selectedOption != nil {
            return Color.green
        }
        
        return AppTheme.Colors.textPrimary
    }
}
