import SwiftUI

/// 语法卡片内容视图
struct GrammarCardView: View {
    let card: PracticeCardModel
    let exercise: GrammarExercise
    @State private var animateGradient: Bool = false
    @State private var selectedOption: String? = nil

    var body: some View {
        VStack(spacing: AppTheme.Dimensions.paddingMedium) {
            // Title and category card
            ZStack {
                // Background with gradient
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                card.color.opacity(0.15),
                                card.color.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        card.color.opacity(0.3),
                                        card.color.opacity(0.1)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1.5
                            )
                    )

                VStack(spacing: 16) {
                    // Title with category badge
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(exercise.title)
                                .font(AppTheme.Typography.title2.bold())
                                .foregroundColor(AppTheme.Colors.textPrimary)

                            Text(exercise.category)
                                .font(AppTheme.Typography.subheadline)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }

                        Spacer()

                        // Difficulty badge
                        VStack(alignment: .trailing, spacing: 2) {
                            Text("LEVEL")
                                .font(AppTheme.Typography.caption2)
                                .foregroundColor(AppTheme.Colors.textTertiary)

                            Text(exercise.difficulty.rawValue.capitalized)
                                .font(AppTheme.Typography.caption1.bold())
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    Capsule()
                                        .fill(difficultyColor(exercise.difficulty))
                                )
                        }
                    }

                    // Instruction
                    Text(exercise.instruction)
                        .font(AppTheme.Typography.callout)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 4)

                    // Animated divider
                    Rectangle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    card.color.opacity(0.7),
                                    card.color.opacity(0.3),
                                    card.color.opacity(0.7)
                                ]),
                                startPoint: animateGradient ? .leading : .trailing,
                                endPoint: animateGradient ? .trailing : .leading
                            )
                        )
                        .frame(height: 2)
                }
                .padding(AppTheme.Dimensions.paddingMedium)
            }

            // Question and options
            VStack(alignment: .leading, spacing: AppTheme.Dimensions.paddingMedium) {
                // Question
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "questionmark.circle.fill")
                            .font(.system(size: 16))
                            .foregroundColor(card.color)

                        Text("Question")
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }

                    Text(exercise.question)
                        .font(AppTheme.Typography.title3)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .padding(AppTheme.Dimensions.paddingMedium)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                .fill(AppTheme.Colors.background)
                                .overlay(
                                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                        .stroke(card.color.opacity(0.2), lineWidth: 1)
                                )
                        )
                }

                Divider()
                    .background(AppTheme.Colors.textTertiary.opacity(0.3))

                // Options
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "list.bullet")
                            .font(.system(size: 14))
                            .foregroundColor(card.color)

                        Text("Options")
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Spacer()

                        // Hint button
                        Button(action: {
                            // Show hint
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "lightbulb.fill")
                                    .font(.system(size: 12))

                                Text("Hint")
                                    .font(AppTheme.Typography.caption1)
                            }
                            .foregroundColor(card.color)
                            .padding(.horizontal, 10)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .stroke(card.color, lineWidth: 1)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }

                    // Options grid
                    LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 10) {
                        ForEach(exercise.options, id: \.self) { option in
                            Button(action: {
                                withAnimation(.spring()) {
                                    selectedOption = option
                                }
                            }) {
                                Text(option)
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(optionTextColor(option))
                                    .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
                                    .padding(.vertical, 12)
                                    .frame(maxWidth: .infinity)
                                    .background(
                                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                            .fill(optionBackgroundColor(option))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                    .stroke(optionBorderColor(option), lineWidth: isOptionSelected(option) ? 2 : 1)
                                            )
                                    )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }

                // Context or explanation
                if let context = exercise.context, !context.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "lightbulb.fill")
                                .font(.system(size: 12))
                                .foregroundColor(card.color)

                            Text("Grammar Note")
                                .font(AppTheme.Typography.footnote.bold())
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }

                        Text(context)
                            .font(AppTheme.Typography.callout)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .padding(AppTheme.Dimensions.paddingSmall)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                    .fill(card.color.opacity(0.1))
                            )
                    }
                    .padding(.top, 8)
                }

                // Example sentence
                if !exercise.exampleSentence.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "text.quote")
                                .font(.system(size: 12))
                                .foregroundColor(card.color)

                            Text("Example")
                                .font(AppTheme.Typography.footnote.bold())
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }

                        Text(exercise.exampleSentence)
                            .font(AppTheme.Typography.callout)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .italic()
                            .padding(AppTheme.Dimensions.paddingSmall)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                    .fill(AppTheme.Colors.background.opacity(0.5))
                            )
                    }
                    .padding(.top, 4)
                }
            }
            .padding(AppTheme.Dimensions.paddingMedium)
            .background(AppTheme.Colors.backgroundSecondary)
            .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
        }
        .padding(.bottom, AppTheme.Dimensions.paddingLarge)
        .onAppear {
            withAnimation(Animation.linear(duration: 2.0).repeatForever(autoreverses: true)) {
                animateGradient.toggle()
            }
        }
    }

    // Helper function to get color based on difficulty
    private func difficultyColor(_ difficulty: CommonDifficulty) -> Color {
        switch difficulty {
        case .easy:
            return .green
        case .medium:
            return .orange
        case .hard:
            return .red
        }
    }

    // Helper functions for option styling
    private func isOptionSelected(_ option: String) -> Bool {
        return selectedOption == option
    }

    private func isCorrectOption(_ option: String) -> Bool {
        return option == exercise.correctAnswer
    }

    private func optionBackgroundColor(_ option: String) -> Color {
        if selectedOption == nil {
            return AppTheme.Colors.background
        }

        if isOptionSelected(option) {
            return isCorrectOption(option) ? Color.green.opacity(0.1) : Color.red.opacity(0.1)
        }

        if isCorrectOption(option) && selectedOption != nil {
            return Color.green.opacity(0.1)
        }

        return AppTheme.Colors.background
    }

    private func optionBorderColor(_ option: String) -> Color {
        if selectedOption == nil {
            return isOptionSelected(option) ? card.color : AppTheme.Colors.textTertiary.opacity(0.3)
        }

        if isOptionSelected(option) {
            return isCorrectOption(option) ? Color.green : Color.red
        }

        if isCorrectOption(option) && selectedOption != nil {
            return Color.green
        }

        return AppTheme.Colors.textTertiary.opacity(0.3)
    }

    private func optionTextColor(_ option: String) -> Color {
        if selectedOption == nil {
            return AppTheme.Colors.textPrimary
        }

        if isOptionSelected(option) {
            return isCorrectOption(option) ? Color.green : Color.red
        }

        if isCorrectOption(option) && selectedOption != nil {
            return Color.green
        }

        return AppTheme.Colors.textPrimary
    }
}
