import SwiftUI

/// Card details view - shown when swiping down on a card
struct CardDetailsView: View {
    let card: PracticeCardModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // Card header
                    HStack {
                        // Icon
                        ZStack {
                            Circle()
                                .fill(card.color.opacity(0.2))
                                .frame(width: 50, height: 50)
                            
                            Image(systemName: card.icon)
                                .font(.system(size: 24))
                                .foregroundColor(card.color)
                        }
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(card.title)
                                .font(AppTheme.Typography.title3.bold())
                                .foregroundColor(AppTheme.Colors.textPrimary)
                            
                            Text(card.type.displayName)
                                .font(AppTheme.Typography.subheadline)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }
                        
                        Spacer()
                        
                        // Difficulty indicator
                        VStack(spacing: 2) {
                            Text("LEVEL")
                                .font(AppTheme.Typography.caption2)
                                .foregroundColor(AppTheme.Colors.textTertiary)
                            
                            HStack(spacing: 4) {
                                ForEach(0..<3) { index in
                                    let difficultyLevel: Int = {
                                        switch card.difficulty {
                                        case .easy: return 1
                                        case .medium: return 2
                                        case .hard: return 3
                                        }
                                    }()
                                    
                                    Circle()
                                        .fill(index < difficultyLevel ? card.color : AppTheme.Colors.background)
                                        .frame(width: 8, height: 8)
                                        .overlay(
                                            Circle()
                                                .stroke(card.color.opacity(0.3), lineWidth: 1)
                                        )
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top)
                    
                    // Card content
                    CardContentView(card: card)
                        .frame(maxWidth: .infinity)
                    
                    // Additional details section
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Additional Details")
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                        
                        // Card type info
                        HStack {
                            Text("Type:")
                                .font(AppTheme.Typography.subheadline)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                            
                            Text(card.type.displayName)
                                .font(AppTheme.Typography.subheadline.bold())
                                .foregroundColor(AppTheme.Colors.textPrimary)
                        }
                        
                        // Card difficulty info
                        HStack {
                            Text("Difficulty:")
                                .font(AppTheme.Typography.subheadline)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                            
                            Text(card.difficulty.rawValue.capitalized)
                                .font(AppTheme.Typography.subheadline.bold())
                                .foregroundColor(AppTheme.Colors.textPrimary)
                        }
                        
                        // Card description
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Description:")
                                .font(AppTheme.Typography.subheadline)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                            
                            Text(card.description)
                                .font(AppTheme.Typography.body)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                                .lineSpacing(4)
                        }
                    }
                    .padding()
                    .background(AppTheme.Colors.card)
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                    .padding(.horizontal)
                    
                    // Action buttons
                    HStack(spacing: 16) {
                        // Practice button
                        Button(action: {
                            // Navigate to practice
                            dismiss()
                        }) {
                            HStack {
                                Image(systemName: "play.fill")
                                    .font(.system(size: 16))
                                Text("Practice Now")
                                    .font(AppTheme.Typography.body.bold())
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 14)
                            .background(card.color)
                            .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                            .shadow(color: card.color.opacity(0.3), radius: 5, x: 0, y: 3)
                        }
                        
                        // Close button
                        Button(action: {
                            dismiss()
                        }) {
                            HStack {
                                Image(systemName: "xmark")
                                    .font(.system(size: 16))
                                Text("Close")
                                    .font(AppTheme.Typography.body)
                            }
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 14)
                            .background(AppTheme.Colors.background)
                            .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                            .overlay(
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                        }
                    }
                    .padding(.horizontal)
                    .padding(.bottom)
                }
            }
            .navigationTitle("Card Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }
                }
            }
        }
    }
}

// MARK: - Preview
struct CardDetailsView_Previews: PreviewProvider {
    static var previews: some View {
        CardDetailsView(card: PracticeCardModel.samples.first!)
    }
}
