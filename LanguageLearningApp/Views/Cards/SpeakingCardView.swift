import AVFoundation
import Speech
import SwiftUI
import Foundation

// Container style modifier to reduce complex expressions
struct ContainerStyleModifier: ViewModifier {
    let cornerRadius: CGFloat

    func body(content: Content) -> some View {
        content
            .padding(AppTheme.Dimensions.paddingMedium)
            .background(AppTheme.Colors.backgroundSecondary)
            .cornerRadius(cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.05),
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
}

/// 口语卡片内容视图
struct SpeakingCardView: View {
    // Make properties public to allow initialization from outside
    let card: PracticeCardModel
    let exercise: SpeakingExercise

    // Add a public initializer
    public init(card: PracticeCardModel, exercise: SpeakingExercise) {
        self.card = card
        self.exercise = exercise
    }
    @State private var animateGradient: Bool = false
    @State private var isRecording: Bool = false
    @State private var transcribedText: String = ""
    @State private var showFeedback: Bool = false
    @State private var feedback: String = ""
    @StateObject private var ttsManager = TTSManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared

    // Speech recognition properties
    private let audioEngine = AVAudioEngine()
    @State private var speechRecognizer: SFSpeechRecognizer?
    @State private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    @State private var recognitionTask: SFSpeechRecognitionTask?

    var body: some View {
        // Break down complex expressions to help compiler
        let mainPadding = AppTheme.Dimensions.paddingMedium
        let cornerRadius = AppTheme.Dimensions.cornerRadiusMedium

        // Create gradient colors separately
        let gradientColors = [
            card.color.opacity(0.15),
            card.color.opacity(0.05)
        ]

        let strokeGradientColors = [
            card.color.opacity(0.3),
            card.color.opacity(0.1)
        ]

        // Create gradients separately
        let fillGradient = LinearGradient(
            gradient: Gradient(colors: gradientColors),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )

        let strokeGradient = LinearGradient(
            gradient: Gradient(colors: strokeGradientColors),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )

        // Break down the complex view hierarchy into smaller components
        // Create title header view
        let titleHeaderView: some View = {
            HStack {
                Text(exercise.title)
                    .font(AppTheme.Typography.title2.bold())
                    .foregroundColor(AppTheme.Colors.textPrimary)

                Spacer()

                // Category badge
                Text(exercise.category)
                    .font(AppTheme.Typography.caption2.bold())
                    .foregroundColor(card.color)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(card.color.opacity(0.15))
                            .overlay(
                                Capsule()
                                    .stroke(card.color.opacity(0.3), lineWidth: 1)
                            )
                    )
            }
        }()

        // Create card background view
        let cardBackgroundView: some View = {
            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(fillGradient)
                .overlay(
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(strokeGradient, lineWidth: 1.5)
                )
        }()

        return VStack(spacing: mainPadding) {
            // Title and category card
            ZStack {
                // Background with gradient
                cardBackgroundView

                VStack(spacing: 16) {
                    // Title with category badge
                    titleHeaderView

                    // Instruction
                    Text(exercise.instruction)
                        .font(AppTheme.Typography.body)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.vertical, 4)

                    // Prompt
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: "text.bubble.fill")
                                .font(.system(size: 14))
                                .foregroundColor(card.color)

                            Text("Prompt")
                                .font(AppTheme.Typography.footnote.bold())
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }

                        Text(exercise.prompt)
                            .font(AppTheme.Typography.callout)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .padding(AppTheme.Dimensions.paddingSmall)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(
                                    cornerRadius: AppTheme.Dimensions.cornerRadiusSmall
                                )
                                .fill(AppTheme.Colors.background.opacity(0.5))
                                .overlay(
                                    RoundedRectangle(
                                        cornerRadius: AppTheme.Dimensions.cornerRadiusSmall
                                    )
                                    .stroke(card.color.opacity(0.2), lineWidth: 1)
                                )
                            )
                    }
                }
                .padding(AppTheme.Dimensions.paddingMedium)
            }

            // Break down target phrase section into smaller components

            // Create practice title view
            let practiceTitleView: some View = {
                HStack(spacing: 6) {
                    Image(systemName: "mic.fill")
                        .font(.system(size: 14))
                        .foregroundColor(card.color)

                    Text("Practice Saying")
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                }
            }()

            // Create play button view
            let playButtonView: some View = {
                // Determine button colors and text
                let buttonColor = ttsManager.isSpeaking ? Color.red : card.color
                let buttonText = ttsManager.isSpeaking
                    ? localizationManager.localizedString(LocalizationKey.stop)
                    : localizationManager.localizedString(LocalizationKey.listen)
                let buttonIcon = ttsManager.isSpeaking ? "stop.fill" : "speaker.wave.1.fill"

                return Button(action: {
                    // Play example audio using TTS
                    if ttsManager.isSpeaking {
                        ttsManager.stopSample()
                    } else {
                        ttsManager.playSample(text: exercise.targetPhrase, languageCode: "en-US") { _ in }
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: buttonIcon)
                            .font(.system(size: AppTheme.Dimensions.iconSizeSmall))

                        Text(buttonText)
                            .font(AppTheme.Typography.caption1)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 6)
                    .background(
                        Capsule()
                            .fill(buttonColor)
                    )
                    .shadow(
                        color: buttonColor.opacity(0.3),
                        radius: 3, x: 0, y: 2
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }()

            // Create target phrase display view
            let targetPhraseView: some View = {
                Text(exercise.targetPhrase)
                    .font(AppTheme.Typography.title3)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .padding(AppTheme.Dimensions.paddingMedium)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                            .fill(AppTheme.Colors.background)
                            .overlay(
                                RoundedRectangle(
                                    cornerRadius: AppTheme.Dimensions.cornerRadiusSmall
                                )
                                .stroke(card.color.opacity(0.2), lineWidth: 1)
                            )
                    )
            }()

            // Target phrase and recording interface
            VStack(alignment: .leading, spacing: AppTheme.Dimensions.paddingMedium) {
                // Target phrase section
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        practiceTitleView

                        Spacer()

                        // Play example button
                        playButtonView
                    }

                    // Target phrase display
                    targetPhraseView
                }

                Divider()
                    .background(AppTheme.Colors.textTertiary.opacity(0.3))

                // Break down recording interface into smaller components

                // Create waveform visualization view
                let waveformView: some View = {
                    HStack(spacing: 3) {
                        ForEach(0..<20, id: \.self) { index in
                            // Create a single bar in the waveform
                            let barHeight = isRecording ? CGFloat(4 + index % 10 * 3) : 4
                            let barOpacity = isRecording ? 0.3 + Double(index % 5) * 0.15 : 0.3

                            RoundedRectangle(cornerRadius: 1.5)
                                .fill(card.color)
                                .frame(width: 3, height: barHeight)
                                .opacity(barOpacity)
                                .animation(
                                    isRecording
                                        ? Animation.easeInOut(duration: 0.5)
                                            .repeatForever()
                                            .delay(Double(index) * 0.05) : .default,
                                    value: isRecording
                                )
                        }
                    }
                    .frame(height: 40)
                    .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
                }()

                // Create record button view
                let recordButtonView: some View = {
                    // Create button colors
                    let mainColor = isRecording ? Color.red : card.color
                    let secondaryColor = isRecording ? Color.red.opacity(0.8) : card.color.opacity(0.8)
                    let buttonGradient = LinearGradient(
                        gradient: Gradient(colors: [mainColor, secondaryColor]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )

                    return Button(action: {
                        withAnimation {
                            if isRecording {
                                stopRecording()
                            } else {
                                startRecording()
                            }
                        }
                    }) {
                        ZStack {
                            // Outer glow
                            Circle()
                                .fill(card.color.opacity(0.2))
                                .frame(width: 80, height: 80)
                                .blur(radius: 4)
                                .opacity(isRecording ? 1 : 0.5)

                            // Main circle
                            Circle()
                                .fill(buttonGradient)
                                .frame(width: 70, height: 70)
                                .shadow(
                                    color: mainColor.opacity(0.4),
                                    radius: 8, x: 0, y: 4)

                            // Mic icon
                            Image(systemName: isRecording ? "stop.fill" : "mic.fill")
                                .font(.system(size: AppTheme.Dimensions.iconSizeLarge))
                                .foregroundColor(.white)
                        }
                        .scaleEffect(isRecording ? 1.1 : 1.0)
                    }
                    .buttonStyle(PlainButtonStyle())
                }()

                // Recording interface
                VStack(spacing: 16) {
                    // Voice waveform visualization
                    waveformView

                    // Record button
                    recordButtonView

                    Text(isRecording ? "Tap to stop recording" : "Tap to start recording")
                        .font(AppTheme.Typography.subheadline)
                        .foregroundColor(isRecording ? Color.red : AppTheme.Colors.textSecondary)
                        .animation(.easeInOut, value: isRecording)

                    // Create transcribed text view
                    let transcribedTextView: some View = {
                        // Create text label
                        let textLabelView = Text("Your speech:")
                            .font(AppTheme.Typography.footnote.bold())
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        // Create text background
                        let cornerRadius = AppTheme.Dimensions.cornerRadiusSmall
                        let textBackgroundView = RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(AppTheme.Colors.background.opacity(0.5))
                            .overlay(
                                RoundedRectangle(cornerRadius: cornerRadius)
                                    .stroke(card.color.opacity(0.2), lineWidth: 1)
                            )

                        // Create text content
                        let textContentView = Text(transcribedText)
                            .font(AppTheme.Typography.body)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .padding(AppTheme.Dimensions.paddingSmall)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(textBackgroundView)

                        return VStack(alignment: .leading, spacing: 8) {
                            textLabelView
                            textContentView
                        }
                        .padding(.top, 8)
                    }()

                    // Create feedback view
                    let feedbackView: some View = {
                        let cornerRadius = AppTheme.Dimensions.cornerRadiusSmall
                        let feedbackBackgroundView = RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(card.color.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: cornerRadius)
                                    .stroke(card.color.opacity(0.2), lineWidth: 1)
                            )

                        return Text(feedback)
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(card.color)
                            .padding(AppTheme.Dimensions.paddingSmall)
                            .background(feedbackBackgroundView)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, AppTheme.Dimensions.paddingSmall)
                    }()

                    // Show transcribed text when available
                    if !transcribedText.isEmpty {
                        transcribedTextView
                    }

                    // Show feedback when available
                    if showFeedback {
                        feedbackView
                    }

                    // Create example sentence view
                    let exampleSentenceView: some View = {
                        // Create example header
                        let exampleHeaderView = HStack {
                            Image(systemName: "text.quote")
                                .font(.system(size: 12))
                                .foregroundColor(card.color)

                            Text("Example")
                                .font(AppTheme.Typography.footnote.bold())
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }

                        // Create example background
                        let cornerRadius = AppTheme.Dimensions.cornerRadiusSmall
                        let exampleBackgroundView = RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(AppTheme.Colors.background.opacity(0.5))

                        // Create example text
                        let exampleTextView = Text(exercise.exampleSentence)
                            .font(AppTheme.Typography.callout)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .italic()
                            .padding(AppTheme.Dimensions.paddingSmall)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(exampleBackgroundView)

                        return VStack(alignment: .leading, spacing: 8) {
                            exampleHeaderView
                            exampleTextView
                        }
                        .padding(.top, 8)
                    }()

                    // Show example sentence when available
                    if !exercise.exampleSentence.isEmpty {
                        exampleSentenceView
                    }
                }
                // Create container styling
                .modifier(ContainerStyleModifier(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium))
            }
            .onAppear {
                withAnimation(Animation.linear(duration: 2.0).repeatForever(autoreverses: true)) {
                    animateGradient.toggle()
                }

                // Initialize speech recognizer on the main thread
                DispatchQueue.main.async {
                    self.speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))

                    // Request authorization
                    SFSpeechRecognizer.requestAuthorization { status in
                        // Handle authorization status
                        DispatchQueue.main.async {
                            if status != .authorized {
                                print("Speech recognition authorization denied")
                            }
                        }
                    }
                }
            }
            .onDisappear {
                // Clean up resources on the main thread
                DispatchQueue.main.async {
                    if self.isRecording {
                        self.stopRecording()
                    }
                    if self.ttsManager.isSpeaking {
                        self.ttsManager.stopSample()
                    }
                }
            }
        }
    }

    // MARK: - Speech Recognition Methods

    private func startRecording() {
        // Reset previous recording session if any
        if recognitionTask != nil {
            recognitionTask?.cancel()
            recognitionTask = nil
        }

        // Reset feedback and transcribed text
        DispatchQueue.main.async {
            self.transcribedText = ""
            self.showFeedback = false
        }

        #if os(iOS)
        // Set up audio session
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.record, mode: .default)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("Failed to set up audio session: \(error)")
            return
        }
        #endif

        // Create and configure speech recognition request
        let newRequest = SFSpeechAudioBufferRecognitionRequest()
        DispatchQueue.main.async {
            self.recognitionRequest = newRequest
        }

        guard let speechRecognizer = speechRecognizer else {
            print("Unable to create speech recognition request")
            return
        }

        newRequest.shouldReportPartialResults = true

        // Start recognition task
        let task = speechRecognizer.recognitionTask(with: newRequest) { result, error in
            var isFinal = false

            if let result = result {
                // Update transcribed text
                let transcribedString = result.bestTranscription.formattedString
                DispatchQueue.main.async { [self] in
                    self.transcribedText = transcribedString
                }
                isFinal = result.isFinal
            }

            if error != nil || isFinal {
                // Stop recording
                self.audioEngine.stop()
                self.audioEngine.inputNode.removeTap(onBus: 0)

                DispatchQueue.main.async { [self] in
                    self.recognitionRequest = nil
                    self.recognitionTask = nil
                    self.isRecording = false

                    if isFinal {
                        self.analyzeSpeech()
                    }
                }
            }
        }

        // Store the task
        DispatchQueue.main.async {
            self.recognitionTask = task
        }

        // Configure audio engine
        let recordingFormat = audioEngine.inputNode.outputFormat(forBus: 0)
        audioEngine.inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) {
            buffer, _ in
            self.recognitionRequest?.append(buffer)
        }

        // Start audio engine
        audioEngine.prepare()
        do {
            try audioEngine.start()
            DispatchQueue.main.async {
                self.isRecording = true
            }
        } catch {
            print("Failed to start audio engine: \(error)")
            return
        }
    }

    private func stopRecording() {
        // Stop audio engine and recognition
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()

        // Use a local variable to avoid mutating self directly
        let localTask = recognitionTask

        // Clear properties on main thread
        DispatchQueue.main.async {
            self.recognitionRequest = nil
            self.recognitionTask = nil
            self.isRecording = false

            // Analyze speech
            self.analyzeSpeech()
        }

        // Cancel the task outside of the main thread
        localTask?.cancel()
    }

    private func analyzeSpeech() {
        guard !transcribedText.isEmpty else { return }

        // Calculate accuracy (simple implementation)
        let accuracy = calculateAccuracy(transcribedText, exercise.targetPhrase)

        DispatchQueue.main.async {
            self.feedback = "Accuracy: \(Int(accuracy * 100))%"
            self.showFeedback = true
        }
    }

    func calculateAccuracy(_ spoken: String, _ target: String) -> Double {
        // Simple accuracy calculation (can be improved)
        let spokenLower = spoken.lowercased()
        let targetLower = target.lowercased()

        // Count matching words
        let spokenWords = spokenLower.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }
        let targetWords = targetLower.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }

        var matchCount = 0
        for word in spokenWords {
            if targetWords.contains(word) {
                matchCount += 1
            }
        }

        // Calculate accuracy
        let totalWords = max(spokenWords.count, targetWords.count)
        return totalWords > 0 ? Double(matchCount) / Double(totalWords) : 0
    }
}
