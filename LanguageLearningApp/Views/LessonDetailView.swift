import SwiftUI
import Foundation

struct LessonDetailView: View {
    let lesson: Lesson
    @StateObject private var lessonManager: LessonManager = LessonManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var isFavorite: Bool = false

    var body: some View {
        StyledContainer {
            VStack(spacing: 24) {
                // Header with category icon
                HStack(spacing: 16) {
                    // Category icon
                    ZStack {
                        Circle()
                            .fill(categoryColor)
                            .frame(width: 60, height: 60)
                            .shadow(color: categoryColor.opacity(0.5), radius: 8, x: 0, y: 4)

                        Image(systemName: categoryIcon)
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(lesson.title)
                            .font(AppTheme.Typography.title2)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(lesson.category.rawValue.capitalized)
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }

                    Spacer()

                    Button(action: {
                        isFavorite.toggle()
                        lessonManager.toggleFavorite(lessonId: lesson.id)
                    }) {
                        Image(systemName: isFavorite ? "star.fill" : "star")
                            .font(.system(size: 24))
                            .foregroundColor(isFavorite ? AppTheme.Colors.accent2 : AppTheme.Colors.textTertiary)
                    }
                }

                // Lesson Info Card
                StyledCard {
                    VStack(spacing: 16) {
                        // Lesson details
                        HStack(spacing: 20) {
                            // Difficulty
                            VStack(spacing: 8) {
                                Text("Difficulty")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                HStack(spacing: 4) {
                                    ForEach(0..<3) { index in
                                        Circle()
                                            .fill(index < difficultyLevel ? difficultyColor : AppTheme.Colors.background)
                                            .frame(width: 8, height: 8)
                                    }
                                }

                                Text(lesson.difficulty.rawValue.capitalized)
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(difficultyColor)
                            }

                            Divider()
                                .background(AppTheme.Colors.textTertiary)
                                .frame(height: 40)

                            // Duration
                            VStack(spacing: 8) {
                                Text("Duration")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                Image(systemName: "clock")
                                    .font(.system(size: AppTheme.Dimensions.iconSizeSmall))
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                Text("\(lesson.duration) min")
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                            }

                            Divider()
                                .background(AppTheme.Colors.textTertiary)
                                .frame(height: 40)

                            // Points
                            VStack(spacing: 8) {
                                Text("Points")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                Image(systemName: "star.fill")
                                    .font(.system(size: AppTheme.Dimensions.iconSizeSmall))
                                    .foregroundColor(AppTheme.Colors.accent2)

                                Text("\(lesson.points)")
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                    }
                    .padding(16)
                }

                // Description Section
                VStack(alignment: .leading, spacing: 12) {
                    StyledSectionHeader(title: "Description")

                    Text(lesson.description)
                        .font(AppTheme.Typography.body)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .fixedSize(horizontal: false, vertical: true)
                        .padding(.horizontal, 4)
                }

                // Tags Section
                if !lesson.tags.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        StyledSectionHeader(title: "Tags")

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(lesson.tags, id: \.self) { tag in
                                    Text(tag)
                                        .font(AppTheme.Typography.caption1)
                                        .foregroundColor(AppTheme.Colors.textSecondary)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(AppTheme.Colors.background)
                                        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                                        )
                                }
                            }
                            .padding(.horizontal, 4)
                        }
                    }
                }

                Spacer(minLength: 30)

                // Start Button
                NavigationLink(destination: LessonStartView(lesson: lesson)) {
                    HStack {
                        Text("Start Learning")
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(.white)

                        Image(systemName: "arrow.right")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                    .shadow(color: AppTheme.Colors.primary.opacity(0.5), radius: 10, x: 0, y: 5)
                }
                .padding(.horizontal, 20)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            isFavorite = lessonManager.isFavorite(lessonId: lesson.id)
        }
    }

    private var categoryColor: Color {
        switch lesson.category {
        case .vocabulary: return AppTheme.Colors.primary
        case .grammar: return AppTheme.Colors.secondary
        case .listening: return AppTheme.Colors.accent1
        case .speaking: return AppTheme.Colors.accent3
        case .reading: return AppTheme.Colors.accent2
        case .writing: return AppTheme.Colors.writing
        case .uncategorized: return AppTheme.Colors.uncategorized
        }
    }

    private var categoryIcon: String {
        switch lesson.category {
        case .vocabulary: return "textformat.abc"
        case .grammar: return "text.book.closed"
        case .listening: return "ear"
        case .speaking: return "mic"
        case .reading: return "book"
        case .writing: return "pencil"
        case .uncategorized: return "questionmark.circle"
        }
    }

    private var difficultyLevel: Int {
        switch lesson.difficulty {
        case .easy: return 1
        case .medium: return 2
        case .hard: return 3
        }
    }

    private var difficultyColor: Color {
        switch lesson.difficulty {
        case .easy: return AppTheme.Colors.success
        case .medium: return AppTheme.Colors.warning
        case .hard: return AppTheme.Colors.error
        }
    }
}

#Preview {
    NavigationView {
        LessonDetailView(lesson: Lesson(
            id: "sample1",
            title: "Sample Lesson",
            description: "This is a sample lesson for preview purposes.",
            category: .vocabulary,
            level: .beginner,
            difficulty: .easy,
            duration: 15,
            points: 10,
            tags: ["sample", "preview", "test"]
        ))
    }
}