import SwiftUI
import Foundation

struct ForgotPasswordView: View {
    @EnvironmentObject private var localizationManager: LocalizationManager
    @Environment(\.presentationMode) var presentationMode
    @State private var email = ""
    @State private var isSending = false
    @State private var showSuccess = false
    // 移除 errorMessage，使用统一的 ErrorManager
    @EnvironmentObject private var errorManager: ErrorManager

    var body: some View {
        StyledContainer {
            VStack(spacing: 24) {
                // Header with icon
                VStack(spacing: 16) {
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        AppTheme.Colors.accent2,
                                        AppTheme.Colors.secondary
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 90, height: 90)
                            .shadow(color: AppTheme.Colors.secondary.opacity(0.5), radius: 15, x: 0, y: 5)

                        Image(systemName: "key.fill")
                            .font(.system(size: AppTheme.Dimensions.iconSizeLarge + 3))
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }

                    Text(localizationManager.localizedString(LocalizationKey.reset_password))
                        .font(AppTheme.Typography.title2)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    Text(localizationManager.localizedString(LocalizationKey.reset_password_instruction))
                        .font(AppTheme.Typography.body)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
                .padding(.top, 40)
                .padding(.bottom, 20)

                // Email input
                StyledTextField(
                    title: localizationManager.localizedString(LocalizationKey.email),
                    text: $email,
                    placeholder: "<EMAIL>",
                    icon: "envelope.fill",
                    keyboardType: .emailAddress,
                    autocapitalization: .none
                )
                .padding(.horizontal, 20)

                // 移除自定义错误显示，使用统一的 ErrorManager

                // Success message
                if showSuccess {
                    HStack(spacing: 12) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: AppTheme.Dimensions.iconSizeSmall))
                            .foregroundColor(AppTheme.Colors.primary)

                        Text(localizationManager.localizedString(LocalizationKey.reset_link_sent))
                            .font(AppTheme.Typography.subheadline.weight(.medium))
                            .foregroundColor(AppTheme.Colors.textPrimary.opacity(0.9))
                            .fixedSize(horizontal: false, vertical: true)

                        Spacer()
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal, 16)
                    .background(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                            .fill(AppTheme.Colors.card)
                            .overlay(
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                    .stroke(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                AppTheme.Colors.primary.opacity(0.5),
                                                AppTheme.Colors.primary.opacity(0.2)
                                            ]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 1.5
                                    )
                            )
                    )
                    .shadow(color: AppTheme.Colors.primary.opacity(0.2), radius: 8, x: 0, y: 4)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 8)
                    .transition(.scale.combined(with: .opacity))
                }

                Spacer()

                // Send button
                StyledButton(
                    title: localizationManager.localizedString(LocalizationKey.send_reset_link),
                    action: sendResetLink,
                    icon: "paperplane.fill",
                    isLoading: isSending,
                    isDisabled: email.isEmpty || isSending
                )
                .padding(.horizontal, 20)
                .padding(.bottom, 16)

                // Back button
                Button(action: { presentationMode.wrappedValue.dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.left")
                            .font(.system(size: 14))

                        Text(localizationManager.localizedString(LocalizationKey.back_to_login))
                            .font(AppTheme.Typography.subheadline)
                    }
                    .foregroundColor(AppTheme.Colors.primary)
                }
                .padding(.bottom, 30)
            }
        }
        .withErrorHandling() // 添加统一错误处理
    }

    private func sendResetLink() {
        guard !email.isEmpty else {
            errorManager.showError(.customError(localizationManager.localizedString(LocalizationKey.enter_email)))
            return
        }

        isSending = true

        // 模拟网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            isSending = false
            withAnimation {
                showSuccess = true
            }

            // 2秒后自动关闭
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                presentationMode.wrappedValue.dismiss()
            }
        }
    }
}

#Preview {
    ForgotPasswordView()
        .environmentObject(LocalizationManager.shared)
}
