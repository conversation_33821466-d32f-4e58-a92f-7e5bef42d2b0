import SwiftUI
import Foundation

struct StarWarsCrawlView: View {
    @State private var offset: CGFloat = 0
    let text: String

    var body: some View {
        GeometryReader { geometry in
            Text(text)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(.yellow)
                .multilineTextAlignment(.center)
                .frame(width: geometry.size.width * 0.8)
                .rotationEffect(.degrees(-30))
                .offset(y: geometry.size.height + offset)
                .onAppear {
                    withAnimation(.linear(duration: 20).repeatForever(autoreverses: false)) {
                        offset = -geometry.size.height * 2
                    }
                }
        }
    }
}

struct LoginView: View {
    @EnvironmentObject private var userManager: UserManager
    @EnvironmentObject private var errorManager: ErrorManager
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var username = ""
    @State private var password = ""
    @State private var isShowingRegister = false
    // 移除 errorMessage，使用统一的 ErrorManager
    @State private var isLoggingIn = false
    @State private var rememberMe = false
    @State private var isAnimating = false
    @State private var showForgotPassword = false
    @State private var showMockAccounts = false
    @State private var globeOffset: CGSize = .zero

    var body: some View {
        ZStack {
            // Star Wars crawl background
            StarWarsCrawlView(text: """
                Language Learning App

                In a world where communication knows no bounds,
                a new era of language learning has begun.

                Our mission is to break down language barriers
                and connect people across the globe.

                Join us on this journey of discovery,
                where every word learned is a step towards
                understanding and unity.

                May the language be with you!
                """)
            .opacity(0.15)

            StyledContainer {
                VStack(spacing: 24) {
                    // Logo and Title
                    VStack(spacing: 16) {
                        LogoView()

                        // Text with mask effect
                        VStack(spacing: 16) {
                            Text(localizationManager.localizedString(LocalizationKey.app_name))
                                .font(AppTheme.Typography.title1)
                                .foregroundColor(AppTheme.Colors.textPrimary)
                                .overlay(
                                    Text(localizationManager.localizedString(LocalizationKey.app_name))
                                        .font(AppTheme.Typography.title1)
                                        .foregroundColor(
                                            Color(
                                                hue: 0.6 + Double(globeOffset.width) * 0.0005,
                                                saturation: 0.8 + abs(globeOffset.width) * 0.001,
                                                brightness: 0.9
                                            )
                                        )
                                        .mask(
                                            Circle()
                                                .fill(
                                                    LinearGradient(
                                                        gradient: Gradient(colors: [.clear, .white, .clear]),
                                                        startPoint: .topLeading,
                                                        endPoint: .bottomTrailing
                                                    )
                                                )
                                                .frame(width: 100, height: 100)
                                                .offset(globeOffset)
                                                .blur(radius: 15)
                                        )
                                )

                            Text(localizationManager.localizedString(LocalizationKey.welcome_message))
                                .font(AppTheme.Typography.subheadline)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                                .multilineTextAlignment(.center)
                                .overlay(
                                    Text(localizationManager.localizedString(LocalizationKey.welcome_message))
                                        .font(AppTheme.Typography.subheadline)
                                        .foregroundColor(
                                            Color(
                                                hue: 0.6 + Double(globeOffset.width) * 0.0005,
                                                saturation: 0.6 + abs(globeOffset.width) * 0.001,
                                                brightness: 0.7
                                            )
                                        )
                                        .multilineTextAlignment(.center)
                                        .mask(
                                            Circle()
                                                .fill(
                                                    LinearGradient(
                                                        gradient: Gradient(colors: [.clear, .white, .clear]),
                                                        startPoint: .topLeading,
                                                        endPoint: .bottomTrailing
                                                    )
                                                )
                                                .frame(width: 100, height: 100)
                                                .offset(globeOffset)
                                                .blur(radius: 15)
                                        )
                                )
                        }
                    }
                    .padding(.top, 30)
                    .padding(.bottom, 20)
                    .opacity(isAnimating ? 1 : 0)
                    .offset(y: isAnimating ? 0 : 20)

                    // Login Form
                    StyledCard {
                        VStack(spacing: 20) {
                            // Username field
                            VStack(alignment: .leading, spacing: 10) {
                                Text(localizationManager.localizedString(LocalizationKey.username))
                                    .font(AppTheme.Typography.subheadline)
                                    .foregroundColor(AppTheme.Colors.secondary)

                                HStack(spacing: 12) {
                                    Image(systemName: "person.fill")
                                        .font(.system(size: 18))
                                        .foregroundColor(AppTheme.Colors.textSecondary)

                                    TextField(localizationManager.localizedString(LocalizationKey.username_placeholder), text: $username)
                                        .font(AppTheme.Typography.body)
                                        .foregroundColor(AppTheme.Colors.textPrimary)
                                        .autocapitalization(.none)
                                }
                                .padding(.vertical, 14)
                                .padding(.horizontal, 16)
                                .background(AppTheme.Colors.backgroundSecondary)
                                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                                .overlay(
                                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                        .stroke(
                                            LinearGradient(
                                                gradient: Gradient(colors: [
                                                    Color.white.opacity(0.2),
                                                    Color.white.opacity(0.05)
                                                ]),
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            ),
                                            lineWidth: 1.5
                                        )
                                )
                            }

                            // Password field
                            VStack(alignment: .leading, spacing: 10) {
                                Text(localizationManager.localizedString(LocalizationKey.password))
                                    .font(AppTheme.Typography.subheadline)
                                    .foregroundColor(AppTheme.Colors.secondary)

                                HStack(spacing: 12) {
                                    Image(systemName: "lock.fill")
                                        .font(.system(size: 18))
                                        .foregroundColor(AppTheme.Colors.textSecondary)

                                    SecureField(localizationManager.localizedString(LocalizationKey.password_placeholder), text: $password)
                                        .font(AppTheme.Typography.body)
                                        .foregroundColor(AppTheme.Colors.textPrimary)
                                }
                                .padding(.vertical, 14)
                                .padding(.horizontal, 16)
                                .background(AppTheme.Colors.backgroundSecondary)
                                .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                                .overlay(
                                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                        .stroke(
                                            LinearGradient(
                                                gradient: Gradient(colors: [
                                                    Color.white.opacity(0.2),
                                                    Color.white.opacity(0.05)
                                                ]),
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            ),
                                            lineWidth: 1.5
                                        )
                                )
                            }

                            // Remember me and forgot password
                            HStack {
                                StyledToggle(
                                    title: localizationManager.localizedString(LocalizationKey.remember_me),
                                    isOn: $rememberMe,
                                    icon: "checkmark.square"
                                )

                                Spacer()

                                Button(action: { showForgotPassword = true }) {
                                    Text(localizationManager.localizedString(LocalizationKey.forgot_password))
                                        .font(AppTheme.Typography.footnote)
                                        .foregroundColor(AppTheme.Colors.primary)
                                        .underline()
                                }
                            }
                            .padding(.top, 8)
                        }
                        .padding(20)
                    }
                    .opacity(isAnimating ? 1 : 0)
                    .offset(y: isAnimating ? 0 : 20)
                    .gyroscopeEffect(intensity: 15)

                    // 移除自定义错误显示，使用统一的 ErrorManager

                    // Login Button
                    StyledButton(
                        title: localizationManager.localizedString(LocalizationKey.login),
                        action: login,
                        icon: "arrow.right",
                        isLoading: isLoggingIn
                    )
                    .opacity(isAnimating ? 1 : 0)
                    .offset(y: isAnimating ? 0 : 20)

                    // Demo Account Button - 在开发环境下始终显示
                    #if DEBUG
                    VStack(spacing: 12) {
                        Button(action: {
                            quickLogin(username: "demo", password: "demo123")
                        }) {
                            HStack {
                                Image(systemName: "person.crop.circle.badge.checkmark")
                                Text("Demo Login")
                            }
                            .font(AppTheme.Typography.footnote)
                            .foregroundColor(AppTheme.Colors.primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(AppTheme.Colors.primary.opacity(0.1))
                            .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                        }

                        // Mock Accounts Button
                        Button(action: {
                            showMockAccounts = true
                        }) {
                            HStack {
                                Image(systemName: "person.3.fill")
                                Text("Mock Accounts")
                            }
                            .font(AppTheme.Typography.footnote)
                            .foregroundColor(AppTheme.Colors.secondary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(AppTheme.Colors.secondary.opacity(0.1))
                            .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                        }
                    }
                    .opacity(isAnimating ? 1 : 0)
                    .offset(y: isAnimating ? 0 : 20)
                    #endif

                    // Register Link
                    HStack(spacing: 4) {
                        Text(localizationManager.localizedString(LocalizationKey.no_account))
                            .font(AppTheme.Typography.footnote)
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        Button(action: { isShowingRegister = true }) {
                            Text(localizationManager.localizedString(LocalizationKey.register))
                                .font(AppTheme.Typography.footnote)
                                .foregroundColor(AppTheme.Colors.primary)
                                .underline()
                        }
                    }
                    .opacity(isAnimating ? 1 : 0)
                    .offset(y: isAnimating ? 0 : 20)
                }
                .padding(.horizontal)
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $isShowingRegister) {
            RegisterView()
        }
        .sheet(isPresented: $showForgotPassword) {
            ForgotPasswordView()
        }
        .sheet(isPresented: $showMockAccounts) {
            MockAccountView { account in
                username = account.username
                password = account.password
                login()
            }
        }
        .onAppear {
            withAnimation(.easeOut(duration: 0.8)) {
                isAnimating = true
            }
        }
        .withErrorHandling() // 添加统一错误处理
    }

    private func login() {
        guard !username.isEmpty && !password.isEmpty else {
            errorManager.showError(.customError(localizationManager.localizedString(LocalizationKey.fill_all_fields)))
            return
        }

        isLoggingIn = true

        Task {
            do {
                try await userManager.login(username: username, password: password)
                if rememberMe {
                    // 使用 StorageManager 保存登录状态
                    StorageManager.shared.saveLoginState(true)
                }
            } catch {
                await MainActor.run {
                    let nsError = error as NSError
                    switch nsError.code {
                    case 401:
                        errorManager.showError(.authenticationFailed("用户名或密码错误"))
                    case 404:
                        errorManager.showError(.userNotFound)
                    case 500:
                        errorManager.showError(.serverError("服务器错误，请稍后重试"))
                    default:
                        errorManager.showError(.customError(error.localizedDescription))
                    }
                }
            }

            await MainActor.run {
                isLoggingIn = false
            }
        }
    }

    private func quickLogin(username: String, password: String) {
        self.username = username
        self.password = password
        login()
    }
}

struct MockAccountView: View {
    @Environment(\.dismiss) private var dismiss
    let onSelect: (MockAccountManager.MockAccount) -> Void

    var body: some View {
        NavigationView {
            List(MockAccountManager.shared.mockAccounts, id: \.username) { account in
                MockAccountRow(account: account) {
                    onSelect(account)
                    dismiss()
                }
            }
            .navigationTitle("Demo Accounts")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppTheme.Colors.primary)
                }
            }
        }
    }
}

struct MockAccountRow: View {
    let account: MockAccountManager.MockAccount
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Role Icon
                roleIconView

                // Account Info
                accountInfoView

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(AppTheme.Colors.textSecondary)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var roleIconView: some View {
        ZStack {
            Circle()
                .fill(roleColor.opacity(0.2))
                .frame(width: 50, height: 50)

            Image(systemName: roleIcon)
                .font(.system(size: 20))
                .foregroundColor(roleColor)
        }
    }

    private var accountInfoView: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(account.username)
                .font(AppTheme.Typography.headline)
                .foregroundColor(AppTheme.Colors.textPrimary)

            Text(account.role.capitalized)
                .font(AppTheme.Typography.caption)
                .foregroundColor(AppTheme.Colors.textSecondary)

            Text("Password: \(account.password)")
                .font(AppTheme.Typography.caption2)
                .foregroundColor(AppTheme.Colors.textTertiary)
        }
    }

    private var roleIcon: String {
        switch account.role.lowercased() {
        case "admin":
            return "crown.fill"
        case "teacher":
            return "graduationcap.fill"
        case "student":
            return "person.fill"
        case "beginner":
            return "star.fill"
        case "advanced":
            return "star.circle.fill"
        default:
            return "person.circle.fill"
        }
    }

    private var roleColor: Color {
        switch account.role.lowercased() {
        case "admin":
            return .red
        case "teacher":
            return .blue
        case "student":
            return AppTheme.Colors.primary
        case "beginner":
            return .green
        case "advanced":
            return .purple
        default:
            return AppTheme.Colors.secondary
        }
    }
}
