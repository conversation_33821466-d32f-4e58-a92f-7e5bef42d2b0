import SwiftUI
import AVFoundation
import Combine

// Add missing imports to fix compiler errors
// These errors are unrelated to our navigation bar fix

struct ListeningPracticeView: View {
    @StateObject private var practiceManager = PracticeManager.shared
    @Environment(\.dismiss) private var dismiss
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        StyledContainer {
            ModernListeningView()
                .environmentObject(LessonManager.shared)
                .environmentObject(ErrorManager.shared)
        }
        .navigationTitle(localizationManager.localizedString(LocalizationKey.listeningExercise))
        .navigationBarTitleDisplayMode(.inline)
    }
}

// Enhanced version of ListeningView with modern UI
struct ModernListeningView: View {
    @StateObject private var viewModel = ListeningViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @EnvironmentObject private var lessonManager: LessonManager
    @EnvironmentObject private var errorManager: ErrorManager

    var body: some View {
        VStack(spacing: 24) {
            if let exercise = viewModel.currentExercise {
                // Enhanced Exercise Header
                EnhancedExerciseHeader(exercise: exercise, progress: viewModel.progress)

                // Enhanced Audio Controls
                EnhancedAudioControls(viewModel: viewModel)

                // Enhanced Transcript View
                EnhancedTranscriptView(exercise: exercise, showTranscript: viewModel.showTranscript)

                // Enhanced Question View
                EnhancedQuestionView(exercise: exercise, viewModel: viewModel)

                // Enhanced Navigation Controls
                EnhancedNavigationControls(viewModel: viewModel, exercise: exercise)
            } else {
                VStack {
                    Image(systemName: "ear.trianglebadge.exclamationmark")
                        .font(.system(size: 50))
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .padding(.bottom, 16)

                    Text(localizationManager.localizedString(LocalizationKey.no_exercise))
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .cardStyle()
                .padding()
            }
        }
        .padding(.horizontal)
    }
}

// MARK: - Enhanced Subviews
struct EnhancedExerciseHeader: View {
    let exercise: ListeningExercise
    let progress: Double
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        VStack(spacing: 16) {
            // Title with icon
            HStack(spacing: 16) {
                ZStack {
                    Circle()
                        .fill(AppTheme.Colors.accent1)
                        .frame(width: 50, height: 50)
                        .shadow(color: AppTheme.Colors.accent1.opacity(0.5), radius: 8, x: 0, y: 4)

                    Image(systemName: "ear")
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.white)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(exercise.title)
                        .font(AppTheme.Typography.title3)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    Text(localizationManager.localizedString(LocalizationKey.listening_practice))
                        .font(AppTheme.Typography.subheadline)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }

                Spacer()
            }

            // Progress bar
            VStack(spacing: 8) {
                HStack {
                    Text(String(format: localizationManager.localizedString(LocalizationKey.exercise_progress), Int(progress * 100)))
                        .font(AppTheme.Typography.footnote)
                        .foregroundColor(AppTheme.Colors.textSecondary)

                    Spacer()

                    Text("\(Int(progress * 100))%")
                        .font(AppTheme.Typography.footnote)
                        .foregroundColor(AppTheme.Colors.accent1)
                }

                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // Background
                        RoundedRectangle(cornerRadius: 10)
                            .fill(AppTheme.Colors.card)
                            .frame(height: 8)

                        // Progress
                        RoundedRectangle(cornerRadius: 10)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [AppTheme.Colors.accent1, AppTheme.Colors.accent3]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: max(0, min(geometry.size.width * progress, geometry.size.width)), height: 8)
                    }
                }
                .frame(height: 8)
            }
        }
        .padding()
        .cardStyle()
    }
}

struct EnhancedAudioControls: View {
    @ObservedObject var viewModel: ListeningViewModel
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        HStack(spacing: 20) {
            // Play/Pause Button
            Button(action: {
                viewModel.playAudio()
            }) {
                ZStack {
                    Circle()
                        .fill(AppTheme.Colors.primary)
                        .frame(width: 60, height: 60)
                        .shadow(color: AppTheme.Colors.primary.opacity(0.5), radius: 10, x: 0, y: 5)

                    Image(systemName: viewModel.isPlaying ? "pause.fill" : "play.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                }
            }
            .buttonStyle(PlainButtonStyle())

            // Transcript Toggle Button
            Button(action: viewModel.toggleTranscript) {
                HStack(spacing: 10) {
                    Image(systemName: viewModel.showTranscript ? "text.badge.minus" : "text.badge.plus")
                        .font(.system(size: 16))

                    Text(viewModel.showTranscript ?
                        localizationManager.localizedString(LocalizationKey.hideTranscript) :
                        localizationManager.localizedString(LocalizationKey.showTranscript))
                        .font(AppTheme.Typography.subheadline)
                }
                .padding(.vertical, 12)
                .padding(.horizontal, 16)
                .foregroundColor(AppTheme.Colors.textPrimary)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .fill(AppTheme.Colors.card)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.white.opacity(0.2),
                                            Color.white.opacity(0.05)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding()
        .cardStyle()
    }
}

struct EnhancedTranscriptView: View {
    let exercise: ListeningExercise
    let showTranscript: Bool
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        if showTranscript {
            VStack(alignment: .leading, spacing: 12) {
                Text(localizationManager.localizedString(LocalizationKey.showTranscript))
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .padding(.horizontal, 16)
                    .padding(.top, 16)

                ScrollView {
                    Text(exercise.transcript)
                        .font(AppTheme.Typography.body)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .lineSpacing(4)
                        .padding(16)
                }
                .frame(maxHeight: 200)
            }
            .cardStyle()
        }
    }
}

struct EnhancedQuestionView: View {
    let exercise: ListeningExercise
    @ObservedObject var viewModel: ListeningViewModel
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        if viewModel.currentQuestionIndex < exercise.questions.count {
            let question = exercise.questions[viewModel.currentQuestionIndex]

            VStack(alignment: .leading, spacing: 16) {
                // Question
                Text(question.question)
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .padding(.horizontal, 16)
                    .padding(.top, 16)

                // Options
                VStack(spacing: 12) {
                    ForEach(0..<question.options.count, id: \.self) { index in
                        Button(action: {
                            viewModel.selectAnswer(index)
                        }) {
                            HStack {
                                Text(question.options[index])
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(
                                        viewModel.isAnswerSubmitted && viewModel.selectedAnswer == index ?
                                            (viewModel.isAnswerCorrect ? AppTheme.Colors.success : AppTheme.Colors.error) :
                                            AppTheme.Colors.textPrimary
                                    )
                                    .multilineTextAlignment(.leading)

                                Spacer()

                                if viewModel.isAnswerSubmitted && viewModel.selectedAnswer == index {
                                    Image(systemName: viewModel.isAnswerCorrect ? "checkmark.circle.fill" : "xmark.circle.fill")
                                        .foregroundColor(viewModel.isAnswerCorrect ? AppTheme.Colors.success : AppTheme.Colors.error)
                                }
                            }
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                    .fill(
                                        viewModel.selectedAnswer == index ?
                                            (viewModel.isAnswerSubmitted ?
                                                (viewModel.isAnswerCorrect ? AppTheme.Colors.success.opacity(0.1) : AppTheme.Colors.error.opacity(0.1)) :
                                                AppTheme.Colors.primary.opacity(0.1)) :
                                            AppTheme.Colors.card
                                    )
                                    .overlay(
                                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                            .stroke(
                                                viewModel.selectedAnswer == index ?
                                                    (viewModel.isAnswerSubmitted ?
                                                        (viewModel.isAnswerCorrect ? AppTheme.Colors.success.opacity(0.3) : AppTheme.Colors.error.opacity(0.3)) :
                                                        AppTheme.Colors.primary.opacity(0.3)) :
                                                    Color.white.opacity(0.1),
                                                lineWidth: 1
                                            )
                                    )
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                        .disabled(viewModel.isAnswerSubmitted)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 16)

                // Feedback message
                if viewModel.isAnswerSubmitted {
                    HStack {
                        Image(systemName: viewModel.isAnswerCorrect ? "hand.thumbsup.fill" : "hand.thumbsdown.fill")
                            .foregroundColor(viewModel.isAnswerCorrect ? AppTheme.Colors.success : AppTheme.Colors.error)

                        Text(viewModel.isAnswerCorrect ?
                            localizationManager.localizedString(LocalizationKey.correct_answer) :
                            localizationManager.localizedString(LocalizationKey.incorrect_answer))
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(viewModel.isAnswerCorrect ? AppTheme.Colors.success : AppTheme.Colors.error)
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 16)
                }
            }
            .cardStyle()
        }
    }
}

struct EnhancedNavigationControls: View {
    @ObservedObject var viewModel: ListeningViewModel
    let exercise: ListeningExercise
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        HStack {
            // Previous Button
            Button(action: {
                viewModel.stopAudio()
                viewModel.currentQuestionIndex = max(0, viewModel.currentQuestionIndex - 1)
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 14, weight: .semibold))

                    Text(localizationManager.localizedString(LocalizationKey.previous))
                        .font(AppTheme.Typography.subheadline)
                }
                .padding(.vertical, 12)
                .padding(.horizontal, 16)
                .foregroundColor(viewModel.currentQuestionIndex == 0 ? AppTheme.Colors.textTertiary : AppTheme.Colors.textPrimary)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .fill(AppTheme.Colors.card)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(viewModel.currentQuestionIndex == 0)
            .opacity(viewModel.currentQuestionIndex == 0 ? 0.5 : 1.0)

            Spacer()

            // Next Button
            Button(action: {
                viewModel.stopAudio()
                viewModel.currentQuestionIndex = min(exercise.questions.count - 1, viewModel.currentQuestionIndex + 1)
            }) {
                HStack(spacing: 8) {
                    Text(localizationManager.localizedString(LocalizationKey.next))
                        .font(AppTheme.Typography.subheadline)

                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .semibold))
                }
                .padding(.vertical, 12)
                .padding(.horizontal, 16)
                .foregroundColor(viewModel.currentQuestionIndex == exercise.questions.count - 1 ? AppTheme.Colors.textTertiary : AppTheme.Colors.textPrimary)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .fill(AppTheme.Colors.card)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(viewModel.currentQuestionIndex == exercise.questions.count - 1)
            .opacity(viewModel.currentQuestionIndex == exercise.questions.count - 1 ? 0.5 : 1.0)
        }
        .padding(.horizontal, 16)
    }
}

// Using cardStyle() from UIComponents.swift

// MARK: - Preview
#Preview {
    NavigationView {
        ListeningPracticeView()
    }
}