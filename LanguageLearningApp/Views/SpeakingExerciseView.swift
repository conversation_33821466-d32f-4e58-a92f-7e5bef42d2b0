import SwiftUI
import Speech

struct SpeakingExerciseView: View {
    let exercise: SpeakingExercise
    @StateObject private var speechRecognizer = SpeechRecognizer()
    @State private var isRecording = false
    @State private var transcribedText = ""
    @State private var showResult = false
    @State private var score = 0
    @EnvironmentObject private var errorManager: ErrorManager
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        VStack(spacing: 20) {
            // 练习说明
            Text(exercise.instruction)
                .font(.headline)
                .multilineTextAlignment(.center)
                .padding()

            // 示例句子
            VStack(alignment: .leading, spacing: 10) {
                Text(localizationManager.localizedString(LocalizationKey.example_sentence))
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Text(exercise.exampleSentence)
                    .font(.body)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
            }
            .padding()

            // 录音按钮
            Button(action: {
                if isRecording {
                    stopRecording()
                } else {
                    startRecording()
                }
            }) {
                Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(isRecording ? .red : .blue)
            }
            .padding()

            // 录音状态
            if isRecording {
                Text(localizationManager.localizedString(LocalizationKey.recording))
                    .foregroundColor(.red)
            }

            // 转录文本
            if !transcribedText.isEmpty {
                VStack(alignment: .leading, spacing: 10) {
                    Text(localizationManager.localizedString(LocalizationKey.yourAnswer))
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text(transcribedText)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)
                }
                .padding()
            }

            // 提交按钮
            if !transcribedText.isEmpty && !isRecording {
                Button(action: {
                    evaluateAnswer()
                }) {
                    Text(localizationManager.localizedString(LocalizationKey.submit))
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.blue)
                        .cornerRadius(10)
                }
                .padding()
            }

            // 结果显示
            if showResult {
                VStack(spacing: 10) {
                    Text(String(format: localizationManager.localizedString(LocalizationKey.score), score))
                        .font(.title)
                        .bold()

                    Text(score >= 60 ? 
                        localizationManager.localizedString(LocalizationKey.great_job) :
                        localizationManager.localizedString(LocalizationKey.keep_practicing))
                        .font(.headline)
                        .foregroundColor(score >= 60 ? .green : .orange)
                }
                .padding()
            }
        }
        .padding()
        .navigationTitle(localizationManager.localizedString(LocalizationKey.speakingExercise))
    }

    private func startRecording() {
        speechRecognizer.requestAuthorization { authorized in
            if authorized {
                isRecording = true
                transcribedText = ""
                speechRecognizer.startRecording { text in
                    transcribedText = text
                }
            }
        }
    }

    private func stopRecording() {
        isRecording = false
        speechRecognizer.stopRecording()
    }

    private func evaluateAnswer() {
        // 简单的评分逻辑：根据文本相似度评分
        let similarity = calculateSimilarity(transcribedText, exercise.exampleSentence)
        score = Int(similarity * 100)
        showResult = true
    }

    private func calculateSimilarity(_ text1: String, _ text2: String) -> Double {
        // 简单的相似度计算：基于共同单词的比例
        let words1 = Set(text1.lowercased().components(separatedBy: .whitespacesAndNewlines))
        let words2 = Set(text2.lowercased().components(separatedBy: .whitespacesAndNewlines))

        let intersection = words1.intersection(words2).count
        let union = words1.union(words2).count

        return union > 0 ? Double(intersection) / Double(union) : 0
    }
}

class SpeechRecognizer: ObservableObject {
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()
    private let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))

    // 使用 ErrorManager.shared 而不是环境对象，因为这是一个 ObservableObject 而不是 View
    private let errorManager = ErrorManager.shared

    func requestAuthorization(completion: @escaping (Bool) -> Void) {
        SFSpeechRecognizer.requestAuthorization { status in
            DispatchQueue.main.async {
                completion(status == .authorized)
            }
        }
    }

    func startRecording(completion: @escaping (String) -> Void) {
        // 取消之前的任务
        if recognitionTask != nil {
            recognitionTask?.cancel()
            recognitionTask = nil
        }

        // 设置音频会话
        let audioSession = AVAudioSession.sharedInstance()
        do {
            // 使用更安全的设置
            try audioSession.setCategory(.record, mode: .default)
            try audioSession.setActive(true)
        } catch {
            self.errorManager.showError(.audioRecordingFailed("音频会话设置失败: \(error.localizedDescription)"))
            return
        }

        // 创建识别请求
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()

        guard let recognitionRequest = recognitionRequest,
              let speechRecognizer = speechRecognizer else {
            self.errorManager.showError(.speechRecognitionFailed("无法创建语音识别请求或识别器"))
            return
        }

        // 设置部分结果报告
        recognitionRequest.shouldReportPartialResults = true

        // 检查输入节点和格式
        let inputNode = audioEngine.inputNode
        
        // Get the input format from the input node
        let recordingFormat = inputNode.inputFormat(forBus: 0)

        // 确保音频格式有效
        guard recordingFormat.sampleRate > 0,
              recordingFormat.channelCount > 0 else {
            self.errorManager.showError(.audioRecordingFailed("无效的音频格式"))
            return
        }

        // 安装音频输入 tap
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }

        // 准备音频引擎
        audioEngine.prepare()

        // 启动音频引擎
        do {
            try audioEngine.start()
        } catch {
            self.errorManager.showError(.audioRecordingFailed("音频引擎启动失败: \(error.localizedDescription)"))
            self.stopRecording()
            return
        }

        // 创建识别任务
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { result, error in
            var isFinal = false

            if let result = result {
                completion(result.bestTranscription.formattedString)
                isFinal = result.isFinal
            }

            if let error = error {
                self.errorManager.showError(.speechRecognitionFailed("语音识别失败: \(error.localizedDescription)"))
            }

            if error != nil || isFinal {
                self.audioEngine.stop()
                self.audioEngine.inputNode.removeTap(onBus: 0)
                self.recognitionRequest = nil
                self.recognitionTask = nil
            }
        }
    }

    func stopRecording() {
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        recognitionRequest = nil
        recognitionTask = nil
    }
}

#Preview {
    SpeakingExerciseView(
        exercise: SpeakingExercise(
            id: UUID(),
            title: "自我介绍",
            prompt: "请用中文进行自我介绍",
            targetPhrase: "你好，我是小明",
            difficulty: .easy,
            category: "Self Introduction",
            instruction: "请跟读以下句子",
            exampleSentence: "你好，我是小明"
        )
    )
    .environmentObject(ErrorManager.shared)
}