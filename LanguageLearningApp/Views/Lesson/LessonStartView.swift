import SwiftUI
import Foundation

struct LessonStartView: View {
    let lesson: Lesson
    @Environment(\.dismiss) private var dismiss
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var showingExercise = false

    var body: some View {
        StyledContainer {
            VStack(spacing: 24) {
                // Header with category icon
                HStack(spacing: 16) {
                    // Category icon
                    ZStack {
                        Circle()
                            .fill(categoryColor)
                            .frame(width: 60, height: 60)
                            .shadow(color: categoryColor.opacity(0.5), radius: 8, x: 0, y: 4)

                        Image(systemName: categoryIcon)
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(lesson.title)
                            .font(AppTheme.Typography.title2)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(lesson.category.rawValue.capitalized)
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }

                    Spacer()
                }

                // Lesson Info Card
                StyledCard {
                    VStack(spacing: 16) {
                        // Lesson details
                        HStack(spacing: 20) {
                            // Difficulty
                            VStack(spacing: 8) {
                                Text("Difficulty")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                HStack(spacing: 4) {
                                    ForEach(0..<3) { index in
                                        Circle()
                                            .fill(index < difficultyLevel ? difficultyColor : AppTheme.Colors.background)
                                            .frame(width: 8, height: 8)
                                    }
                                }

                                Text(lesson.difficulty.rawValue.capitalized)
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(difficultyColor)
                            }

                            Divider()
                                .background(AppTheme.Colors.textTertiary)
                                .frame(height: 40)

                            // Duration
                            VStack(spacing: 8) {
                                Text("Duration")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                Image(systemName: "clock")
                                    .font(.system(size: 16))
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                Text("\(lesson.duration) min")
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                            }

                            Divider()
                                .background(AppTheme.Colors.textTertiary)
                                .frame(height: 40)

                            // Points
                            VStack(spacing: 8) {
                                Text("Points")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                Image(systemName: "star.fill")
                                    .font(.system(size: 16))
                                    .foregroundColor(AppTheme.Colors.accent2)

                                Text("\(lesson.points)")
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                            }

                            Divider()
                                .background(AppTheme.Colors.textTertiary)
                                .frame(height: 40)

                            // Level
                            VStack(spacing: 8) {
                                Text("Level")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                Image(systemName: "graduationcap.fill")
                                    .font(.system(size: 16))
                                    .foregroundColor(AppTheme.Colors.textSecondary)

                                Text(lesson.level.rawValue.capitalized)
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                    }
                    .padding(16)
                }

                // Description Section
                VStack(alignment: .leading, spacing: 12) {
                    StyledSectionHeader(title: "Description")

                    Text(lesson.description)
                        .font(AppTheme.Typography.body)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .fixedSize(horizontal: false, vertical: true)
                        .padding(.horizontal, 4)
                }

                // Learning Goals Section
                VStack(alignment: .leading, spacing: 12) {
                    StyledSectionHeader(title: "Learning Goals")

                    StyledCard {
                        VStack(alignment: .leading, spacing: 12) {
                            LearningGoalRow(icon: "checkmark.circle.fill", text: "Master basic vocabulary")

                            Divider()
                                .background(AppTheme.Colors.textTertiary.opacity(0.3))

                            LearningGoalRow(icon: "checkmark.circle.fill", text: "Understand grammar rules")

                            Divider()
                                .background(AppTheme.Colors.textTertiary.opacity(0.3))

                            LearningGoalRow(icon: "checkmark.circle.fill", text: "Improve listening and speaking skills")
                        }
                        .padding(16)
                    }
                }

                // Tags Section
                if !lesson.tags.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        StyledSectionHeader(title: "Tags")

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(lesson.tags, id: \.self) { tag in
                                    Text(tag)
                                        .font(AppTheme.Typography.caption1)
                                        .foregroundColor(AppTheme.Colors.textSecondary)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(AppTheme.Colors.background)
                                        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                                        )
                                }
                            }
                            .padding(.horizontal, 4)
                        }
                    }
                }

                Spacer(minLength: 30)

                // Start Button
                NavigationLink(destination: LessonExerciseView(lesson: lesson)) {
                    HStack {
                        Text("Start Learning")
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(.white)

                        Image(systemName: "arrow.right")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
                    .shadow(color: AppTheme.Colors.primary.opacity(0.5), radius: 10, x: 0, y: 5)
                }
                .padding(.horizontal, 20)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { dismiss() }) {
                    HStack {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Back")
                            .font(AppTheme.Typography.subheadline)
                    }
                    .foregroundColor(AppTheme.Colors.textPrimary)
                }
            }
        }
    }

    private var categoryColor: Color {
        switch lesson.category {
        case .vocabulary: return AppTheme.Colors.primary
        case .grammar: return AppTheme.Colors.secondary
        case .listening: return AppTheme.Colors.accent1
        case .speaking: return AppTheme.Colors.accent3
        case .reading: return AppTheme.Colors.accent2
        case .writing: return AppTheme.Colors.writing
        case .uncategorized: return AppTheme.Colors.uncategorized
        }
    }

    private var categoryIcon: String {
        switch lesson.category {
        case .vocabulary: return "textformat.abc"
        case .grammar: return "text.book.closed"
        case .listening: return "ear"
        case .speaking: return "mic"
        case .reading: return "book"
        case .writing: return "pencil"
        case .uncategorized: return "questionmark.circle"
        }
    }

    private var difficultyLevel: Int {
        switch lesson.difficulty {
        case .easy: return 1
        case .medium: return 2
        case .hard: return 3
        }
    }

    private var difficultyColor: Color {
        switch lesson.difficulty {
        case .easy: return AppTheme.Colors.success
        case .medium: return AppTheme.Colors.warning
        case .hard: return AppTheme.Colors.error
        }
    }
}

// LearningGoalRow 已移至单独的文件

struct FlowLayout: Layout {
    var spacing: CGFloat = 8

    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        return layout(sizes: sizes, proposal: proposal).size
    }

    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        let offsets = layout(sizes: sizes, proposal: proposal).offsets

        for (offset, subview) in zip(offsets, subviews) {
            subview.place(at: CGPoint(x: bounds.minX + offset.x, y: bounds.minY + offset.y), proposal: .unspecified)
        }
    }

    private func layout(sizes: [CGSize], proposal: ProposedViewSize) -> (offsets: [CGPoint], size: CGSize) {
        let width = proposal.width ?? .infinity
        var offsets: [CGPoint] = []
        var currentX: CGFloat = 0
        var currentY: CGFloat = 0
        var maxY: CGFloat = 0
        var rowHeight: CGFloat = 0

        for size in sizes {
            if currentX + size.width > width {
                currentX = 0
                currentY += rowHeight + spacing
                rowHeight = 0
            }

            offsets.append(CGPoint(x: currentX, y: currentY))
            currentX += size.width + spacing
            rowHeight = max(rowHeight, size.height)
            maxY = max(maxY, currentY + rowHeight)
        }

        return (offsets, CGSize(width: width, height: maxY))
    }
}

#Preview {
    NavigationView {
        LessonStartView(lesson: Lesson(
            id: "preview",
            title: "示例课程",
            description: "这是一个示例课程的详细描述。",
            category: .vocabulary,
            level: .beginner,
            difficulty: .easy,
            duration: 30,
            points: 100,
            tags: ["基础", "词汇", "入门"]
        ))
    }
}