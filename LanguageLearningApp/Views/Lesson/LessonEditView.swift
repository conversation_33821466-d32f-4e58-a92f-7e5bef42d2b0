import SwiftUI

struct LessonEditView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var lessonManager = LessonManager.shared

    @State private var title: String = ""
    @State private var description: String = ""
    @State private var selectedCategory: Category = .vocabulary
    @State private var level: Level = .beginner
    @State private var difficulty: Difficulty = .easy
    @State private var duration: Int = 15
    @State private var points: Int = 10
    @State private var tags: String = ""

    let lesson: Lesson?

    var isNewLesson: Bool {
        lesson == nil
    }

    var body: some View {
        NavigationView {
            Form {
                Section {
                    TextField(LocalizationManager.shared.localizedString("lesson_title"), text: $title)
                    TextEditor(text: $description)
                        .frame(height: 100)
                } header: {
                    Text("Basic Info")
                }

                // Section {
                //     Picker("Category", selection: $selectedCategory) {
                //         ForEach(Category.allCases, id: \.self) { (category: Category) in
                //             Text(category.rawValue.capitalized).tag(category)
                //         }
                //     }
                //     .pickerStyle(MenuPickerStyle())

                //     Picker("Level", selection: $level) {
                //         ForEach(Level.allCases, id: \.self) { (level: Level) in
                //             Text(level.rawValue.capitalized).tag(level)
                //         }
                //     }
                //     .pickerStyle(MenuPickerStyle())

                //     Picker("Difficulty", selection: $difficulty) {
                //         ForEach(Difficulty.allCases, id: \.self) { (difficulty: Difficulty) in
                //             Text(difficulty.rawValue.capitalized).tag(difficulty)
                //         }
                //     }
                //     .pickerStyle(MenuPickerStyle())

                //     Stepper("Duration: \(duration) min", value: $duration, in: 5...60, step: 5)
                //     Stepper("Points: \(points)", value: $points, in: 5...50, step: 5)
                // } header: {
                //     Text("Lesson Details")
                // }

                Section {
                    TextField("Enter tags (comma separated)", text: $tags)
                } header: {
                    Text("Tags")
                }
            }
            .navigationTitle(isNewLesson ? "New Lesson" : "Edit Lesson")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveLesson()
                    }
                }
            }
        }
    }

    private func saveLesson() {
        // TODO: Implement save functionality
        dismiss()
    }
}

#Preview {
    LessonEditView(lesson: Lesson.sample)
}