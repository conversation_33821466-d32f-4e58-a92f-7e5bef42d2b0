import SwiftUI

/// An enhanced card view that can be swiped in all four directions with visual feedback
struct SwipeCardView<Content: View>: View {
    // Content to display in the card
    private let content: Content

    // Callback for when the card is swiped right (confirm)
    private let onSwipeRight: () -> Void

    // Callback for when the card is swiped left (skip)
    private let onSwipeLeft: () -> Void

    // Horizontal swipe callbacks only

    // State for tracking the card offset during drag
    @State private var offset = CGSize.zero

    // State for tracking if the card has been swiped away
    @State private var isRemoved = false

    // State for tracking swipe direction for visual effects
    @State private var swipeDirection: SwipeDirection = .none

    // State for animation
    @State private var animateIndicator = false

    // Threshold for considering a swipe action complete
    private let swipeThreshold: CGFloat = 120

    // Threshold for vertical swipes
    private let verticalSwipeThreshold: CGFloat = 100

    // Enum to track swipe direction for visual effects
    private enum SwipeDirection {
        case left, right, none
    }

    init(
        @ViewBuilder content: () -> Content,
        onSwipeRight: @escaping () -> Void,
        onSwipeLeft: @escaping () -> Void
    ) {
        self.content = content()
        self.onSwipeRight = onSwipeRight
        self.onSwipeLeft = onSwipeLeft
    }

    var body: some View {
        ZStack {
            // Background indicators that show the action result
            // Left swipe indicator (skip)
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                .fill(Color.red.opacity(0.15))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                        .stroke(Color.red.opacity(0.3), lineWidth: 2)
                )
                .opacity(swipeDirection == .left ? 1 : 0)

            // Right swipe indicator (confirm)
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                .fill(Color.green.opacity(0.15))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                        .stroke(Color.green.opacity(0.3), lineWidth: 2)
                )
                .opacity(swipeDirection == .right ? 1 : 0)



            // Main card content
            content
                .frame(maxWidth: .infinity)
                .frame(minHeight: 400) // 确保内容有足够的显示空间
                .background(
                    ZStack {
                        // Card background
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                            .fill(AppTheme.Colors.card)

                        // Dynamic color overlay based on swipe direction
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        getSwipeColor().opacity(0.05),
                                        getSwipeColor().opacity(0.02)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )

                        // Card border
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.3),
                                        Color.white.opacity(0.1),
                                        getSwipeColor().opacity(getSwipeOpacity() * 0.3)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1.5
                            )
                    }
                )
                .shadow(color: getSwipeColor().opacity(0.1), radius: 10, x: 0, y: 5)
                .overlay(
                    ZStack {
                        // Enhanced skip indicator (left swipe)
                        VStack {
                            HStack {
                                ZStack {
                                    // Pulsating background
                                    Circle()
                                        .fill(Color.red.opacity(0.2))
                                        .frame(width: 110, height: 110)
                                        .scaleEffect(animateIndicator && swipeDirection == .left ? 1.2 : 1.0)
                                        .opacity(animateIndicator && swipeDirection == .left ? 0.6 : 0.0)

                                    // Icon
                                    Image(systemName: "xmark.circle.fill")
                                        .foregroundColor(.red)
                                        .font(.system(size: 80))
                                        .opacity(getSkipOpacity())
                                        .shadow(color: Color.red.opacity(0.5), radius: 5, x: 0, y: 2)
                                }
                                .offset(x: -20)

                                Spacer()
                            }

                            Spacer()
                        }
                        .padding(.top, 40)
                        .padding(.leading, 24)

                        // Enhanced confirm indicator (right swipe)
                        VStack {
                            HStack {
                                Spacer()

                                ZStack {
                                    // Pulsating background
                                    Circle()
                                        .fill(Color.green.opacity(0.2))
                                        .frame(width: 110, height: 110)
                                        .scaleEffect(animateIndicator && swipeDirection == .right ? 1.2 : 1.0)
                                        .opacity(animateIndicator && swipeDirection == .right ? 0.6 : 0.0)

                                    // Icon
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                        .font(.system(size: 80))
                                        .opacity(getConfirmOpacity())
                                        .shadow(color: Color.green.opacity(0.5), radius: 5, x: 0, y: 2)
                                }
                                .offset(x: 20)
                            }

                            Spacer()
                        }
                        .padding(.top, 40)
                        .padding(.trailing, 24)

                        // Swipe direction labels
                        VStack {
                            Spacer()

                            HStack {
                                // Skip label
                                HStack(spacing: 4) {
                                    Image(systemName: "xmark")
                                        .font(.system(size: 14, weight: .bold))
                                    Text("SKIP")
                                        .font(.caption.bold())
                                }
                                .foregroundColor(.red.opacity(getSkipOpacity() * 0.8 + 0.2))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    Capsule()
                                        .fill(Color.red.opacity(getSkipOpacity() * 0.1))
                                        .overlay(
                                            Capsule()
                                                .stroke(Color.red.opacity(getSkipOpacity() * 0.3), lineWidth: 1)
                                        )
                                )
                                .opacity(getSkipOpacity())

                                Spacer()

                                // Confirm label
                                HStack(spacing: 4) {
                                    Text("CONFIRM")
                                        .font(.caption.bold())
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 14, weight: .bold))
                                }
                                .foregroundColor(.green.opacity(getConfirmOpacity() * 0.8 + 0.2))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    Capsule()
                                        .fill(Color.green.opacity(getConfirmOpacity() * 0.1))
                                        .overlay(
                                            Capsule()
                                                .stroke(Color.green.opacity(getConfirmOpacity() * 0.3), lineWidth: 1)
                                        )
                                )
                                .opacity(getConfirmOpacity())
                            }
                            .padding(.horizontal, 24)
                            .padding(.bottom, 24)
                        }
                    }
                )
                .offset(x: offset.width, y: offset.height * 0.2)
                .rotationEffect(.degrees(Double(offset.width / 20)))
                .gesture(
                    DragGesture()
                        .onChanged { gesture in
                            offset = gesture.translation
                            updateSwipeDirection()

                            // Start animation when dragging
                            if !animateIndicator {
                                withAnimation(Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                                    animateIndicator = true
                                }
                            }
                        }
                        .onEnded { gesture in
                            handleSwipe(with: gesture)
                        }
                )
                .animation(.spring(response: 0.4, dampingFraction: 0.6), value: offset)
        }
        .onAppear {
            // Start animation when view appears
            withAnimation(Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                animateIndicator = true
            }
        }
    }

    // Update swipe direction based on current offset
    private func updateSwipeDirection() {
        // Only track horizontal swipes
        if offset.width > 10 {
            swipeDirection = .right
        } else if offset.width < -10 {
            swipeDirection = .left
        } else {
            swipeDirection = .none
        }
    }

    // Get color based on swipe direction
    private func getSwipeColor() -> Color {
        switch swipeDirection {
        case .left:
            return .red
        case .right:
            return .green
        case .none:
            return .clear
        }
    }

    // Get overall swipe opacity for effects - horizontal only
    private func getSwipeOpacity() -> Double {
        let dragThreshold: CGFloat = 80

        if offset.width != 0 {
            return Double(min(abs(offset.width) / dragThreshold, 1.0))
        } else {
            return 0
        }
    }

    // Calculate opacity for the skip indicator based on swipe position
    private func getSkipOpacity() -> Double {
        let dragThreshold: CGFloat = 80

        if offset.width < 0 {
            return Double(min(abs(offset.width) / dragThreshold, 1.0))
        } else {
            return 0
        }
    }

    // Calculate opacity for the confirm indicator based on swipe position
    private func getConfirmOpacity() -> Double {
        let dragThreshold: CGFloat = 80

        if offset.width > 0 {
            return Double(min(abs(offset.width) / dragThreshold, 1.0))
        } else {
            return 0
        }
    }



    // Handle the end of a swipe gesture
    private func handleSwipe(with gesture: DragGesture.Value) {
        let swipedRight = gesture.translation.width > swipeThreshold
        let swipedLeft = gesture.translation.width < -swipeThreshold

        if swipedRight {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                offset.width = 1000
                offset.height = 100
                isRemoved = true
            }
            onSwipeRight()
        } else if swipedLeft {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                offset.width = -1000
                offset.height = 100
                isRemoved = true
            }
            onSwipeLeft()
        } else {
            resetCard()
        }
    }

    // Reset card to center position
    private func resetCard() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.6)) {
            offset = .zero
            swipeDirection = .none
        }
    }
}

// MARK: - Preview
struct SwipeCardView_Previews: PreviewProvider {
    static var previews: some View {
        SwipeCardView(
            content: {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Sample Card")
                        .font(.title2)
                        .foregroundColor(.primary)

                    Text("This is a sample card that can be swiped left or right.")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .padding(24)
            },
            onSwipeRight: {
                print("Swiped right (confirm)")
            },
            onSwipeLeft: {
                print("Swiped left (skip)")
            }
        )
        .frame(height: 400)
        .padding()
    }
}
