import SwiftUI

/// 隐藏入口手势修饰符
struct SecretTapGesture: ViewModifier {
    // 需要连续点击的次数
    private let requiredTaps: Int
    // 点击有效的时间窗口（秒）
    private let timeWindow: Double
    // 点击触发的动作
    private let action: () -> Void
    
    // 当前点击次数
    @State private var tapCount = 0
    // 上次点击时间
    @State private var lastTapTime = Date()
    
    init(requiredTaps: Int = 5, timeWindow: Double = 3.0, action: @escaping () -> Void) {
        self.requiredTaps = requiredTaps
        self.timeWindow = timeWindow
        self.action = action
    }
    
    func body(content: Content) -> some View {
        content
            .onTapGesture {
                let now = Date()
                
                // 检查是否在时间窗口内
                if now.timeIntervalSince(lastTapTime) <= timeWindow {
                    // 增加点击计数
                    tapCount += 1
                    
                    // 如果达到所需点击次数，执行动作
                    if tapCount >= requiredTaps {
                        action()
                        // 重置计数
                        tapCount = 0
                    }
                } else {
                    // 超出时间窗口，重置计数
                    tapCount = 1
                }
                
                // 更新上次点击时间
                lastTapTime = now
            }
    }
}

extension View {
    /// 添加隐藏入口手势
    /// - Parameters:
    ///   - requiredTaps: 需要连续点击的次数
    ///   - timeWindow: 点击有效的时间窗口（秒）
    ///   - action: 点击触发的动作
    /// - Returns: 修饰后的视图
    func secretTapGesture(requiredTaps: Int = 5, timeWindow: Double = 3.0, action: @escaping () -> Void) -> some View {
        self.modifier(SecretTapGesture(requiredTaps: requiredTaps, timeWindow: timeWindow, action: action))
    }
}
