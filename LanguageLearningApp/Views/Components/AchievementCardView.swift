import SwiftUI

struct AchievementCardView: View {
    let achievement: Achievement
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 16) {
                // Icon with gradient background
                ZStack {
                    Group {
                        if achievement.isUnlocked {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [AppTheme.Colors.primary, AppTheme.Colors.secondary]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                        } else {
                            Circle()
                                .fill(AppTheme.Colors.card)
                        }
                    }
                    .frame(width: 50, height: 50)
                    .shadow(color: achievement.isUnlocked ? AppTheme.Colors.primary.opacity(0.5) : Color.clear, radius: 8)

                    Image(systemName: achievement.icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(achievement.isUnlocked ? .white : AppTheme.Colors.textTertiary)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(localizationManager.localizedString(achievement.titleKey))
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    Text(localizationManager.localizedString(achievement.descriptionKey))
                        .font(AppTheme.Typography.footnote)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .lineLimit(2)
                }

                Spacer()

                if achievement.isUnlocked {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(AppTheme.Colors.accent3)
                }
            }

            if !achievement.isUnlocked {
                VStack(alignment: .leading, spacing: 8) {
                    // Progress bar with integrated text
                    StyledProgressBar(
                        progress: Double(achievement.progress) / Double(achievement.requirement),
                        height: 12,
                        showProgressText: true,
                        currentValue: achievement.progress,
                        maxValue: achievement.requirement
                    )
                    .padding(.horizontal, 2) // 添加一点水平内边距，确保对齐

                    HStack {
                        Text(String(format: localizationManager.localizedString(LocalizationKey.achievement_progress), achievement.progress, achievement.requirement))
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textSecondary)

                        Spacer()

                        HStack(spacing: 4) {
                            Image(systemName: "star.fill")
                                .font(.system(size: 12))
                                .foregroundColor(AppTheme.Colors.accent2)

                            Text(String(format: localizationManager.localizedString(LocalizationKey.reward_points), achievement.reward))
                                .font(AppTheme.Typography.caption1)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                        }
                    }
                }
            } else {
                // For unlocked achievements, show when it was unlocked
                HStack {
                    Spacer()

                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.system(size: 12))
                            .foregroundColor(AppTheme.Colors.accent2)

                        Text("+\(achievement.reward)")
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.accent2)
                    }
                    .padding(.horizontal, 10)
                    .padding(.vertical, 4)
                    .background(AppTheme.Colors.accent2.opacity(0.2))
                    .cornerRadius(12)
                }
            }
        }
        .padding(16)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

// Keep the original AchievementCard for backward compatibility
struct AchievementCard: View {
    let achievement: Achievement
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        AchievementCardView(achievement: achievement)
    }
}
