import SwiftUI

// Using Color+Hex extension from LanguageLearningApp/Extensions/Color+Hex.swift

// MARK: - WordLearningView
struct WordLearningView: View {
    @StateObject private var viewModel = WordLearningViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var showingSettings = false
    @EnvironmentObject private var errorManager: ErrorManager
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        StyledContainer {
            VStack(spacing: AppTheme.Dimensions.spacingLarge) {
                if let word = viewModel.currentWord {
                    // Word card
                    EnhancedWordCardView(word: word, viewModel: viewModel)

                    // Control buttons
                    EnhancedControlButtonsView(viewModel: viewModel)

                    // Progress bar
                    VStack(spacing: AppTheme.Dimensions.spacingSmall) {
                        HStack {
                            Text(String(format: localizationManager.localizedString("progress"), Int(viewModel.progress * 100)))
                                .font(AppTheme.Typography.footnote)
                                .foregroundColor(AppTheme.Colors.textSecondary)

                            Spacer()

                            Text("\(viewModel.currentWordIndex + 1)/\(viewModel.words.count)")
                                .font(AppTheme.Typography.footnote)
                                .foregroundColor(AppTheme.Colors.accent)
                        }

                        GeometryReader { geometry in
                            ZStack(alignment: .leading) {
                                // Background
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                    .fill(AppTheme.Colors.backgroundSecondary)
                                    .frame(height: 8)

                                // Progress
                                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                                    .fill(AppTheme.Colors.accentGradient)
                                    .frame(width: max(0, min(geometry.size.width * viewModel.progress, geometry.size.width)), height: 8)
                            }
                        }
                        .frame(height: 8)
                    }
                    .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
                } else {
                    EnhancedNoWordsView()
                }
            }
        }
        .navigationTitle(localizationManager.localizedString("word_learning"))
        #if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
        #endif
        .toolbar {
            // Remove the custom back button for iOS since the default navigation back button is already present

            #if os(macOS)
            // Keep the back button for macOS
            ToolbarItem(placement: .navigation) {
                Button(action: { dismiss() }) {
                    HStack {
                        Image(systemName: "chevron.left")
                            .font(.system(size: AppTheme.Dimensions.iconSizeSmall, weight: .semibold))
                        Text(localizationManager.localizedString("back"))
                            .font(AppTheme.Typography.subheadline)
                    }
                    .foregroundColor(AppTheme.Colors.textPrimary)
                }
            }

            ToolbarItem(placement: .primaryAction) {
                Button(action: {
                    showingSettings.toggle()
                }) {
                    Image(systemName: "gear")
                        .foregroundColor(AppTheme.Colors.textPrimary)
                }
            }
            #endif
        }
        #if os(macOS)
        .sheet(isPresented: $showingSettings) {
            SettingsView()
                .environmentObject(errorManager)
        }
        #endif
    }
}

// MARK: - Enhanced Word Card View
struct EnhancedWordCardView: View {
    let word: Word
    @ObservedObject var viewModel: WordLearningViewModel
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var isPlayingPronunciation = false

    var body: some View {
        VStack(spacing: AppTheme.Dimensions.spacingLarge) {
            // Word header with part of speech
            HStack {
                Text(word.partOfSpeech)
                    .font(AppTheme.Typography.caption)
                    .foregroundColor(AppTheme.Colors.textSecondary)
                    .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                    .padding(.vertical, 6)
                    .background(AppTheme.Colors.backgroundSecondary)
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)

                Spacer()

                // Difficulty indicator
                HStack(spacing: 4) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(index < word.difficulty ? AppTheme.Colors.accent2 : AppTheme.Colors.backgroundSecondary)
                            .frame(width: 8, height: 8)
                    }
                }
                .padding(.horizontal, AppTheme.Dimensions.paddingSmall)
                .padding(.vertical, 6)
                .background(AppTheme.Colors.backgroundSecondary)
                .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
            }
            .padding(.horizontal, AppTheme.Dimensions.paddingLarge)

            // Word with pronunciation button
            HStack {
                Spacer()

                VStack(spacing: AppTheme.Dimensions.spacingMedium) {
                    Text(word.text)
                        .font(AppTheme.Typography.largeTitle)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .multilineTextAlignment(.center)

                    // Pronunciation button
                    Button(action: {
                        // Play pronunciation (in a real app, this would use TTS or audio file)
                        isPlayingPronunciation.toggle()

                        // Simulate audio playback
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                            isPlayingPronunciation = false
                        }
                    }) {
                        HStack(spacing: AppTheme.Dimensions.spacingSmall) {
                            Image(systemName: isPlayingPronunciation ? "pause.circle.fill" : "play.circle.fill")
                                .font(.system(size: AppTheme.Dimensions.iconSizeMedium))

                            Text(word.pronunciation)
                                .font(AppTheme.Typography.subheadline)
                        }
                        .foregroundColor(AppTheme.Colors.accent)
                        .padding(.vertical, AppTheme.Dimensions.paddingSmall)
                        .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
                        .background(AppTheme.Colors.accent.opacity(0.1))
                        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                    }
                }

                Spacer()
            }

            // Translation (if showing)
            if viewModel.isShowingTranslation {
                VStack(spacing: AppTheme.Dimensions.spacingSmall) {
                    Text(localizationManager.localizedString("translation"))
                        .font(AppTheme.Typography.caption)
                        .foregroundColor(AppTheme.Colors.textSecondary)

                    Text(word.translation)
                        .font(AppTheme.Typography.title2)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .multilineTextAlignment(.center)
                }
                .padding(.vertical, AppTheme.Dimensions.paddingMedium)
                .padding(.horizontal, AppTheme.Dimensions.paddingLarge)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .fill(AppTheme.Colors.backgroundSecondary)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            AppTheme.Colors.textPrimary.opacity(0.2),
                                            AppTheme.Colors.textPrimary.opacity(0.05)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                )
            }

            // Example (if showing)
            if viewModel.isShowingExample {
                EnhancedExampleView(word: word)
            }
        }
        .padding(AppTheme.Dimensions.paddingLarge)
        .frame(maxWidth: .infinity)
        .background(AppTheme.Colors.backgroundSecondary)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            AppTheme.Colors.textPrimary.opacity(0.2),
                            AppTheme.Colors.textPrimary.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: AppTheme.Shadows.medium.color, radius: AppTheme.Shadows.medium.radius, x: AppTheme.Shadows.medium.x, y: AppTheme.Shadows.medium.y)
        .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
    }
}

// MARK: - Enhanced Example View
struct EnhancedExampleView: View {
    let word: Word
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.Dimensions.spacingMedium) {
            HStack {
                Image(systemName: "text.quote")
                    .font(.system(size: AppTheme.Dimensions.iconSizeSmall))
                    .foregroundColor(AppTheme.Colors.accent2)

                Text(localizationManager.localizedString("wordExample"))
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)
            }

            Text(word.exampleSentence)
                .font(AppTheme.Typography.body)
                .foregroundColor(AppTheme.Colors.textSecondary)
                .lineSpacing(4)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(AppTheme.Dimensions.paddingLarge)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .fill(AppTheme.Colors.backgroundSecondary)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    AppTheme.Colors.textPrimary.opacity(0.2),
                                    AppTheme.Colors.textPrimary.opacity(0.05)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
    }
}

// MARK: - Enhanced Control Buttons View
struct EnhancedControlButtonsView: View {
    @ObservedObject var viewModel: WordLearningViewModel
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        HStack(spacing: AppTheme.Dimensions.spacingSmall) {
            // Previous button
            EnhancedNavigationButton(
                action: viewModel.previousWord,
                isDisabled: viewModel.currentWordIndex == 0,
                icon: "chevron.left",
                text: localizationManager.localizedString("previous")
            )

            // Toggle translation button
            EnhancedToggleButton(
                action: viewModel.toggleTranslation,
                isActive: viewModel.isShowingTranslation,
                activeIcon: "text.badge.minus",
                inactiveIcon: "text.badge.plus",
                activeText: localizationManager.localizedString("hide_translation"),
                inactiveText: localizationManager.localizedString("show_translation")
            )

            // Toggle example button
            EnhancedToggleButton(
                action: viewModel.toggleExample,
                isActive: viewModel.isShowingExample,
                activeIcon: "text.quote.rtl",
                inactiveIcon: "text.quote",
                activeText: localizationManager.localizedString("hide_example"),
                inactiveText: localizationManager.localizedString("show_example")
            )

            // Next button
            EnhancedNavigationButton(
                action: viewModel.nextWord,
                isDisabled: viewModel.currentWordIndex == viewModel.words.count - 1,
                icon: "chevron.right",
                text: localizationManager.localizedString("next"),
                isReversed: true
            )
        }
        .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
    }
}

// MARK: - Enhanced Navigation Button
struct EnhancedNavigationButton: View {
    let action: () -> Void
    let isDisabled: Bool
    let icon: String
    let text: String
    var isReversed: Bool = false

    var body: some View {
        Button(action: action) {
            HStack(spacing: AppTheme.Dimensions.spacingSmall) {
                if isReversed {
                    Text(text)
                        .font(AppTheme.Typography.subheadline)

                    Image(systemName: icon)
                        .font(.system(size: AppTheme.Dimensions.iconSizeSmall, weight: .semibold))
                } else {
                    Image(systemName: icon)
                        .font(.system(size: AppTheme.Dimensions.iconSizeSmall, weight: .semibold))

                    Text(text)
                        .font(AppTheme.Typography.subheadline)
                }
            }
            .padding(.vertical, AppTheme.Dimensions.paddingSmall)
            .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
            .foregroundColor(isDisabled ? AppTheme.Colors.textTertiary : AppTheme.Colors.textPrimary)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .fill(AppTheme.Colors.backgroundSecondary)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                            .stroke(AppTheme.Colors.textPrimary.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isDisabled)
        .opacity(isDisabled ? 0.5 : 1.0)
    }
}

// MARK: - Enhanced Toggle Button
struct EnhancedToggleButton: View {
    let action: () -> Void
    let isActive: Bool
    let activeIcon: String
    let inactiveIcon: String
    let activeText: String
    let inactiveText: String

    var body: some View {
        Button(action: action) {
            HStack(spacing: AppTheme.Dimensions.spacingSmall) {
                Image(systemName: isActive ? activeIcon : inactiveIcon)
                    .font(.system(size: AppTheme.Dimensions.iconSizeSmall))

                Text(isActive ? activeText : inactiveText)
                    .font(AppTheme.Typography.subheadline)
            }
            .padding(.vertical, AppTheme.Dimensions.paddingSmall)
            .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
            .foregroundColor(isActive ? AppTheme.Colors.accent : AppTheme.Colors.textPrimary)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                    .fill(isActive ? AppTheme.Colors.accent.opacity(0.1) : AppTheme.Colors.backgroundSecondary)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                            .stroke(
                                isActive ? AppTheme.Colors.accent.opacity(0.3) : AppTheme.Colors.textPrimary.opacity(0.1),
                                lineWidth: 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Enhanced No Words View
struct EnhancedNoWordsView: View {
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        UnifiedEmptyStateView(
            icon: "book.closed",
            title: localizationManager.localizedString("no_words"),
            message: "Try adding some words to your vocabulary list"
        )
        .padding(AppTheme.Dimensions.paddingLarge)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.Colors.backgroundSecondary)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            AppTheme.Colors.textPrimary.opacity(0.2),
                            AppTheme.Colors.textPrimary.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: AppTheme.Shadows.medium.color, radius: AppTheme.Shadows.medium.radius, x: AppTheme.Shadows.medium.x, y: AppTheme.Shadows.medium.y)
        .padding(.horizontal, AppTheme.Dimensions.paddingMedium)
    }
}

struct WordPracticeView: View {
    @StateObject private var viewModel = WordLearningViewModel()
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var userAnswer = ""
    @State private var showResult = false
    @State private var score = 0
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var errorManager: ErrorManager

    var body: some View {
        practiceContent
    }

    private var practiceContent: some View {
        VStack(spacing: AppTheme.Dimensions.spacingLarge) {
            if let currentWord = viewModel.currentWord {
                PracticeQuestionView(
                    word: currentWord,
                    userAnswer: $userAnswer,
                    onSubmit: checkAnswer
                )
            } else {
                PracticeResultView(
                    score: score,
                    totalWords: viewModel.words.count,
                    onDismiss: { dismiss() }
                )
            }
        }
        .padding()
        .navigationTitle(localizationManager.localizedString("word_practice"))
        #if os(iOS)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { dismiss() }) {
                    Text(localizationManager.localizedString("exit"))
                        .foregroundColor(AppTheme.Colors.textPrimary)
                }
            }
        }
        #endif
    }

    private func checkAnswer() {
        guard let currentWord = viewModel.currentWord else { return }
        let correct = userAnswer.lowercased() == currentWord.translation.lowercased()
        if correct {
            score += 1
        }

        showResult = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            showResult = false
            userAnswer = ""
            viewModel.nextWord()
        }
    }
}

struct PracticeQuestionView: View {
    let word: Word
    @Binding var userAnswer: String
    let onSubmit: () -> Void
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        VStack(spacing: AppTheme.Dimensions.spacingLarge) {
            Text(localizationManager.localizedString("translate_word"))
                .font(AppTheme.Typography.headline)
                .foregroundColor(AppTheme.Colors.textPrimary)

            Text(word.text)
                .font(AppTheme.Typography.largeTitle)
                .foregroundColor(AppTheme.Colors.textPrimary)
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                        .fill(AppTheme.Colors.backgroundSecondary)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            AppTheme.Colors.textPrimary.opacity(0.2),
                                            AppTheme.Colors.textPrimary.opacity(0.05)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                )
                .shadow(color: AppTheme.Shadows.small.color, radius: AppTheme.Shadows.small.radius, x: AppTheme.Shadows.small.x, y: AppTheme.Shadows.small.y)

            TextField(localizationManager.localizedString("enter_translation"), text: $userAnswer)
                .padding()
                .background(AppTheme.Colors.backgroundSecondary)
                .foregroundColor(AppTheme.Colors.textPrimary)
                .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                        .stroke(AppTheme.Colors.textPrimary.opacity(0.2), lineWidth: 1)
                )
                .padding(.horizontal)

            Button(action: onSubmit) {
                Text(localizationManager.localizedString("submit"))
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(AppTheme.Colors.accentGradient)
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
            }
            .padding(.horizontal)
        }
        .padding()
        .background(AppTheme.Colors.background)
    }
}

struct PracticeResultView: View {
    let score: Int
    let totalWords: Int
    let onDismiss: () -> Void
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        VStack(spacing: AppTheme.Dimensions.spacingLarge) {
            // Success icon
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: AppTheme.Dimensions.iconSizeLarge * 2.5))
                .foregroundColor(AppTheme.Colors.success)
                .padding(.bottom, AppTheme.Dimensions.paddingMedium)

            Text(localizationManager.localizedString("practice_complete"))
                .font(AppTheme.Typography.title1)
                .foregroundColor(AppTheme.Colors.textPrimary)
                .multilineTextAlignment(.center)

            Text(String(format: localizationManager.localizedString("score"), score, totalWords))
                .font(AppTheme.Typography.title2)
                .foregroundColor(AppTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.bottom, AppTheme.Dimensions.paddingMedium)

            Button(action: onDismiss) {
                Text(localizationManager.localizedString("back"))
                    .font(AppTheme.Typography.headline)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(AppTheme.Colors.accentGradient)
                    .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
            }
            .padding(.horizontal)
        }
        .padding(AppTheme.Dimensions.paddingLarge)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                .fill(AppTheme.Colors.backgroundSecondary)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusLarge)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    AppTheme.Colors.textPrimary.opacity(0.2),
                                    AppTheme.Colors.textPrimary.opacity(0.05)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .shadow(color: AppTheme.Shadows.large.color, radius: AppTheme.Shadows.large.radius, x: AppTheme.Shadows.large.x, y: AppTheme.Shadows.large.y)
        .padding()
    }
}