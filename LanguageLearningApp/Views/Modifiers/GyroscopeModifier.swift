import SwiftUI
import CoreMotion

class MotionManager: ObservableObject {
    private let motionManager = CMMotionManager()
    @Published var pitch: Double = 0
    @Published var roll: Double = 0
    
    init() {
        motionManager.deviceMotionUpdateInterval = 1/60
        motionManager.startDeviceMotionUpdates(to: .main) { [weak self] motion, error in
            guard let motion = motion else { return }
            self?.pitch = motion.attitude.pitch
            self?.roll = motion.attitude.roll
        }
    }
    
    deinit {
        motionManager.stopDeviceMotionUpdates()
    }
}

struct GyroscopeModifier: ViewModifier {
    @StateObject private var motionManager = MotionManager()
    let intensity: CGFloat
    
    func body(content: Content) -> some View {
        content
            .offset(x: CGFloat(motionManager.roll) * intensity * 0.1,
                   y: CGFloat(motionManager.pitch) * intensity * 0.1)
            .rotation3DEffect(
                .degrees(motionManager.roll * 8),
                axis: (x: 0, y: 1, z: 0),
                perspective: 0.5
            )
            .rotation3DEffect(
                .degrees(motionManager.pitch * 8),
                axis: (x: 1, y: 0, z: 0),
                perspective: 0.5
            )
            .scaleEffect(1 + abs(motionManager.roll) * 0.01 + abs(motionManager.pitch) * 0.01)
            .shadow(
                color: .black.opacity(0.15 + abs(motionManager.roll) * 0.1 + abs(motionManager.pitch) * 0.1),
                radius: 8 + abs(motionManager.roll) * 3 + abs(motionManager.pitch) * 3,
                x: CGFloat(motionManager.roll) * 3,
                y: CGFloat(motionManager.pitch) * 3
            )
    }
}

extension View {
    func gyroscopeEffect(intensity: CGFloat = 8) -> some View {
        modifier(GyroscopeModifier(intensity: intensity))
    }
} 