import SwiftUI
import Foundation

struct LanguageSettingsView: View {
    @StateObject private var localizationManager = LocalizationManager.shared
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var userManager: UserManager
    @State private var isSaving = false
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            StyledContainer {
                VStack(spacing: 24) {
                    // Header with icon
                    VStack(spacing: 16) {
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            AppTheme.Colors.accent1,
                                            AppTheme.Colors.accent3
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 80, height: 80)
                                .shadow(color: AppTheme.Colors.accent1.opacity(0.5), radius: 10, x: 0, y: 5)

                            Image(systemName: "globe")
                                .font(.system(size: 30))
                                .foregroundColor(.white)
                        }

                        Text(localizationManager.localizedString("language"))
                            .font(AppTheme.Typography.title2)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(localizationManager.localizedString("language_preference_subtitle"))
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.bottom, 10)

                    // Language Selection
                    StyledSectionHeader(title: localizationManager.localizedString("language"))

                    StyledCard {
                        VStack(spacing: 0) {
                            ForEach(localizationManager.supportedLanguages, id: \.0) { language in
                                Button(action: {
                                    withAnimation {
                                        // 更新本地语言设置
                                        localizationManager.currentLanguage = language.0

                                        // 同步语言设置到服务器
                                        updateLanguageSettings(language: language.0)
                                    }
                                }) {
                                    HStack {
                                        // Language flag or icon
                                        Text(languageFlag(for: language.0))
                                            .font(.system(size: 24))
                                            .frame(width: 40, height: 40)

                                        Text(language.1)
                                            .font(AppTheme.Typography.body)
                                            .foregroundColor(AppTheme.Colors.textPrimary)

                                        Spacer()

                                        if localizationManager.currentLanguage == language.0 {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(AppTheme.Colors.accent3)
                                                .font(.system(size: 18))
                                                .transition(.scale.combined(with: .opacity))
                                        }
                                    }
                                    .padding(.vertical, 14)
                                    .padding(.horizontal, 16)
                                }

                                if language.0 != localizationManager.supportedLanguages.last?.0 {
                                    Divider()
                                        .background(Color.white.opacity(0.1))
                                        .padding(.horizontal)
                                }
                            }
                        }
                    }

                    // Language Info
                    StyledCard {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Image(systemName: "info.circle.fill")
                                    .foregroundColor(AppTheme.Colors.accent1)
                                    .font(.system(size: 20))

                                Text(localizationManager.localizedString("language_settings_info_title"))
                                    .font(AppTheme.Typography.headline)
                                    .foregroundColor(AppTheme.Colors.textPrimary)
                            }

                            Text(localizationManager.localizedString("language_settings_info_description"))
                                .font(AppTheme.Typography.body)
                                .foregroundColor(AppTheme.Colors.textSecondary)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(16)
                    }

                    Spacer(minLength: 30)
                }
            }
            .navigationTitle(localizationManager.localizedString("language"))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { dismiss() }) {
                        Text(localizationManager.localizedString("done"))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }
                }
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text(alertTitle),
                    message: Text(alertMessage),
                    dismissButton: .default(Text(localizationManager.localizedString("ok")))
                )
            }
        }
    }

    // 更新语言设置到服务器
    private func updateLanguageSettings(language: String) {
        isSaving = true

        Task {
            do {
                // 创建用户设置对象
                var settings = UserSettings()
                if let user = userManager.currentUser {
                    settings = user.settings ?? UserSettings()
                }

                // 更新语言设置
                settings.preferredLanguage = language

                // 调用API更新设置
                try await userManager.updateSettings(settings: settings)

                DispatchQueue.main.async {
                    self.isSaving = false
                    // 成功时不显示提示
                }
            } catch {
                DispatchQueue.main.async {
                    self.isSaving = false
                    self.alertTitle = localizationManager.localizedString("settings_error")
                    self.alertMessage = error.localizedDescription
                    self.showAlert = true
                }
            }
        }
    }

    // 根据语言代码返回对应的国旗表情
    private func languageFlag(for languageCode: String) -> String {
        switch languageCode {
        case "en":
            return "🇺🇸"
        case "zh-Hans":
            return "🇨🇳"
        case "ja":
            return "🇯🇵"
        case "ko":
            return "🇰🇷"
        case "fr":
            return "🇫🇷"
        case "de":
            return "🇩🇪"
        case "es":
            return "🇪🇸"
        default:
            return "🌐"
        }
    }
}

#Preview {
    LanguageSettingsView()
}
