import SwiftUI
import Foundation

// MARK: - Settings Row
struct SettingsRow: View {
    let icon: String
    let title: String
    var subtitle: String? = nil
    var showChevron: Bool = true
    var iconGradient: LinearGradient = AppTheme.Colors.primaryGradient

    var body: some View {
        HStack(spacing: 16) {
            // Icon with gradient background
            ZStack {
                Circle()
                    .fill(AppTheme.Colors.background)
                    .frame(width: 40, height: 40)
                    .overlay(
                        Circle()
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.2),
                                        Color.white.opacity(0.05)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )

                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundStyle(iconGradient)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(AppTheme.Typography.callout)
                    .foregroundColor(AppTheme.Colors.textPrimary)

                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(AppTheme.Typography.caption1)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }
            }

            Spacer()

            if showChevron {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(AppTheme.Colors.textTertiary)
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .contentShape(Rectangle())
    }
}

// MARK: - Settings View
struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showLanguageSettings = false
    @State private var showNotificationSettings = false
    @State private var showAccountSettings = false
    @State private var showAbout = false
    @State private var showEnvironmentSettings = false
    @State private var showTTSSettings = false
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        NavigationView {
            StyledContainer {
                VStack(spacing: 24) {
                    // Account Settings
                    StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.account))

                    StyledCard {
                        Button(action: { showAccountSettings = true }) {
                            SettingsRow(
                                icon: "person.fill",
                                title: localizationManager.localizedString(LocalizationKey.account_settings),
                                subtitle: localizationManager.localizedString(LocalizationKey.account_settings_subtitle)
                            )
                        }
                    }

                    // App Settings
                    StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.app_settings))

                    StyledCard {
                        VStack(spacing: 0) {
                            Button(action: { showLanguageSettings = true }) {
                                SettingsRow(
                                    icon: "globe",
                                    title: localizationManager.localizedString(LocalizationKey.language),
                                    subtitle: localizationManager.localizedString(LocalizationKey.change_app_language_subtitle),
                                    iconGradient: LinearGradient(
                                        gradient: Gradient(colors: [
                                            AppTheme.Colors.accent1,
                                            AppTheme.Colors.accent3
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                            }

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            NavigationLink(destination: TTSSettingsView()) {
                                SettingsRow(
                                    icon: "speaker.wave.2.fill",
                                    title: localizationManager.localizedString(LocalizationKey.tts_settings_title),
                                    subtitle: localizationManager.localizedString(LocalizationKey.tts_settings_subtitle)
                                )
                            }
                             Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            NavigationLink(destination: ThemeSettingsView()) {
                                SettingsRow(
                                    icon: "paintbrush.fill",
                                    title: localizationManager.localizedString(LocalizationKey.theme_settings),
                                    subtitle: localizationManager.localizedString(LocalizationKey.theme_settings_subtitle)
                                )
                            }

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            Button(action: { showNotificationSettings = true }) {
                                SettingsRow(
                                    icon: "bell.fill",
                                    title: localizationManager.localizedString(LocalizationKey.notifications),
                                    subtitle: localizationManager.localizedString(LocalizationKey.manage_notification_preferences_subtitle),
                                    iconGradient: LinearGradient(
                                        gradient: Gradient(colors: [
                                            AppTheme.Colors.accent2,
                                            AppTheme.Colors.secondary
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                            }
                        }
                    }

                    // About and Support
                    StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.about_support_section_title))

                    StyledCard {
                        VStack(spacing: 0) {
                            Button(action: { showAbout = true }) {
                                SettingsRow(
                                    icon: "info.circle.fill",
                                    title: localizationManager.localizedString(LocalizationKey.about),
                                    subtitle: localizationManager.localizedString(LocalizationKey.app_information_credits_subtitle)
                                )
                            }

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            Button(action: {
                                if let url = URL(string: "https://help.languagelearningapp.com") {
                                    UIApplication.shared.open(url)
                                }
                            }) {
                                SettingsRow(
                                    icon: "questionmark.circle.fill",
                                    title: localizationManager.localizedString(LocalizationKey.help_support),
                                    subtitle: localizationManager.localizedString(LocalizationKey.get_help_app_subtitle)
                                )
                            }
                        }
                    }

                    // Version Info
                    Button(action: {
                        var tapCount = UserDefaults.standard.integer(forKey: "dev_settings_tap_count")
                        tapCount += 1
                        if tapCount >= 7 {
                            showEnvironmentSettings = true
                            UserDefaults.standard.set(0, forKey: "dev_settings_tap_count")
                        } else {
                            UserDefaults.standard.set(tapCount, forKey: "dev_settings_tap_count")
                        }
                    }) {
                        HStack {
                            Text(localizationManager.localizedString(LocalizationKey.version_label))
                                .font(AppTheme.Typography.footnote)
                                .foregroundColor(AppTheme.Colors.textSecondary)

                            Spacer()

                            Text("1.0.0")
                                .font(AppTheme.Typography.footnote)
                                .foregroundColor(AppTheme.Colors.textTertiary)
                        }
                        .padding(.horizontal, 16)
                    }

                    Spacer(minLength: 50)
                }
            }
            .navigationTitle(localizationManager.localizedString(LocalizationKey.settings))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { dismiss() }) {
                        Text(localizationManager.localizedString(LocalizationKey.done))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }
                }
            }
            .sheet(isPresented: $showEnvironmentSettings) {
                EnvironmentSettingsView()
            }
            .sheet(isPresented: $showLanguageSettings) {
                LanguageSettingsView()
            }
            .sheet(isPresented: $showNotificationSettings) {
                NotificationSettingsView()
            }
            .sheet(isPresented: $showAccountSettings) {
                AccountSettingsView()
            }
            .sheet(isPresented: $showAbout) {
                AboutView()
            }
        }
    }
}

#Preview {
    SettingsView()
}
