import SwiftUI

/// Kokoro TTS 測試視圖 - 用於測試 Kokoro TTS 引擎
struct KokoroTTSTestView: View {
    @StateObject private var ttsManager = TTSManager.shared
    @State private var testText: String = "這是一個測試。Kokoro TTS 提供高質量的神經網絡語音合成。"
    @State private var isPlaying: Bool = false
    @State private var errorMessage: String? = nil
    @State private var showModelInfo: Bool = false
    @State private var isKokoroEngineAvailable: Bool = false
    @State private var kokoroAvailabilityMessage: String = "正在檢查 Kokoro TTS 可用性..."
    
    // 獲取 Kokoro TTS 引擎
    private var kokoroEngine: TTSEngine? {
        return ttsManager.availableEngines.first { $0.engineId == "kokoro_tts" }
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Kokoro TTS 引擎")) {
                    if isKokoroEngineAvailable {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Kokoro TTS 引擎可用")
                                .foregroundColor(.primary)
                        }
                    } else {
                        HStack {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.red)
                            Text(kokoroAvailabilityMessage)
                                .foregroundColor(.red)
                        }
                        
                        Text("Kokoro TTS 需要 Apple Silicon 設備（M1/M2/M3 系列芯片）。")
                            .font(.footnote)
                            .foregroundColor(.secondary)
                    }
                    
                    Button(action: {
                        showModelInfo.toggle()
                    }) {
                        HStack {
                            Image(systemName: "info.circle")
                            Text("模型信息")
                        }
                    }
                    .sheet(isPresented: $showModelInfo) {
                        ModelInfoView()
                    }
                }
                
                Section(header: Text("測試文本")) {
                    TextEditor(text: $testText)
                        .frame(height: 100)
                    
                    Button(action: {
                        playTestText()
                    }) {
                        HStack {
                            Image(systemName: isPlaying ? "stop.fill" : "play.fill")
                            Text(isPlaying ? "停止" : "播放")
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .disabled(!isKokoroEngineAvailable)
                    .buttonStyle(BorderedButtonStyle())
                    
                    if let error = errorMessage {
                        Text(error)
                            .foregroundColor(.red)
                            .font(.footnote)
                    }
                }
                
                Section(header: Text("語言支持")) {
                    Text("Kokoro TTS 支持多種語言，包括中文、英文、日文等。")
                        .font(.body)
                    
                    Text("示例：")
                        .font(.headline)
                        .padding(.top, 8)
                    
                    Button(action: {
                        testText = "你好，這是中文測試。Kokoro TTS 提供高質量的語音合成。"
                    }) {
                        Text("中文示例")
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    
                    Button(action: {
                        testText = "Hello, this is an English test. Kokoro TTS provides high-quality speech synthesis."
                    }) {
                        Text("英文示例")
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    
                    Button(action: {
                        testText = "こんにちは、これは日本語のテストです。Kokoro TTSは高品質な音声合成を提供します。"
                    }) {
                        Text("日文示例")
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                
                Section(header: Text("提示")) {
                    Text("Kokoro TTS 是一個基於 MLX-Audio 的高質量神經網絡 TTS 引擎，專為 Apple Silicon 設備優化。")
                        .font(.footnote)
                        .foregroundColor(.secondary)
                    
                    Text("模型大小約為 82M，支持多種語言和語音風格。")
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("Kokoro TTS 測試")
            .task {
                await checkKokoroAvailability()
            }
            .onDisappear {
                // 確保在離開視圖時停止播放
                if isPlaying {
                    ttsManager.stopSample()
                    isPlaying = false
                }
            }
        }
    }
    
    private func checkKokoroAvailability() async {
        if let engine = ttsManager.availableEngines.first(where: { $0.engineId == "kokoro_tts" }) {
            let available = await engine.isAvailable
            self.isKokoroEngineAvailable = available
            if available {
                self.kokoroAvailabilityMessage = "Kokoro TTS 引擎可用"
            } else {
                self.kokoroAvailabilityMessage = "Kokoro TTS 引擎不可用 (可能由於設備不支持或模型未配置)"
            }
        } else {
            self.isKokoroEngineAvailable = false
            self.kokoroAvailabilityMessage = "未找到 Kokoro TTS 引擎"
        }
    }
    
    private func playTestText() {
        if isPlaying {
            ttsManager.stopSample()
            isPlaying = false
            return
        }
        
        guard let kokoroEngine = kokoroEngine else {
            errorMessage = "無法獲取 Kokoro TTS 引擎"
            return
        }
        
        errorMessage = nil
        isPlaying = true
        
        // 保存當前引擎
        let currentEngineId = ttsManager.currentEngine.engineId
        
        // 切換到 Kokoro 引擎
        ttsManager.setCurrentEngine(engineId: kokoroEngine.engineId)
        
        // 播放測試文本
        ttsManager.playSample(text: testText, languageCode: "mul") { error in
            isPlaying = false
            
            // 恢復原來的引擎
            ttsManager.setCurrentEngine(engineId: currentEngineId)
            
            if let error = error {
                errorMessage = "錯誤: \(error.localizedDescription)"
            }
        }
    }
}

/// 模型信息視圖
struct ModelInfoView: View {
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Kokoro TTS 模型")) {
                    InfoRow(title: "模型名稱", value: "Kokoro-82M")
                    InfoRow(title: "參數大小", value: "82M")
                    InfoRow(title: "量化", value: "4-bit")
                    InfoRow(title: "開發者", value: "hexagrad")
                    InfoRow(title: "框架", value: "MLX (Apple)")
                }
                
                Section(header: Text("模型特點")) {
                    Text("• 多語言支持")
                    Text("• 高質量語音合成")
                    Text("• 低延遲（接近實時）")
                    Text("• 完全離線運行")
                    Text("• 針對 Apple Silicon 優化")
                }
                
                Section(header: Text("模型來源")) {
                    Link("Hugging Face: mlx-community/Kokoro-82M-4bit", destination: URL(string: "https://huggingface.co/mlx-community/Kokoro-82M-4bit")!)
                    Link("GitHub: mlalma/kokoro-ios", destination: URL(string: "https://github.com/mlalma/kokoro-ios")!)
                    Link("原始項目: Blaizzy/mlx-audio", destination: URL(string: "https://github.com/Blaizzy/mlx-audio")!)
                }
            }
            .navigationTitle("模型信息")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

/// 信息行視圖
struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .foregroundColor(.primary)
        }
    }
}

struct KokoroTTSTestView_Previews: PreviewProvider {
    static var previews: some View {
        KokoroTTSTestView()
    }
}
