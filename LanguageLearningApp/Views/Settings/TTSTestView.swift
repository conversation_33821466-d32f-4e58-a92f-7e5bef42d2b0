import SwiftUI

/// TTS 測試視圖 - 用於測試不同的 TTS 引擎和聲音
struct TTSTestView: View {
    @StateObject private var ttsManager = TTSManager.shared
    @State private var testText: String = "這是一個測試。Apple Neural TTS 提供高質量的語音合成。"
    @State private var selectedLanguage: String = "zh-CN"
    @State private var selectedVoiceId: String? = nil
    @State private var isPlaying: Bool = false
    @State private var errorMessage: String? = nil
    @State private var selectedEngineId: String = TTSManager.shared.currentEngine.engineId
    @State private var voices: [VoiceInfo] = [] // For storing loaded voices
    @State private var kokoroEngineExists: Bool = false
    @State private var isKokoroEngineActuallyAvailable: Bool = false // Separate state for detailed check
    @State private var isCurrentEngineAvailable: Bool = true // Added for button state

    // 支持的語言列表
    private let supportedLanguages = [
        ("中文 (簡體)", "zh-CN"),
        ("中文 (繁體)", "zh-TW"),
        ("英文 (美國)", "en-US"),
        ("英文 (英國)", "en-GB"),
        ("日文", "ja-JP"),
        ("韓文", "ko-KR")
    ]

    // 示例文本
    private let sampleTexts = [
        "zh-CN": "這是一個測試。Apple Neural TTS 提供高質量的語音合成。",
        "zh-TW": "這是一個測試。Apple Neural TTS 提供高品質的語音合成。",
        "en-US": "This is a test. Apple Neural TTS provides high-quality speech synthesis.",
        "en-GB": "This is a test. Apple Neural TTS provides high-quality speech synthesis.",
        "ja-JP": "これはテストです。Apple Neural TTSは高品質な音声合成を提供します。",
        "ko-KR": "이것은 테스트입니다. Apple Neural TTS는 고품질 음성 합성을 제공합니다."
    ]

    // 獲取當前語言的可用聲音
    // private var availableVoices: [VoiceInfo] { ... } // This is now replaced by @State voices

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("TTS 引擎")) {
                    Picker("選擇 TTS 引擎", selection: $selectedEngineId) {
                        ForEach(ttsManager.availableEngines, id: \.engineId) { engine in
                            // We need to check engine.isAvailable asynchronously.
                            // For the Picker, we can't easily do that directly in the loop.
                            // We might need to filter availableEngines in onAppear or use a Task.
                            // For now, let's assume all engines in ttsManager.availableEngines can be listed,
                            // and the check in `onChange` and the `isAvailable` text below handles usability.
                            Text(engine.engineName) // engine.isAvailable is async, cannot call here
                                .tag(engine.engineId)
                        }
                    }
                    .onChange(of: selectedEngineId) { newValue in
                        Task {
                            // Check availability of new engine before setting if possible, or let TTSManager handle it
                            // For now, just set it. TTSManager's playSample will check.
                            self.ttsManager.setCurrentEngine(engineId: newValue)
                            // Reset selected voice and refresh voices
                            self.selectedVoiceId = nil
                            // TODO: Refresh voices asynchronously if/when availableVoices becomes async
                            // This will be handled by the new .task modifier
                        }
                    }

                    // This check needs to be done asynchronously for the current engine.
                    // We can use a @State variable that is updated by a Task.
                    // For simplicity now, this might show stale data or require a Task in onChange.
                    // Let's add a @State for current engine availability
                    // if !ttsManager.currentEngine.isAvailable { // This is an async property
                    //    Text("所選引擎不可用")
                    //        .foregroundColor(.red)
                    // }
                    CurrentEngineAvailabilityView(engine: ttsManager.currentEngine)
                }

                Section(header: Text("語言和聲音")) {
                    Picker("語言", selection: $selectedLanguage) {
                        ForEach(supportedLanguages, id: \.1) { language in
                            Text(language.0)
                                .tag(language.1)
                        }
                    }
                    .onChange(of: selectedLanguage) { newValue in
                        // 更新示例文本
                        if let sample = self.sampleTexts[newValue] {
                            self.testText = sample
                        }
                        // 重置選擇的聲音
                        self.selectedVoiceId = nil
                        // Voices will be reloaded by the .task modifier watching selectedLanguage
                    }

                    if !self.voices.isEmpty { // Use @State voices
                        Picker("聲音", selection: self.$selectedVoiceId) {
                            Text("默認").tag(nil as String?)
                            ForEach(self.voices) { voice in // Use @State voices
                                Text(voice.name)
                                    .tag(voice.id as String?)
                            }
                        }
                    } else {
                        Text("沒有可用的聲音")
                            .foregroundColor(.secondary)
                    }
                }

                Section(header: Text("測試文本")) {
                    TextEditor(text: $testText)
                        .frame(height: 100)

                    Button(action: {
                        playTestText()
                    }) {
                        HStack {
                            Image(systemName: isPlaying ? "stop.fill" : "play.fill")
                            Text(isPlaying ? "停止" : "播放")
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .disabled(!isCurrentEngineAvailable)
                    .buttonStyle(BorderedButtonStyle())

                    if let error = errorMessage {
                        Text(error)
                            .foregroundColor(.red)
                            .font(.footnote)
                    }
                }

                Section(header: Text("聲音信息")) {
                    if let selectedVoiceDetails = voices.first(where: { $0.id == selectedVoiceId }) {
                        VoiceInfoView(voice: selectedVoiceDetails)
                    } else if let defaultVoice = voices.first {
                        VoiceInfoView(voice: defaultVoice)
                            .opacity(0.7)
                    } else {
                        Text("沒有可用的聲音信息")
                            .foregroundColor(.secondary)
                    }
                }

                Section(header: Text("提示")) {
                    Text("Apple Neural TTS 需要 iOS 13 或更高版本。高質量的神經網絡聲音在較新的設備上可用。")
                        .font(.footnote)
                        .foregroundColor(.secondary)

                    if kokoroEngineExists { // Use state variable
                        Divider()
                            .padding(.vertical, 4)

                        Text("Kokoro TTS 是一個高質量的神經網絡 TTS 引擎，專為 Apple Silicon 設備優化。它支持多種語言，並提供自然的語音合成。")
                            .font(.footnote)
                            .foregroundColor(.secondary)

                        if !isKokoroEngineActuallyAvailable { // Use state variable
                            Text("注意：Kokoro TTS 需要完成集成步驟才能使用，或當前設備不支持。請查看 README_KOKORO_INTEGRATION.md 文件了解詳情。")
                                .font(.footnote)
                                .foregroundColor(.orange)
                                .padding(.top, 4)
                        }
                    }
                }
            }
            .navigationTitle("TTS 測試")
            .task(id: selectedEngineId) { // Reload voices when engine changes
                await loadVoices()
                await updateCurrentEngineAvailability()
            }
            .task(id: selectedLanguage) { // Reload voices when language changes
                await loadVoices()
            }
            .task { // Initial load of voices and Kokoro status
                await loadVoices()
                await checkKokoroStatus()
                await updateCurrentEngineAvailability()
            }
            .onDisappear {
                // 確保在離開視圖時停止播放
                if self.isPlaying {
                    self.ttsManager.stopSample()
                    self.isPlaying = false
                }
            }
        }
    }

    private func loadVoices() async {
        print("TTSTestView: Loading voices for engine \(ttsManager.currentEngine.engineId) and language \(selectedLanguage)")
        let fetchedVoices = await ttsManager.getVoicesForCurrentEngine(languageCode: selectedLanguage)
        await MainActor.run {
            self.voices = fetchedVoices
            // If current selectedVoiceId is no longer in the new list, reset it
            if let currentSelected = selectedVoiceId, !fetchedVoices.contains(where: { $0.id == currentSelected }) {
                self.selectedVoiceId = nil
            }
            // If no voice is selected and there are voices, select the first one as default (optional)
            // else if selectedVoiceId == nil && !fetchedVoices.isEmpty {
            // self.selectedVoiceId = fetchedVoices.first?.id
            // }
            print("TTSTestView: Loaded \(fetchedVoices.count) voices.")
        }
    }
    
    private func checkKokoroStatus() async {
        let engineExists = ttsManager.availableEngines.contains(where: { $0.engineId == "kokoro_tts" })
        self.kokoroEngineExists = engineExists
        if engineExists {
            if let kokoroEngine = ttsManager.availableEngines.first(where: { $0.engineId == "kokoro_tts" }) {
                self.isKokoroEngineActuallyAvailable = await kokoroEngine.isAvailable
            }
        }
    }

    private func updateCurrentEngineAvailability() async {
        self.isCurrentEngineAvailable = await ttsManager.currentEngine.isAvailable
    }

    private func playTestText() {
        if isPlaying {
            ttsManager.stopSample()
            isPlaying = false
            return
        }

        errorMessage = nil
        isPlaying = true

        ttsManager.playSample(text: testText, languageCode: selectedLanguage, voiceIdentifier: selectedVoiceId) { error in
            Task { @MainActor in // Ensure UI updates are on the main actor
                self.isPlaying = false

                if let error = error {
                    self.errorMessage = "錯誤: \(error.localizedDescription)"
                }
            }
        }
    }
}

// Helper View to display current engine availability asynchronously
struct CurrentEngineAvailabilityView: View {
    let engine: TTSEngine
    @State private var isAvailable: Bool = true // Assume available until checked
    @State private var availabilityText: String = "Checking availability..."

    var body: some View {
        Group {
            if !isAvailable {
                Text(availabilityText)
                    .foregroundColor(.red)
            }
        }
        .task(id: engine.engineId) { // Re-run when engine changes
            self.availabilityText = "Checking availability..."
            let available = await engine.isAvailable
            self.isAvailable = available
            if !available {
                self.availabilityText = "所選引擎 (\(engine.engineName)) 不可用"
            }
        }
    }
}

/// 顯示聲音詳細信息的視圖
struct VoiceInfoView: View {
    let voice: VoiceInfo

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("名稱:")
                    .fontWeight(.bold)
                Text(voice.name)
            }

            HStack {
                Text("語言:")
                    .fontWeight(.bold)
                Text(voice.languageCode)
            }

            if let quality = voice.quality {
                HStack {
                    Text("質量:")
                        .fontWeight(.bold)
                    Text(qualityString(quality))
                }
            }

            if let gender = voice.gender {
                HStack {
                    Text("性別:")
                        .fontWeight(.bold)
                    Text(genderString(gender))
                }
            }
        }
    }

    private func qualityString(_ quality: Int) -> String {
        switch quality {
        case 1: return "標準"
        case 2: return "增強"
        case 3: return "高級"
        default: return "未知"
        }
    }

    private func genderString(_ gender: Int) -> String {
        switch gender {
        case 1: return "男性"
        case 2: return "女性"
        default: return "未指定"
        }
    }
}

struct TTSTestView_Previews: PreviewProvider {
    static var previews: some View {
        TTSTestView()
    }
}
