import SwiftUI

struct TTSSettingsView: View {
    @ObservedObject private var ttsManager = TTSManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared

    @State private var selectedEngineId: String
    @State private var selectedVoiceId: String?
    @State private var isPlayingSample: Bool = false
    @State private var samplePlaybackError: String? = nil
    @State private var showAdvancedTTSTest: Bool = false

    // State for async properties
    @State private var currentEngineIsAvailable: Bool = false
    @State private var availableVoicesForPicker: [VoiceInfo] = []
    @State private var kokoroEngineHolder: TTSEngine? = nil // To hold the engine instance
    @State private var kokoroEngineIsAvailable: Bool = false

    // Sample text and language - you might want to make this configurable or use current app language
    private let sampleTextEnglish = "Hello, this is a test of the text to speech engine."
    private let sampleTextChinese = "你好，这是一个语音合成引擎的测试。"
    // Determine which sample text to use based on current app language or a specific setting
    private var currentSampleText: String {
        // Simplified: default to English or Chinese based on current app language prefix
        if localizationManager.currentLanguage.starts(with: "zh") {
            return sampleTextChinese
        } else {
            return sampleTextEnglish
        }
    }
    private var currentSampleLanguageCode: String {
        if localizationManager.currentLanguage.starts(with: "zh") {
            return "zh-CN" // Or a more specific code if available for the engine
        } else {
            return "en-US"
        }
    }

    init() {
        // Initialize selectedEngineId synchronously
        _selectedEngineId = State(initialValue: TTSManager.shared.currentEngine.engineId)
        // Other state variables will be updated by .task
    }

    var body: some View {
        ZStack {
            AppTheme.Colors.background.ignoresSafeArea()
            VStack(spacing: 24) {
                StyledSectionHeader(title: localizationManager.localizedString("tts_engine_selection_header"))

                StyledCard(title: localizationManager.localizedString("tts_engine_config")) {
                    VStack(spacing: 0) {
                        Picker(
                            localizationManager.localizedString("tts_select_engine_label"),
                            selection: $selectedEngineId
                        ) {
                            ForEach(ttsManager.availableEngines, id: \.engineId) { engine in
                                Text(engine.engineName).tag(engine.engineId)
                            }
                        }
                        // .onChange will be handled by .task(id: selectedEngineId)

                        Divider()
                            .background(Color.white.opacity(0.1))
                            .padding(.horizontal)

                        VStack(alignment: .leading, spacing: 10) {
                            Text(currentEngineIsAvailable ? localizationManager.localizedString("tts_status_available") : localizationManager.localizedString("tts_status_not_available"))
                                .font(AppTheme.Typography.caption1)
                                .foregroundColor(currentEngineIsAvailable ? AppTheme.Colors.accent1 : .orange)

                            if !availableVoicesForPicker.isEmpty {
                                Picker(localizationManager.localizedString("tts_voice_selection_label"),
                                    selection: $selectedVoiceId) {
                                    ForEach(availableVoicesForPicker, id: \.id) { voice in
                                        Text(voice.name).tag(voice.id as String?)
                                    }
                                }
                                .pickerStyle(.menu)
                                .onChange(of: selectedVoiceId) { // Updated onChange
                                    samplePlaybackError = nil
                                }
                            } else if currentEngineIsAvailable {
                                Text(localizationManager.localizedString("tts_no_sample_voices_for_lang") + " (\(currentSampleLanguageCode))")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            }
                        }
                        .padding(.vertical, 12)
                    }
                }

                StyledSectionHeader(title: localizationManager.localizedString("tts_test_playback_header"))

                StyledCard(title: localizationManager.localizedString("tts_test_sample")) {
                    VStack(spacing: 12) {
                        StyledButton(
                            title: isPlayingSample ?
                                localizationManager.localizedString("tts_stop_sample") :
                                localizationManager.localizedString("tts_play_sample"),
                            action: {
                                if isPlayingSample {
                                    ttsManager.stopSample() // This is synchronous
                                    isPlayingSample = false
                                } else {
                                    playSampleAudio()
                                }
                            },
                            icon: isPlayingSample ? "stop.fill" : "play.fill",
                            isPrimary: !isPlayingSample,
                            isDisabled: !currentEngineIsAvailable && !isPlayingSample
                        )

                        if let errorText = samplePlaybackError {
                            Text(errorText)
                                .font(AppTheme.Typography.caption2)
                                .foregroundColor(AppTheme.Colors.secondary)
                        }

                        Divider()
                            .background(Color.white.opacity(0.1))
                            .padding(.horizontal)
                            .padding(.vertical, 8)

                        // 高級 TTS 測試按鈕
                        StyledButton(
                            title: localizationManager.localizedString("tts_advanced_test") != "tts_advanced_test" ?
                                   localizationManager.localizedString("tts_advanced_test") : "高級 TTS 測試",
                            action: {
                                showAdvancedTTSTest = true
                            },
                            icon: "waveform",
                            isPrimary: false
                        )

                        // Kokoro TTS 提示
                        if let kokoroEngine = kokoroEngineHolder {
                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)
                                .padding(.vertical, 8)

                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: kokoroEngineIsAvailable ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                                        .foregroundColor(kokoroEngineIsAvailable ? .green : .orange)

                                    Text(kokoroEngine.engineName) // Use stored engine's name
                                        .font(AppTheme.Typography.headline)
                                        .foregroundColor(AppTheme.Colors.primary)
                                }

                                Text("高質量神經網絡 TTS 引擎，專為 Apple Silicon 優化")
                                    .font(AppTheme.Typography.caption1)
                                    .foregroundColor(AppTheme.Colors.secondary)

                                if !kokoroEngineIsAvailable {
                                    Text("需要完成集成步驟才能使用。請查看 README_KOKORO_INTEGRATION.md 文件了解詳情。")
                                        .font(AppTheme.Typography.caption2)
                                        .foregroundColor(AppTheme.Colors.secondary)
                                        .padding(.top, 4)
                                }
                            }
                            .padding(.vertical, 8)
                        }
                    }
                    .padding(.vertical, 4)
                }

                Spacer()
            }
            .padding(.horizontal)
            .padding(.top, 20)
        }
        .navigationTitle(localizationManager.localizedString("tts_settings_title"))
        .navigationBarTitleDisplayMode(.inline)
        .sheet(isPresented: $showAdvancedTTSTest) {
            TTSTestView()
        }
        .task { // General task for initial setup and Kokoro status
            self.kokoroEngineHolder = ttsManager.availableEngines.first(where: { $0.engineId == "kokoro_tts" })
            if let engine = kokoroEngineHolder {
                self.kokoroEngineIsAvailable = await engine.isAvailable
            }
        }
        .task(id: selectedEngineId) { // Task dependent on selectedEngineId
            print("Selected engine ID changed to: \(selectedEngineId)")
            ttsManager.setCurrentEngine(engineId: selectedEngineId)
            self.currentEngineIsAvailable = await ttsManager.currentEngine.isAvailable
            print("Current engine \(ttsManager.currentEngine.engineName) isAvailable: \(currentEngineIsAvailable)")

            let voices = await ttsManager.currentEngine.getAvailableVoices(forLanguageCode: currentSampleLanguageCode)
            self.availableVoicesForPicker = voices
            print("Available voices for \(currentSampleLanguageCode): \(voices.count)")

            if let firstVoice = voices.first {
                self.selectedVoiceId = firstVoice.id
                print("Selected voice ID set to: \(firstVoice.id)")
            } else {
                self.selectedVoiceId = nil
                print("No voices available for \(currentSampleLanguageCode), selectedVoiceId is nil")
            }
            self.samplePlaybackError = nil
        }
    }

    private func playSampleAudio() {
        samplePlaybackError = nil
        isPlayingSample = true
        // playSample is async, but its completion handler is @Sendable
        // No need to wrap this call in Task { } if playSample handles its own async work correctly.
        ttsManager.playSample(text: currentSampleText, languageCode: currentSampleLanguageCode, voiceIdentifier: selectedVoiceId) { error in
            // Completion handler is called on main thread by TTSManager or should be dispatched
            isPlayingSample = false
            if let err = error {
                samplePlaybackError = localizationManager.localizedString("tts_playback_error_prefix") + " \(err.localizedDescription)"
            }
        }
    }
}

struct TTSSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView { // Added NavigationView for preview
            TTSSettingsView()
                .environmentObject(LocalizationManager.shared) // Ensure preview has the manager
        }
    }
}
