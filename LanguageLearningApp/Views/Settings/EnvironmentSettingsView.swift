import SwiftUI
import Foundation

struct EnvironmentSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedEnvironment: AppEnvironment = AppEnvironment.current
    @State private var useMockData: Bool = UserDefaults.standard.bool(forKey: "use_mock_data")
    @State private var showRestartAlert = false
    @StateObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        NavigationView {
            StyledContainer {
                VStack(spacing: 24) {
                    // Header with icon
                    VStack(spacing: 16) {
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            environmentColor(selectedEnvironment).opacity(0.8),
                                            AppTheme.Colors.primary
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 80, height: 80)
                                .shadow(color: AppTheme.Colors.primary.opacity(0.5), radius: 10, x: 0, y: 5)

                            Image(systemName: "gearshape.2.fill")
                                .font(.system(size: AppTheme.Dimensions.iconSizeLarge - 2))
                                .foregroundColor(AppTheme.Colors.textPrimary)
                        }

                        Text(localizationManager.localizedString(LocalizationKey.environment_settings_title))
                            .font(AppTheme.Typography.title2)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(localizationManager.localizedString(LocalizationKey.environment_settings_subtitle))
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.bottom, 10)

                    // Environment Selection
                    StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.current_environment_section_title))

                    StyledCard {
                        VStack(spacing: 16) {
                            Picker(localizationManager.localizedString(LocalizationKey.environment_picker_label), selection: $selectedEnvironment) {
                                ForEach(AppEnvironment.allCases, id: \.self) { env in
                                    Text(localizationManager.localizedString(env.localizationKey))
                                        .tag(env)
                                }
                            }
                            .pickerStyle(SegmentedPickerStyle())
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .onChange(of: selectedEnvironment) { _ in
                                showRestartAlert = true
                            }

                            // Environment Indicator
                            HStack {
                                Circle()
                                    .fill(environmentColor(selectedEnvironment))
                                    .frame(width: 12, height: 12)

                                Text(String(format: localizationManager.localizedString(LocalizationKey.current_selection_format), localizationManager.localizedString(selectedEnvironment.localizationKey)))
                                    .font(AppTheme.Typography.subheadline)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            }
                            .padding(.bottom, 10)
                            .padding(.horizontal, 16)
                        }
                    }

                    // Development Options
                    if selectedEnvironment == .development {
                        StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.development_options_section_title))

                        StyledCard {
                            StyledToggle(
                                title: localizationManager.localizedString(LocalizationKey.use_mock_data_toggle),
                                isOn: $useMockData,
                                icon: "server.rack"
                            )
                            .onChange(of: useMockData) { newValue in
                                UserDefaults.standard.set(newValue, forKey: "use_mock_data")
                            }
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                        }
                    }

                    // Environment Info
                    StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.environment_info_section_title))

                    StyledCard {
                        VStack(spacing: 0) {
                            HStack {
                                Text(localizationManager.localizedString(LocalizationKey.api_base_url_label))
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                Spacer()

                                Text(selectedEnvironment.baseURL.absoluteString)
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                                    .lineLimit(1)
                                    .truncationMode(.middle)
                            }
                            .padding(16)

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            HStack {
                                Text(localizationManager.localizedString(LocalizationKey.api_timeout_label))
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                Spacer()

                                Text(String(format: localizationManager.localizedString(LocalizationKey.seconds_format), Int(selectedEnvironment.apiTimeout)))
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            }
                            .padding(16)

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            HStack {
                                Text(localizationManager.localizedString(LocalizationKey.verbose_logging_label))
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                Spacer()

                                Text(selectedEnvironment.enableVerboseLogging ? localizationManager.localizedString(LocalizationKey.enabled_status) : localizationManager.localizedString(LocalizationKey.disabled_status))
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            }
                            .padding(16)

                            Divider()
                                .background(Color.white.opacity(0.1))
                                .padding(.horizontal)

                            HStack {
                                Text(localizationManager.localizedString(LocalizationKey.developer_tools_label))
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                Spacer()

                                Text(selectedEnvironment.showDeveloperTools ? localizationManager.localizedString(LocalizationKey.shown_status) : localizationManager.localizedString(LocalizationKey.hidden_status))
                                    .font(AppTheme.Typography.footnote)
                                    .foregroundColor(AppTheme.Colors.textSecondary)
                            }
                            .padding(16)
                        }
                    }

                    Spacer(minLength: 30)

                    // Apply Button
                    StyledButton(
                        title: localizationManager.localizedString(LocalizationKey.apply_settings_button),
                        action: {
                            // 应用环境设置
                            AppEnvironment.current = selectedEnvironment
                            dismiss()
                        },
                        icon: "checkmark.seal.fill"
                    )
                    .padding(.horizontal, 20)
                }
            }
            .navigationTitle(localizationManager.localizedString(LocalizationKey.environment_settings_title))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { dismiss() }) {
                        Text(localizationManager.localizedString(LocalizationKey.close_button))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }
                }
            }
            .alert(isPresented: $showRestartAlert) {
                Alert(
                    title: Text(localizationManager.localizedString(LocalizationKey.switch_environment_alert_title)),
                    message: Text(localizationManager.localizedString(LocalizationKey.switch_environment_alert_message)),
                    primaryButton: .default(Text(localizationManager.localizedString(LocalizationKey.confirm_button))) {
                        // No action, just dismiss
                    },
                    secondaryButton: .cancel(Text(localizationManager.localizedString(LocalizationKey.cancel_button))) {
                        // 恢复选择
                        selectedEnvironment = AppEnvironment.current
                    }
                )
            }
        }
    }

    /// 根據環境獲取對應顏色
    private func environmentColor(_ environment: AppEnvironment) -> Color {
        switch environment {
        case .development:
            return AppTheme.Colors.environmentDevelopment
        case .staging:
            return AppTheme.Colors.environmentStaging
        case .production:
            return AppTheme.Colors.environmentProduction
        }
    }
}

#Preview {
    EnvironmentSettingsView()
}
