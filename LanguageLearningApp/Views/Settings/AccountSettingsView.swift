import SwiftUI

struct AccountSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var userManager: UserManager
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var username = ""
    @State private var email = ""
    @State private var showingChangePassword = false
    @State private var showingDeleteAccount = false
    @State private var isSaving = false
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            StyledContainer {
                VStack(spacing: 24) {
                    // Profile Header
                    VStack(spacing: 20) {
                        // Profile Image
                        AvatarView(
                            initials: String(username.prefix(1).uppercased()),
                            size: 100
                        )

                        Text(username.isEmpty ? localizationManager.localizedString(LocalizationKey.username_title) : username)
                            .font(AppTheme.Typography.title2)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }
                    .padding(.bottom, 10)

                    // Personal Info Section
                    StyledSectionHeader(title: localizationManager.localizedString(LocalizationKey.personal_info))

                    StyledCard {
                        VStack(spacing: 16) {
                            StyledTextField(
                                title: localizationManager.localizedString(LocalizationKey.username_title),
                                text: $username,
                                placeholder: localizationManager.localizedString(LocalizationKey.username_placeholder),
                                icon: "person.fill"
                            )

                            StyledTextField(
                                title: localizationManager.localizedString(LocalizationKey.email_title),
                                text: $email,
                                placeholder: localizationManager.localizedString(LocalizationKey.email_placeholder),
                                icon: "envelope.fill",
                                isDisabled: true,
                                keyboardType: .emailAddress,
                                autocapitalization: .none
                            )
                        }
                        .padding(16)
                    }

                    // Security Section
                    StyledSectionHeader(title: localizationManager.localizedString("security"))

                    StyledCard {
                        Button(action: {
                            showingChangePassword = true
                        }) {
                            HStack {
                                Image(systemName: "lock.fill")
                                    .font(.system(size: 18))
                                    .foregroundColor(AppTheme.Colors.secondary)
                                    .frame(width: 24, height: 24)

                                Text(localizationManager.localizedString(LocalizationKey.change_password))
                                    .font(AppTheme.Typography.body)
                                    .foregroundColor(AppTheme.Colors.textPrimary)

                                Spacer()

                                Image(systemName: "chevron.right")
                                    .font(.system(size: 14))
                                    .foregroundColor(AppTheme.Colors.textTertiary)
                            }
                            .padding(16)
                        }
                    }

                    Spacer(minLength: 30)

                    // Save Button
                    StyledButton(
                        title: localizationManager.localizedString(LocalizationKey.save),
                        action: saveAccountInfo,
                        icon: "checkmark",
                        isLoading: isSaving,
                        isDisabled: isSaving
                    )
                    .padding(.horizontal, 20)

                    // Delete Account Button
                    StyledButton(
                        title: localizationManager.localizedString(LocalizationKey.delete_account),
                        action: { showingDeleteAccount = true },
                        icon: "trash",
                        isPrimary: false,
                        isDestructive: true
                    )
                    .padding(.horizontal, 20)
                    .padding(.top, 10)
                }
            }
            .navigationTitle(localizationManager.localizedString(LocalizationKey.account_settings))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(localizationManager.localizedString(LocalizationKey.account_settings))
                        .font(AppTheme.Typography.title3)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { dismiss() }) {
                        Text(localizationManager.localizedString(LocalizationKey.done))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }
                }
            }
            .sheet(isPresented: $showingChangePassword) {
                ChangePasswordView()
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text(alertTitle),
                    message: Text(alertMessage),
                    dismissButton: .default(Text(localizationManager.localizedString(LocalizationKey.ok)))
                )
            }
            .alert(localizationManager.localizedString(LocalizationKey.delete_account), isPresented: $showingDeleteAccount) {
                Button(localizationManager.localizedString(LocalizationKey.cancel), role: .cancel) { }
                Button(localizationManager.localizedString(LocalizationKey.delete), role: .destructive) {
                    deleteAccount()
                }
            } message: {
                Text(localizationManager.localizedString(LocalizationKey.profile_delete_message))
            }
            .onAppear {
                loadUserInfo()
            }
        }
    }

    private func loadUserInfo() {
        if let user = userManager.currentUser {
            username = user.username
            email = user.email
        }
    }

    private func saveAccountInfo() {
        isSaving = true

        Task {
            do {
                // 调用API更新用户信息
                try await userManager.updateProfile(name: username, email: nil, avatar: nil)

                // 显示成功提示
                DispatchQueue.main.async {
                    self.alertTitle = localizationManager.localizedString(LocalizationKey.update_success)
                    self.alertMessage = localizationManager.localizedString(LocalizationKey.update_success_message)
                    self.showAlert = true
                    self.isSaving = false
                }
            } catch {
                // 显示错误提示
                DispatchQueue.main.async {
                    self.alertTitle = localizationManager.localizedString("update_failed")
                    self.alertMessage = error.localizedDescription
                    self.showAlert = true
                    self.isSaving = false
                }
            }
        }
    }

    private func deleteAccount() {
        Task {
            do {
                // 调用API删除账户
                try await NetworkService.shared.requestNoResponse(.deleteAccount)

                // 然后登出用户
                try await userManager.logoutAsync()

                // 在主线程中关闭视图
                DispatchQueue.main.async {
                    self.dismiss()
                }
            } catch {
                DispatchQueue.main.async {
                    self.alertTitle = localizationManager.localizedString("delete_failed")
                    self.alertMessage = error.localizedDescription
                    self.showAlert = true
                }
            }
        }
    }
}

struct ChangePasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var userManager: UserManager
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var currentPassword = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var isSaving = false
    @State private var showCurrentPassword = false
    @State private var showNewPassword = false
    @State private var showConfirmPassword = false

    var body: some View {
        NavigationView {
            StyledContainer {
                VStack(spacing: 24) {
                    // Lock Icon Header
                    VStack(spacing: 16) {
                        ZStack {
                            Circle()
                                .fill(AppTheme.Colors.primaryGradient)
                                .frame(width: 90, height: 90)
                                .shadow(color: AppTheme.Colors.primary.opacity(0.5), radius: 15, x: 0, y: 5)

                            Image(systemName: "lock.fill")
                                .font(.system(size: 35))
                                .foregroundColor(.white)
                        }

                        Text(localizationManager.localizedString(LocalizationKey.change_password))
                            .font(AppTheme.Typography.title2)
                            .foregroundColor(AppTheme.Colors.textPrimary)

                        Text(localizationManager.localizedString(LocalizationKey.change_password_subtitle))
                            .font(AppTheme.Typography.subheadline)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.bottom, 10)

                    // Current Password
                    StyledTextField(
                        title: localizationManager.localizedString(LocalizationKey.current_password),
                        text: $currentPassword,
                        placeholder: localizationManager.localizedString(LocalizationKey.current_password),
                        icon: "key.fill",
                        isSecure: !showCurrentPassword
                    )
                    .overlay(
                        Button(action: {
                            showCurrentPassword.toggle()
                        }) {
                            Image(systemName: showCurrentPassword ? "eye.slash.fill" : "eye.fill")
                                .foregroundColor(AppTheme.Colors.textTertiary)
                        }
                        .padding(.trailing, 16),
                        alignment: .trailing
                    )
                    .padding(.horizontal, 20)

                    // New Password
                    StyledTextField(
                        title: localizationManager.localizedString(LocalizationKey.new_password),
                        text: $newPassword,
                        placeholder: localizationManager.localizedString(LocalizationKey.new_password),
                        icon: "lock.fill",
                        isSecure: !showNewPassword
                    )
                    .overlay(
                        Button(action: {
                            showNewPassword.toggle()
                        }) {
                            Image(systemName: showNewPassword ? "eye.slash.fill" : "eye.fill")
                                .foregroundColor(AppTheme.Colors.textTertiary)
                        }
                        .padding(.trailing, 16),
                        alignment: .trailing
                    )
                    .padding(.horizontal, 20)

                    // Confirm Password
                    StyledTextField(
                        title: localizationManager.localizedString(LocalizationKey.confirm_new_password),
                        text: $confirmPassword,
                        placeholder: localizationManager.localizedString(LocalizationKey.confirm_new_password),
                        icon: "lock.fill",
                        isSecure: !showConfirmPassword
                    )
                    .overlay(
                        Button(action: {
                            showConfirmPassword.toggle()
                        }) {
                            Image(systemName: showConfirmPassword ? "eye.slash.fill" : "eye.fill")
                                .foregroundColor(AppTheme.Colors.textTertiary)
                        }
                        .padding(.trailing, 16),
                        alignment: .trailing
                    )
                    .padding(.horizontal, 20)

                    // Password Requirements
                    if !newPassword.isEmpty {
                        StyledCard {
                            VStack(alignment: .leading, spacing: 12) {
                                Text(localizationManager.localizedString("password_requirements"))
                                    .font(AppTheme.Typography.headline)
                                    .foregroundColor(AppTheme.Colors.secondary)

                                HStack(spacing: 10) {
                                    Image(systemName: newPassword.count >= 8 ? "checkmark.circle.fill" : "circle")
                                        .foregroundColor(newPassword.count >= 8 ? AppTheme.Colors.accent3 : AppTheme.Colors.textTertiary)
                                        .font(.system(size: 16))

                                    Text(localizationManager.localizedString("min_8_characters"))
                                        .font(AppTheme.Typography.subheadline)
                                        .foregroundColor(AppTheme.Colors.textSecondary)
                                }

                                HStack(spacing: 10) {
                                    Image(systemName: newPassword == confirmPassword ? "checkmark.circle.fill" : "circle")
                                        .foregroundColor(newPassword == confirmPassword ? AppTheme.Colors.accent3 : AppTheme.Colors.textTertiary)
                                        .font(.system(size: 16))

                                    Text(localizationManager.localizedString("passwords_match"))
                                        .font(AppTheme.Typography.subheadline)
                                        .foregroundColor(AppTheme.Colors.textSecondary)
                                }
                            }
                            .padding(16)
                        }
                        .padding(.horizontal, 20)
                    }

                    Spacer(minLength: 30)

                    // Update Button
                    StyledButton(
                        title: localizationManager.localizedString("update_password"),
                        action: changePassword,
                        icon: "checkmark.shield.fill",
                        isLoading: isSaving,
                        isDisabled: isSaving || !isFormValid
                    )
                    .padding(.horizontal, 20)
                }
            }
            .navigationTitle(localizationManager.localizedString(LocalizationKey.change_password))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(localizationManager.localizedString(LocalizationKey.change_password))
                        .font(AppTheme.Typography.title3)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { dismiss() }) {
                        Text(localizationManager.localizedString(LocalizationKey.cancel))
                            .font(AppTheme.Typography.headline)
                            .foregroundColor(AppTheme.Colors.textPrimary)
                    }
                }
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text(alertTitle),
                    message: Text(alertMessage),
                    dismissButton: .default(Text(localizationManager.localizedString(LocalizationKey.ok))) {
                        if alertTitle == localizationManager.localizedString(LocalizationKey.password_changed) {
                            dismiss()
                        }
                    }
                )
            }
        }
    }

    private var isFormValid: Bool {
        !currentPassword.isEmpty &&
        !newPassword.isEmpty &&
        !confirmPassword.isEmpty &&
        newPassword == confirmPassword &&
        newPassword.count >= 8
    }

    private func changePassword() {
        if newPassword != confirmPassword {
            alertTitle = localizationManager.localizedString("passwords_not_match")
            alertMessage = localizationManager.localizedString("passwords_not_match_message")
            showAlert = true
            return
        }

        if newPassword.count < 8 {
            alertTitle = localizationManager.localizedString("password_too_short")
            alertMessage = localizationManager.localizedString("password_min_chars_message")
            showAlert = true
            return
        }

        isSaving = true

        Task {
            do {
                // 调用API更改密码
                try await userManager.changePassword(oldPassword: currentPassword, newPassword: newPassword)

                // 显示成功提示
                DispatchQueue.main.async {
                    self.alertTitle = self.localizationManager.localizedString("password_changed")
                    self.alertMessage = self.localizationManager.localizedString("password_changed_message")
                    self.showAlert = true
                    self.isSaving = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.alertTitle = self.localizationManager.localizedString("password_change_failed")
                    self.alertMessage = error.localizedDescription
                    self.showAlert = true
                    self.isSaving = false
                }
            }
        }
    }
}

#Preview {
    AccountSettingsView()
        .environmentObject(UserManager.shared)
}
