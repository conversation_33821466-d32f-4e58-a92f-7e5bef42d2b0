import SwiftUI
import Foundation

struct LessonListView: View {
    @EnvironmentObject var lessonManager: LessonManager
    @EnvironmentObject var localizationManager: LocalizationManager
    @State private var searchText = ""
    @State private var selectedCategory: Category?
    @State private var selectedDifficulty: Difficulty?
    @State private var selectedLevel: Level?
    @State private var showFavoritesOnly = false
    @State private var showingAddLesson = false
    @State private var isRefreshing = false

    private var filteredLessons: [Lesson] {
        let categoryFiltered = filterByCategory(lessonManager.lessons)
        let difficultyFiltered = filterByDifficulty(categoryFiltered)
        let levelFiltered = filterByLevel(difficultyFiltered)
        let favoritesFiltered = filterByFavorites(levelFiltered)
        return filterBySearch(favoritesFiltered)
    }

    var body: some View {
        StyledContainer {
            VStack(spacing: 24) {
                FilterSection(
                    selectedCategory: $selectedCategory,
                    selectedDifficulty: $selectedDifficulty,
                    selectedLevel: $selectedLevel,
                    showFavoritesOnly: $showFavoritesOnly
                )

                LessonsSection(
                    lessons: filteredLessons,
                    lessonManager: lessonManager
                )
            }
            .searchable(text: $searchText, prompt: "Search lessons")
            .refreshable {
                await refreshLessons()
            }
        }
        .navigationTitle("Lessons")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                AddLessonButton(showingAddLesson: $showingAddLesson)
            }
        }
        .sheet(isPresented: $showingAddLesson) {
            NavigationView {
                LessonEditView(lesson: nil)
            }
        }
        .onChange(of: isRefreshing) { newValue in
            print("Refresh state changed to: \(newValue)")
        }
    }

    private func refreshLessons() async {
        isRefreshing = true
        do {
            await lessonManager.loadLessonsFromAPI(completion: {
                isRefreshing = false
            })
        } catch {
            isRefreshing = false
        }
        if isRefreshing {
            isRefreshing = false
        }
    }

    private func filterByCategory(_ lessons: [Lesson]) -> [Lesson] {
        guard let category = selectedCategory else { return lessons }
        return lessons.filter { $0.category == category }
    }

    private func filterByDifficulty(_ lessons: [Lesson]) -> [Lesson] {
        guard let difficulty = selectedDifficulty else { return lessons }
        return lessons.filter { $0.difficulty == difficulty }
    }

    private func filterByLevel(_ lessons: [Lesson]) -> [Lesson] {
        guard let level = selectedLevel else { return lessons }
        return lessons.filter { $0.level == level }
    }

    private func filterByFavorites(_ lessons: [Lesson]) -> [Lesson] {
        guard showFavoritesOnly else { return lessons }
        return lessons.filter { lessonManager.isFavorite(lessonId: $0.id) }
    }

    private func filterBySearch(_ lessons: [Lesson]) -> [Lesson] {
        guard !searchText.isEmpty else { return lessons }
        return lessons.filter { lesson in
            let searchContent = "\(lesson.title) \(lesson.description) \(lesson.tags.joined(separator: " "))"
            return searchContent.localizedCaseInsensitiveContains(searchText)
        }
    }
}

// MARK: - Filter Section
private struct FilterSection: View {
    @Binding var selectedCategory: Category?
    @Binding var selectedDifficulty: Difficulty?
    @Binding var selectedLevel: Level?
    @Binding var showFavoritesOnly: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            StyledSectionHeader(title: "Filters")

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    CategoryFilterButton(selectedCategory: $selectedCategory)
                    DifficultyFilterButton(selectedDifficulty: $selectedDifficulty)
                    LevelFilterButton(selectedLevel: $selectedLevel)
                    FavoritesFilterButton(showFavoritesOnly: $showFavoritesOnly)
                }
                .padding(.horizontal, 4)
            }
        }
    }
}

// MARK: - Filter Buttons
private struct CategoryFilterButton: View {
    @Binding var selectedCategory: Category?

    var body: some View {
        Menu {
            Button("All Categories") {
                selectedCategory = nil
            }
            ForEach(Category.allCases, id: \.self) { category in
                Button(category.rawValue.capitalized) {
                    selectedCategory = category
                }
            }
        } label: {
            FilterButton(
                title: selectedCategory?.rawValue.capitalized ?? "Category",
                icon: "folder"
            )
        }
    }
}

private struct DifficultyFilterButton: View {
    @Binding var selectedDifficulty: Difficulty?

    var body: some View {
        Menu {
            Button("All Difficulties") {
                selectedDifficulty = nil
            }
            ForEach(Difficulty.allCases, id: \.self) { difficulty in
                Button(difficulty.rawValue.capitalized) {
                    selectedDifficulty = difficulty
                }
            }
        } label: {
            FilterButton(
                title: selectedDifficulty?.rawValue.capitalized ?? "Difficulty",
                icon: "chart.bar"
            )
        }
    }
}

private struct LevelFilterButton: View {
    @Binding var selectedLevel: Level?

    var body: some View {
        Menu {
            Button("All Levels") {
                selectedLevel = nil
            }
            ForEach(Level.allCases, id: \.self) { level in
                Button(level.rawValue.capitalized) {
                    selectedLevel = level
                }
            }
        } label: {
            FilterButton(
                title: selectedLevel?.rawValue.capitalized ?? "Level",
                icon: "stairs"
            )
        }
    }
}

private struct FavoritesFilterButton: View {
    @Binding var showFavoritesOnly: Bool

    var body: some View {
        Button(action: { showFavoritesOnly.toggle() }) {
            FilterButton(
                title: "Favorites",
                icon: "star",
                isSelected: showFavoritesOnly
            )
        }
    }
}

// MARK: - Lessons Section
private struct LessonsSection: View {
    let lessons: [Lesson]
    let lessonManager: LessonManager

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            StyledSectionHeader(
                title: "Lessons",
                subtitle: "\(lessons.count) items"
            )

            if lessons.isEmpty {
                EmptyLessonsView()
            } else {
                LessonList(lessons: lessons, lessonManager: lessonManager)
            }
        }
    }
}

// MARK: - Empty State
private struct EmptyLessonsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "book.closed")
                .font(.system(size: 50))
                .foregroundColor(AppTheme.Colors.textSecondary)

            Text("No lessons found")
                .font(AppTheme.Typography.headline)
                .foregroundColor(AppTheme.Colors.textSecondary)

            Text("Try adjusting your filters or search criteria")
                .font(AppTheme.Typography.subheadline)
                .foregroundColor(AppTheme.Colors.textTertiary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }
}

// MARK: - Lesson List
private struct LessonList: View {
    let lessons: [Lesson]
    let lessonManager: LessonManager

    var body: some View {
        LazyVStack(spacing: 16) {
            ForEach(lessons) { lesson in
                NavigationLink(destination: EnhancedLessonDetailView(lesson: lesson)) {
                    LessonRow(
                        lesson: lesson,
                        isFavorite: lessonManager.isFavorite(lessonId: lesson.id),
                        progress: calculateProgress(for: lesson),
                        onFavoriteToggle: {
                            lessonManager.toggleFavorite(lessonId: lesson.id)
                        }
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.bottom, 20)
    }

    private func calculateProgress(for lesson: Lesson) -> Double {
        guard let progress = lessonManager.getProgress(for: lesson.id) else { return 0 }
        return progress.progress
    }
}

// MARK: - Add Lesson Button
private struct AddLessonButton: View {
    @Binding var showingAddLesson: Bool

    var body: some View {
        Button(action: { showingAddLesson = true }) {
            Image(systemName: "plus")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(AppTheme.Colors.textPrimary)
                .frame(width: 36, height: 36)
                .background(AppTheme.Colors.card)
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        }
    }
}

struct FilterButton: View {
    let title: String
    let icon: String
    var isSelected: Bool = false

    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 14))
            Text(title)
                .font(AppTheme.Typography.footnote)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            isSelected ?
                AppTheme.Colors.primary :
                AppTheme.Colors.card
        )
        .foregroundColor(isSelected ? .white : AppTheme.Colors.textSecondary)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                .stroke(
                    isSelected ?
                        AppTheme.Colors.primary :
                        Color.white.opacity(0.1),
                    lineWidth: 1
                )
        )
    }
}

struct LessonRow: View {
    let lesson: Lesson
    let isFavorite: Bool
    let progress: Double
    let onFavoriteToggle: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                // Category icon
                ZStack {
                    Circle()
                        .fill(categoryColor)
                        .frame(width: 40, height: 40)

                    Image(systemName: categoryIcon)
                        .font(.system(size: 18))
                        .foregroundColor(.white)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(lesson.title)
                        .font(AppTheme.Typography.headline)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    Text(lesson.description)
                        .font(AppTheme.Typography.footnote)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .lineLimit(2)
                }

                Spacer()

                Button(action: onFavoriteToggle) {
                    Image(systemName: isFavorite ? "star.fill" : "star")
                        .font(.system(size: 20))
                        .foregroundColor(isFavorite ? AppTheme.Colors.accent2 : AppTheme.Colors.textTertiary)
                }
            }

            VStack(spacing: 8) {
                HStack {
                    // Difficulty
                    Text(lesson.difficulty.rawValue.capitalized)
                        .font(AppTheme.Typography.caption1)
                        .foregroundColor(difficultyColor)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(difficultyColor.opacity(0.1))
                        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)

                    // Level
                    Text(lesson.level.rawValue.capitalized)
                        .font(AppTheme.Typography.caption1)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(AppTheme.Colors.background)
                        .cornerRadius(AppTheme.Dimensions.cornerRadiusSmall)

                    Spacer()

                    // Duration
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.system(size: AppTheme.Dimensions.iconSizeSmall - 4))
                        Text("\(lesson.duration) min")
                    }
                    .font(AppTheme.Typography.caption1)
                    .foregroundColor(AppTheme.Colors.textSecondary)
                }

                // Progress Bar
                if progress > 0 {
                    VStack(alignment: .leading, spacing: 4) {
                        StyledProgressBar(progress: progress)

                        Text("\(Int(progress * 100))% completed")
                            .font(AppTheme.Typography.caption1)
                            .foregroundColor(AppTheme.Colors.textSecondary)
                    }
                }
            }
        }
        .padding(16)
        .background(AppTheme.Colors.card)
        .cornerRadius(AppTheme.Dimensions.cornerRadiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusMedium)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }

    private var difficultyColor: Color {
        switch lesson.difficulty {
        case .easy: return AppTheme.Colors.success
        case .medium: return AppTheme.Colors.warning
        case .hard: return AppTheme.Colors.error
        }
    }

    private var categoryColor: Color {
        switch lesson.category {
        case .vocabulary: return AppTheme.Colors.primary
        case .grammar: return AppTheme.Colors.secondary
        case .listening: return AppTheme.Colors.accent1
        case .speaking: return AppTheme.Colors.accent3
        case .reading: return AppTheme.Colors.accent2
        case .writing: return AppTheme.Colors.writing
        case .uncategorized: return AppTheme.Colors.uncategorized
        }
    }

    private var categoryIcon: String {
        switch lesson.category {
        case .vocabulary: return "textformat.abc"
        case .grammar: return "text.book.closed"
        case .listening: return "ear"
        case .speaking: return "mic"
        case .reading: return "book"
        case .writing: return "pencil"
        case .uncategorized: return "questionmark.circle"
        }
    }
}

#Preview {
    NavigationView {
        LessonListView()
    }
    .environmentObject(LessonManager.shared)
    .environmentObject(LocalizationManager.shared)
}