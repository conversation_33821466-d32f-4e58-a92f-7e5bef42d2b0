import SwiftUI
import Foundation

struct RegisterView: View {
    @EnvironmentObject private var userManager: UserManager
    @EnvironmentObject private var errorManager: ErrorManager
    @StateObject private var localizationManager = LocalizationManager.shared
    @Environment(\.presentationMode) var presentationMode
    @State private var username = ""
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    // 移除 errorMessage，使用统一的 ErrorManager
    @State private var isRegistering = false
    @State private var isAnimating = false

    var body: some View {
        StyledContainer {
            VStack(spacing: 24) {
                // Title Section
                VStack(spacing: 16) {
                    ZStack {
                        Circle()
                            .fill(AppTheme.Colors.primaryGradient)
                            .frame(width: 90, height: 90)
                            .shadow(color: AppTheme.Colors.primary.opacity(0.5), radius: 15, x: 0, y: 5)

                        Image(systemName: "person.badge.plus")
                            .font(.system(size: 35))
                            .foregroundColor(.white)
                    }

                    Text(localizationManager.localizedString(LocalizationKey.register))
                        .font(AppTheme.Typography.title1)
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    Text(localizationManager.localizedString(LocalizationKey.create_account_message))
                        .font(AppTheme.Typography.body)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 20)
                .padding(.bottom, 10)
                .opacity(isAnimating ? 1 : 0)
                .offset(y: isAnimating ? 0 : 20)

                // Registration Form
                StyledFormSection(title: "") {
                    VStack(spacing: 20) {
                        StyledTextField(
                            title: localizationManager.localizedString(LocalizationKey.username),
                            text: $username,
                            placeholder: localizationManager.localizedString(LocalizationKey.username_placeholder),
                            icon: "person.fill"
                        )

                        StyledTextField(
                            title: localizationManager.localizedString(LocalizationKey.email),
                            text: $email,
                            placeholder: localizationManager.localizedString(LocalizationKey.email_placeholder),
                            icon: "envelope.fill",
                            keyboardType: .emailAddress
                        )

                        StyledTextField(
                            title: localizationManager.localizedString(LocalizationKey.password),
                            text: $password,
                            placeholder: localizationManager.localizedString(LocalizationKey.password_placeholder),
                            icon: "lock.fill",
                            isSecure: true
                        )

                        StyledTextField(
                            title: localizationManager.localizedString(LocalizationKey.confirm_password),
                            text: $confirmPassword,
                            placeholder: localizationManager.localizedString(LocalizationKey.confirm_password_placeholder),
                            icon: "lock.shield.fill",
                            isSecure: true
                        )
                    }
                    .padding(20)
                }
                .opacity(isAnimating ? 1 : 0)
                .offset(y: isAnimating ? 0 : 20)

                // 移除自定义错误显示，使用统一的 ErrorManager

                // Register button
                StyledButton(
                    title: localizationManager.localizedString(LocalizationKey.register),
                    action: register,
                    icon: "checkmark.circle.fill",
                    isLoading: isRegistering
                )
                .disabled(isRegistering || username.isEmpty || email.isEmpty || password.isEmpty || confirmPassword.isEmpty)
                .padding(.horizontal, 20)
                .padding(.top, 10)
                .opacity(isAnimating ? 1 : 0)
                .offset(y: isAnimating ? 0 : 20)

                // Cancel button
                Button(action: { presentationMode.wrappedValue.dismiss() }) {
                    Text(localizationManager.localizedString(LocalizationKey.cancel))
                        .font(AppTheme.Typography.body)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                }
                .padding(.top, 16)
                .padding(.bottom, 20)
                .opacity(isAnimating ? 1 : 0)
                .offset(y: isAnimating ? 0 : 20)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 20)
            .onAppear {
                withAnimation(.easeOut(duration: 0.8)) {
                    isAnimating = true
                }
            }
            .withErrorHandling() // 添加统一错误处理
        }
        .frame(minWidth: 400, minHeight: 500)
    }

    private func register() {
        guard !username.isEmpty && !email.isEmpty && !password.isEmpty else {
            errorManager.showError(.customError(localizationManager.localizedString(LocalizationKey.fill_all_fields)))
            return
        }

        guard password == confirmPassword else {
            errorManager.showError(.customError(localizationManager.localizedString(LocalizationKey.passwords_not_match)))
            return
        }

        isRegistering = true

        Task {
            do {
                try await userManager.register(username: username, email: email, password: password)
                presentationMode.wrappedValue.dismiss()
            } catch {
                await MainActor.run {
                    errorManager.showError(.customError(error.localizedDescription))
                    isRegistering = false
                }
            }
        }
    }
}
