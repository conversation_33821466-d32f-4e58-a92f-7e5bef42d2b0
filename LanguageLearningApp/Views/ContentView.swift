import SwiftUI
import Combine

// Import for DailyPracticeDashboardView and ErrorManager
import Foundation

struct ContentView: View {
    // Observe UserManager for login state
    @ObservedObject private var userManager = UserManager.shared

    var body: some View {
        Group {
            if userManager.isLoggedIn {
                #if os(iOS)
                // 使用我们的主标签视图
                MainTabView()
                #else
                // This is a placeholder implementation that will be replaced with the actual implementation
                // once the necessary imports and dependencies are resolved
                NavigationView {
                    List {
                        NavigationLink(destination: DailyPracticeDashboardView()) {
                            Label("Daily Practice", systemImage: "square.stack.fill")
                        }

                        NavigationLink(destination: Text("Language Assessment")) {
                            Label("Language Assessment", systemImage: "chart.bar.fill")
                        }

                        NavigationLink(destination: Text("Lessons")) {
                            Label("Lessons", systemImage: "book.fill")
                        }

                        NavigationLink(destination: Text("Practice")) {
                            Label("Practice", systemImage: "pencil.and.outline")
                        }

                        NavigationLink(destination: Text("Achievements")) {
                            Label("Achievements", systemImage: "star.fill")
                        }

                        NavigationLink(destination: Text("Profile")) {
                            Label("Profile", systemImage: "person.fill")
                        }
                    }
                    .listStyle(SidebarListStyle())
                    .frame(minWidth: 200)
                }
                #endif
            } else {
                LoginView()
                    .environmentObject(userManager)
                    .environmentObject(ErrorManager.shared)
            }
        }
    }
}

#Preview {
    // In a real app, we would inject the actual environment objects
    ContentView()
}

// This is a placeholder for the helper views that will be implemented later