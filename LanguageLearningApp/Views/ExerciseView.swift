import SwiftUI
import AVFoundation

struct ExerciseView: View {
    let exercise: Exercise
    @State private var userAnswer = ""
    @State private var showingFeedback = false
    @State private var isCorrect = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 问题
            Text(exercise.question)
                .font(.title2)
                .bold()
            
            // 选项
            if exercise.type == .multipleChoice {
                ForEach(exercise.options, id: \.self) { option in
                    Button(action: {
                        checkAnswer(option)
                    }) {
                        Text(option)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(10)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            } else if exercise.type == .fillInTheBlank {
                TextField("输入答案", text: $userAnswer)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                Button("提交") {
                    checkAnswer(userAnswer)
                }
                .buttonStyle(.borderedProminent)
            } else {
                TextField("输入翻译", text: $userAnswer)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                Button("提交") {
                    checkAnswer(userAnswer)
                }
                .buttonStyle(.borderedProminent)
            }
            
            // 反馈视图
            if showingFeedback {
                VStack {
                    Image(systemName: isCorrect ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .font(.largeTitle)
                        .foregroundColor(isCorrect ? .green : .red)
                    
                    Text(isCorrect ? "回答正确！" : "回答错误")
                        .font(.headline)
                    
                    Text(exercise.explanation ?? "")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(15)
                .shadow(radius: 5)
            }
        }
        .padding()
    }
    
    private func checkAnswer(_ answer: String) {
        isCorrect = answer.lowercased() == exercise.correctAnswer.lowercased()
        showingFeedback = true
    }
}

#Preview {
    ExerciseView(exercise: Exercise(
        id: UUID(),
        type: .multipleChoice,
        question: "选择正确的单词含义",
        options: ["苹果", "香蕉", "橙子", "葡萄"],
        correctAnswer: "苹果",
        explanation: "Apple 在中文中的意思是苹果。"
    ))
}