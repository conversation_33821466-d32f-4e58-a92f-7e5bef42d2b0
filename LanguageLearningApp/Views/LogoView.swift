import Foundation
import SwiftUI

struct LogoView: View {
    @State private var isAnimating = false
    @State private var globeOffset: CGSize = .zero

    var body: some View {
        ZStack {
            // Globe and circle
            ZStack {
                Circle()
                    .fill(AppTheme.Colors.primaryGradient)
                    .frame(width: 100, height: 100)
                    .glowEffect(color: AppTheme.Colors.primary)
                    .rotationEffect(.degrees(isAnimating ? 360 : 0))
                    .scaleEffect(isAnimating ? 1 : 0.8)
                    .animation(.spring(response: 0.6, dampingFraction: 0.6), value: isAnimating)
                    .offset(globeOffset)
                    .scaleEffect(
                        1 + abs(globeOffset.width) * 0.002 + abs(globeOffset.height) * 0.002
                    )
                    .animation(.spring(response: 0.4, dampingFraction: 0.6), value: globeOffset)

                Image(systemName: "globe")
                    .font(.system(size: 40))
                    .foregroundColor(.white)
                    .rotationEffect(.degrees(isAnimating ? 360 : 0))
                    .animation(.spring(response: 0.8, dampingFraction: 0.6), value: isAnimating)
                    .offset(globeOffset)
                    .gesture(
                        DragGesture()
                            .onChanged { gesture in
                                let translation = gesture.translation
                                globeOffset = CGSize(
                                    width: translation.width,
                                    height: translation.height
                                )
                            }
                            .onEnded { _ in
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
                                    globeOffset = .zero
                                }
                            }
                    )
            }
        }
    }
}
