import Foundation

/// 课程难度
public enum LessonDifficulty: String, Codable, CaseIterable {
    case easy = "easy"
    case medium = "medium"
    case hard = "hard"
}

/// 课程级别
public enum LessonLevel: String, Codable, CaseIterable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
}

/// 课程类别
public enum LessonCategory: String, Codable, CaseIterable {
    case vocabulary = "vocabulary"
    case grammar = "grammar"
    case listening = "listening"
    case speaking = "speaking"
    case reading = "reading"
    case writing = "writing"
    case uncategorized = "uncategorized"
}

// MARK: - Type Aliases for Backward Compatibility
public typealias Level = LessonLevel
public typealias Difficulty = LessonDifficulty
public typealias Category = LessonCategory 