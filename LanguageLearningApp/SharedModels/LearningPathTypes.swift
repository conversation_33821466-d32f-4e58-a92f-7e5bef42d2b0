import Foundation

/// 学习路径状态
public enum LearningPathStatus: String, Codable {
    case notStarted = "not_started"
    case inProgress = "in_progress"
    case completed = "completed"
    case locked = "locked"
}

/// 学习路径课程
public struct LearningPathLesson: Codable, Identifiable {
    /// 课程ID
    public let id: String
    /// 课程标题
    public let title: String
    /// 课程描述
    public let description: String
    /// 课程级别
    public let level: LessonLevel
    /// 课程时长（分钟）
    public let duration: Int
    /// 课程积分
    public let points: Int
    /// 课程状态
    public let status: LearningPathStatus
    /// 完成时间
    public let completedAt: Date?
    /// 进度
    public let progress: Double
    
    /// 初始化方法
    public init(
        id: String,
        title: String,
        description: String,
        level: LessonLevel,
        duration: Int,
        points: Int,
        status: LearningPathStatus = .notStarted,
        completedAt: Date? = nil,
        progress: Double = 0
    ) {
        self.id = id
        self.title = title
        self.description = description
        self.level = level
        self.duration = duration
        self.points = points
        self.status = status
        self.completedAt = completedAt
        self.progress = progress
    }
}

/// 学习路径
public struct LearningPath: Codable, Identifiable {
    /// 路径ID
    public let id: String
    /// 路径标题
    public let title: String
    /// 路径描述
    public let description: String
    /// 路径级别
    public let level: LessonLevel
    /// 路径时长（分钟）
    public let duration: Int
    /// 路径积分
    public let points: Int
    /// 路径课程
    public let lessons: [LearningPathLesson]
    /// 路径状态
    public let status: LearningPathStatus
    /// 完成时间
    public let completedAt: Date?
    /// 进度
    public let progress: Double
    
    /// 初始化方法
    public init(
        id: String,
        title: String,
        description: String,
        level: LessonLevel,
        duration: Int,
        points: Int,
        lessons: [LearningPathLesson],
        status: LearningPathStatus = .notStarted,
        completedAt: Date? = nil,
        progress: Double = 0
    ) {
        self.id = id
        self.title = title
        self.description = description
        self.level = level
        self.duration = duration
        self.points = points
        self.lessons = lessons
        self.status = status
        self.completedAt = completedAt
        self.progress = progress
    }
}

/// 课程模型 (共享版本)
public struct SharedLesson: Identifiable, Codable {
    /// 课程ID
    public let id: String
    /// 标题
    public let title: String
    /// 描述
    public let description: String
    /// 级别
    public let level: LessonLevel
    /// 时长（分钟）
    public let durationMinutes: Int
    /// 是否已完成
    public var isCompleted: Bool
    /// 完成日期
    public var completedDate: Date?

    public init(id: String, title: String, description: String, level: LessonLevel, durationMinutes: Int, isCompleted: Bool, completedDate: Date? = nil) {
        self.id = id
        self.title = title
        self.description = description
        self.level = level
        self.durationMinutes = durationMinutes
        self.isCompleted = isCompleted
        self.completedDate = completedDate
    }
}
