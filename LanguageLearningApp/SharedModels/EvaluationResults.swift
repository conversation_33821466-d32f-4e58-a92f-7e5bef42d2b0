import Foundation

/// 表现级别
public enum PerformanceLevel: String, Codable {
    /// 优秀
    case excellent = "excellent"
    /// 良好
    case good = "good"
    /// 满意
    case satisfactory = "satisfactory"
    /// 需要改进
    case needsImprovement = "needs_improvement"

    /// 获取本地化的级别名称
    public func localizedName() -> String {
        switch self {
        case .excellent:
            return "优秀"
        case .good:
            return "良好"
        case .satisfactory:
            return "满意"
        case .needsImprovement:
            return "需要改进"
        }
    }

    /// 获取级别对应的颜色代码
    public func colorCode() -> String {
        switch self {
        case .excellent:
            return "4CAF50" // 绿色
        case .good:
            return "8BC34A" // 浅绿色
        case .satisfactory:
            return "FFC107" // 黄色
        case .needsImprovement:
            return "FF5722" // 橙红色
        }
    }
}

/// 评估部分得分
public struct EvaluationSectionScore: Codable {
    /// 技能名称
    public let skill: String
    /// 得分
    public let score: Int
    /// 最高分
    public let maxScore: Int
    /// 部分标题
    public let sectionTitle: String?
    /// 弱点列表
    public let weaknesses: [String]?
    /// 百分比
    public var percentage: Double {
        return Double(score) / Double(maxScore) * 100.0
    }

    /// 表现级别
    public var performanceLevel: PerformanceLevel {
        let percentage = self.percentage

        if percentage >= 90 {
            return .excellent
        } else if percentage >= 75 {
            return .good
        } else if percentage >= 60 {
            return .satisfactory
        } else {
            return .needsImprovement
        }
    }

    /// 本地化技能名称
    public func localizedSkillName() -> String {
        if let title = sectionTitle {
            return title
        }

        switch skill {
        case "listening":
            return "听力"
        case "speaking":
            return "口语"
        case "reading":
            return "阅读"
        case "writing":
            return "写作"
        case "vocabulary":
            return "词汇"
        case "grammar":
            return "语法"
        default:
            return skill
        }
    }

    public init(skill: String, score: Int, maxScore: Int, sectionTitle: String? = nil, weaknesses: [String]? = nil) {
        self.skill = skill
        self.score = score
        self.maxScore = maxScore
        self.sectionTitle = sectionTitle
        self.weaknesses = weaknesses
    }
}

// 个性化学习路径已移至 LearningPathTypes.swift

/// 评估结果模型
public struct EvaluationResult: Identifiable {
    /// 结果ID
    public let id: UUID
    /// 评估ID
    public let evaluationID: UUID
    /// 用户ID
    public let userID: UUID
    /// 总分
    public let overallScore: Int
    /// 最高分
    public let maxScore: Int
    /// 是否通过
    public let isPassed: Bool
    /// 级别
    public let level: String
    /// 反馈
    public let feedback: String
    /// 建议
    public let recommendations: [String]
    /// 部分得分
    public let sectionScores: [EvaluationSectionScore]
    /// 生成的学习路径（如果有）
    // 注意：这里使用Any类型暂时替代PersonalizedLearningPath，避免循环引用问题
    // 实际使用时需要进行类型转换
    public let learningPath: Any?
    /// 完成时间
    public let completedAt: Date
    /// 创建时间
    public let createdAt: Date

    public init(id: UUID, evaluationID: UUID, userID: UUID, overallScore: Int, maxScore: Int = 100, isPassed: Bool, level: String, feedback: String, recommendations: [String], sectionScores: [EvaluationSectionScore], learningPath: Any? = nil, completedAt: Date, createdAt: Date) {
        self.id = id
        self.evaluationID = evaluationID
        self.userID = userID
        self.overallScore = overallScore
        self.maxScore = maxScore
        self.isPassed = isPassed
        self.level = level
        self.feedback = feedback
        self.recommendations = recommendations
        self.sectionScores = sectionScores
        self.learningPath = learningPath
        self.completedAt = completedAt
        self.createdAt = createdAt
    }

    /// 计算得分百分比
    public var scorePercentage: Double {
        return Double(overallScore) / Double(maxScore) * 100.0
    }

    /// 获取总体表现级别
    public var overallPerformanceLevel: PerformanceLevel {
        let percentage = scorePercentage

        if percentage >= 90 {
            return .excellent
        } else if percentage >= 75 {
            return .good
        } else if percentage >= 60 {
            return .satisfactory
        } else {
            return .needsImprovement
        }
    }

    /// 获取本地化的级别名称
    public func localizedLevelName() -> String {
        switch level.lowercased() {
        case "beginner":
            return "初级"
        case "elementary":
            return "基础"
        case "intermediate":
            return "中级"
        case "upper_intermediate":
            return "中高级"
        case "advanced":
            return "高级"
        case "proficient":
            return "精通"
        default:
            return level
        }
    }

    /// 获取最高分的部分
    public var highestScoringSection: EvaluationSectionScore? {
        return sectionScores.max { $0.score < $1.score }
    }

    /// 获取最低分的部分
    public var lowestScoringSection: EvaluationSectionScore? {
        return sectionScores.min { $0.score < $1.score }
    }

    /// 获取强项技能
    public var strengths: [EvaluationSectionScore] {
        return sectionScores.filter { $0.performanceLevel == .excellent || $0.performanceLevel == .good }
    }

    /// 获取弱项技能
    public var weaknesses: [EvaluationSectionScore] {
        return sectionScores.filter { $0.performanceLevel == .needsImprovement }
    }

    /// 获取需要改进的技能
    public var areasToImprove: [String] {
        return weaknesses.map { $0.localizedSkillName() }
    }

    /// 获取评估完成后的时间描述
    public func completedTimeDescription() -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter.localizedString(for: completedAt, relativeTo: Date())
    }
}

// MARK: - Codable Conformance
extension EvaluationResult: Codable {
    private enum CodingKeys: String, CodingKey {
        case id, evaluationID, userID, overallScore, maxScore, isPassed, level, feedback
        case recommendations, sectionScores, learningPath, completedAt, createdAt
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        evaluationID = try container.decode(UUID.self, forKey: .evaluationID)
        userID = try container.decode(UUID.self, forKey: .userID)
        overallScore = try container.decode(Int.self, forKey: .overallScore)
        maxScore = try container.decodeIfPresent(Int.self, forKey: .maxScore) ?? 100
        isPassed = try container.decode(Bool.self, forKey: .isPassed)
        level = try container.decode(String.self, forKey: .level)
        feedback = try container.decode(String.self, forKey: .feedback)
        recommendations = try container.decode([String].self, forKey: .recommendations)
        sectionScores = try container.decode([EvaluationSectionScore].self, forKey: .sectionScores)
        // 跳过learningPath的解码，因为它是Any类型
        learningPath = nil
        completedAt = try container.decode(Date.self, forKey: .completedAt)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(evaluationID, forKey: .evaluationID)
        try container.encode(userID, forKey: .userID)
        try container.encode(overallScore, forKey: .overallScore)
        try container.encode(maxScore, forKey: .maxScore)
        try container.encode(isPassed, forKey: .isPassed)
        try container.encode(level, forKey: .level)
        try container.encode(feedback, forKey: .feedback)
        try container.encode(recommendations, forKey: .recommendations)
        try container.encode(sectionScores, forKey: .sectionScores)
        // 跳过learningPath的编码，因为它是Any类型
        try container.encode(completedAt, forKey: .completedAt)
        try container.encode(createdAt, forKey: .createdAt)
    }
}

/// 评估答案提交响应
public struct EvaluationSubmitAnswerResponse: Codable {
    public let isCorrect: Bool
    public let explanation: String

    public init(isCorrect: Bool, explanation: String) {
        self.isCorrect = isCorrect
        self.explanation = explanation
    }
}

// 个性化学习状态已移至 PersonalizedLearningPath.swift

/// 评估结果响应
public struct EvaluationResultResponse: Codable {
    /// 结果数据
    public let data: EvaluationResult

    public init(data: EvaluationResult) {
        self.data = data
    }
}

/// 评估历史响应
public struct EvaluationHistoryResponse: Codable {
    /// 结果数据列表
    public let data: [EvaluationResult]

    public init(data: [EvaluationResult]) {
        self.data = data
    }
}
