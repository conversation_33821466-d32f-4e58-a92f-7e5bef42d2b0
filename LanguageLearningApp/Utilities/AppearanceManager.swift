import SwiftUI
import UIKit

/// Manages the appearance of UI elements throughout the app
struct AppearanceManager {

    /// Configure the appearance of the app's UI elements
    static func configureAppearance() {
        configureNavigationBarAppearance()
        configureTabBarAppearance()
    }

    /// Configure the appearance of navigation bars
    private static func configureNavigationBarAppearance() {
        let appearance = UINavigationBarAppearance()

        // Configure the background
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor(AppTheme.Colors.backgroundSecondary)

        // Add a subtle gradient overlay
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor(AppTheme.Colors.card).withAlphaComponent(0.8).cgColor,
            UIColor(AppTheme.Colors.backgroundSecondary).cgColor
        ]
        gradientLayer.locations = [0.0, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)

        // Create an image from the gradient layer
        UIGraphicsBeginImageContextWithOptions(CGSize(width: 1, height: 88), false, 0.0)
        if let context = UIGraphicsGetCurrentContext() {
            gradientLayer.frame = CGRect(x: 0, y: 0, width: 1, height: 88)
            gradientLayer.render(in: context)
            let gradientImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()

            appearance.backgroundImage = gradientImage
        }

        // Add a subtle bottom border
        appearance.shadowColor = UIColor(white: 1.0, alpha: 0.1)

        // Configure text attributes
        appearance.titleTextAttributes = [
            .foregroundColor: UIColor(AppTheme.Colors.textPrimary),
            .font: UIFont.systemFont(ofSize: 17, weight: .semibold)
        ]

        appearance.largeTitleTextAttributes = [
            .foregroundColor: UIColor(AppTheme.Colors.textPrimary),
            .font: UIFont.systemFont(ofSize: 28, weight: .bold)
        ]

        // Configure button appearance
        let buttonAppearance = UIBarButtonItemAppearance()
        buttonAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor(AppTheme.Colors.primary),
            .font: UIFont.systemFont(ofSize: 16, weight: .semibold)
        ]

        appearance.buttonAppearance = buttonAppearance
        appearance.backButtonAppearance = buttonAppearance

        // Apply the appearance
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance

        // Set the tint color for interactive elements
        UINavigationBar.appearance().tintColor = UIColor(AppTheme.Colors.primary)
    }

    /// Configure the appearance of tab bars
    private static func configureTabBarAppearance() {
        let appearance = UITabBarAppearance()

        // Configure the background
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor(AppTheme.Colors.backgroundSecondary)

        // Add a subtle gradient overlay
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor(AppTheme.Colors.backgroundSecondary).cgColor,
            UIColor(AppTheme.Colors.card).withAlphaComponent(0.8).cgColor
        ]
        gradientLayer.locations = [0.0, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)

        // Create an image from the gradient layer
        UIGraphicsBeginImageContextWithOptions(CGSize(width: 1, height: 49), false, 0.0)
        if let context = UIGraphicsGetCurrentContext() {
            gradientLayer.frame = CGRect(x: 0, y: 0, width: 1, height: 49)
            gradientLayer.render(in: context)
            let gradientImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()

            appearance.backgroundImage = gradientImage
        }

        // Add a subtle top border
        appearance.shadowColor = UIColor(white: 1.0, alpha: 0.1)

        // Configure item appearance
        let itemAppearance = UITabBarItemAppearance()

        // Normal state
        itemAppearance.normal.iconColor = UIColor(AppTheme.Colors.textSecondary)
        itemAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor(AppTheme.Colors.textSecondary),
            .font: UIFont.systemFont(ofSize: 10, weight: .medium)
        ]

        // Selected state
        itemAppearance.selected.iconColor = UIColor(AppTheme.Colors.primary)
        itemAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor(AppTheme.Colors.primary),
            .font: UIFont.systemFont(ofSize: 10, weight: .semibold)
        ]

        appearance.stackedLayoutAppearance = itemAppearance
        appearance.inlineLayoutAppearance = itemAppearance
        appearance.compactInlineLayoutAppearance = itemAppearance

        // Apply the appearance
        UITabBar.appearance().standardAppearance = appearance
        if #available(iOS 15.0, *) {
            UITabBar.appearance().scrollEdgeAppearance = appearance
        }

        // Set the tint color for interactive elements
        UITabBar.appearance().tintColor = UIColor(AppTheme.Colors.primary)
    }
}
