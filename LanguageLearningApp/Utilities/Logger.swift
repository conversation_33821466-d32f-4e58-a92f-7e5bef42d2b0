import Foundation
import SwiftyBeaver

/// 提供应用程序日志功能的工具类
public final class Logger {
    
    // MARK: - Singleton
    
    /// 共享日志实例
    public static let shared = Logger()
    
    // MARK: - Properties
    
    private let log = SwiftyBeaver.self
    private var isConfigured = false
    
    // MARK: - Initialization
    
    private init() {
        setupLogger()
    }
    
    // MARK: - Setup
    
    /// 配置日志系统
    private func setupLogger() {
        if isConfigured { return }
        
        // 创建控制台日志目标
        let console = ConsoleDestination()
        
        // 配置日志格式：时间 日志级别 消息
        console.format = "$DHH:mm:ss$d $L $M"
        
        // 使用OSLog API提供更好的控制台输出
        console.logPrintWay = .logger(subsystem: "com.LanguageLearningApp", category: "App")
        
        // 创建文件日志目标
        let file = FileDestination()
        
        // 添加日志目标
        log.addDestination(console)
        log.addDestination(file)
        
        isConfigured = true
        
        // 记录启动日志
        info("📱 应用程序启动")
    }
    
    // MARK: - Logging Methods
    
    /// 记录详细信息日志（最低级别）
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 源文件
    ///   - function: 函数名
    ///   - line: 行号
    ///   - context: 上下文信息
    public func verbose(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
        log.verbose(message, file: file, function: function, line: line, context: context)
    }
    
    /// 记录调试信息日志
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 源文件
    ///   - function: 函数名
    ///   - line: 行号
    ///   - context: 上下文信息
    public func debug(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
        log.debug(message, file: file, function: function, line: line, context: context)
    }
    
    /// 记录一般信息日志
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 源文件
    ///   - function: 函数名
    ///   - line: 行号
    ///   - context: 上下文信息
    public func info(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
        log.info(message, file: file, function: function, line: line, context: context)
    }
    
    /// 记录警告信息日志
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 源文件
    ///   - function: 函数名
    ///   - line: 行号
    ///   - context: 上下文信息
    public func warning(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
        log.warning(message, file: file, function: function, line: line, context: context)
    }
    
    /// 记录错误信息日志
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 源文件
    ///   - function: 函数名
    ///   - line: 行号
    ///   - context: 上下文信息
    public func error(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
        log.error(message, file: file, function: function, line: line, context: context)
    }
    
    /// 记录致命错误日志
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 源文件
    ///   - function: 函数名
    ///   - line: 行号
    ///   - context: 上下文信息
    public func fatal(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
        log.error("❌ [FATAL] \(message)", file: file, function: function, line: line, context: context)
    }
}

// MARK: - 全局便捷函数

/// 记录详细信息日志
/// - Parameters:
///   - message: 日志消息
///   - file: 源文件
///   - function: 函数名
///   - line: 行号
///   - context: 上下文信息
public func logVerbose(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
    Logger.shared.verbose(message, file: file, function: function, line: line, context: context)
}

/// 记录调试信息日志
/// - Parameters:
///   - message: 日志消息
///   - file: 源文件
///   - function: 函数名
///   - line: 行号
///   - context: 上下文信息
public func logDebug(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
    Logger.shared.debug(message, file: file, function: function, line: line, context: context)
}

/// 记录一般信息日志
/// - Parameters:
///   - message: 日志消息
///   - file: 源文件
///   - function: 函数名
///   - line: 行号
///   - context: 上下文信息
public func logInfo(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
    Logger.shared.info(message, file: file, function: function, line: line, context: context)
}

/// 记录警告信息日志
/// - Parameters:
///   - message: 日志消息
///   - file: 源文件
///   - function: 函数名
///   - line: 行号
///   - context: 上下文信息
public func logWarning(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
    Logger.shared.warning(message, file: file, function: function, line: line, context: context)
}

/// 记录错误信息日志
/// - Parameters:
///   - message: 日志消息
///   - file: 源文件
///   - function: 函数名
///   - line: 行号
///   - context: 上下文信息
public func logError(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
    Logger.shared.error(message, file: file, function: function, line: line, context: context)
}

/// 记录致命错误日志
/// - Parameters:
///   - message: 日志消息
///   - file: 源文件
///   - function: 函数名
///   - line: 行号
///   - context: 上下文信息
public func logFatal(_ message: Any, file: String = #file, function: String = #function, line: Int = #line, context: Any? = nil) {
    Logger.shared.fatal(message, file: file, function: function, line: line, context: context)
} 