import Foundation

struct SpeakingExercise: Identifiable, Codable, Sendable {
    let id: UUID
    let title: String
    let prompt: String
    let targetPhrase: String
    let difficulty: CommonDifficulty
    let category: String
    let instruction: String
    let exampleSentence: String

    // 使用 CommonDifficulty 而不是重复定义
}

// 示例数据
extension SpeakingExercise {
    static let sampleExercises: [SpeakingExercise] = [
        SpeakingExercise(
            id: UUID(),
            title: "自我介绍",
            prompt: "请用英语介绍你自己，包括你的名字、年龄、职业和兴趣爱好。",
            targetPhrase: "My name is... I am... years old. I work as a... I like...",
            difficulty: .easy,
            category: "Self Introduction",
            instruction: "请按照提示用英语进行自我介绍",
            exampleSentence: "My name is <PERSON>. I am 25 years old. I work as a software engineer. I like reading and playing basketball."
        ),
        SpeakingExercise(
            id: UUID(),
            title: "日常对话",
            prompt: "请用英语描述你的一天，从早上起床到晚上睡觉。",
            targetPhrase: "I wake up at... I have breakfast... I go to work...",
            difficulty: .medium,
            category: "Daily Life",
            instruction: "请用英语描述你的一天",
            exampleSentence: "I wake up at 7:00 am. I have breakfast at 7:30. I go to work at 8:30. I have lunch at 12:00. I finish work at 6:00 pm. I have dinner at 7:00. I go to bed at 11:00 pm."
        )
    ]
}