import Foundation

/// 单词模型
public struct Word: Identifiable, Codable, Sendable {
    public let id: UUID
    public let text: String
    public let translation: String
    public let pronunciation: String
    public let partOfSpeech: String
    public let exampleSentence: String
    public let difficulty: Int
    public var isLearned: Bool
    public var lastReviewed: Date?
    public let languageCode: String

    public init(
        id: UUID = UUID(),
        text: String,
        translation: String,
        pronunciation: String,
        partOfSpeech: String,
        exampleSentence: String,
        difficulty: Int = 1,
        isLearned: Bool = false,
        lastReviewed: Date? = nil,
        languageCode: String = "en-US"
    ) {
        self.id = id
        self.text = text
        self.translation = translation
        self.pronunciation = pronunciation
        self.partOfSpeech = partOfSpeech
        self.exampleSentence = exampleSentence
        self.difficulty = difficulty
        self.isLearned = isLearned
        self.lastReviewed = lastReviewed
        self.languageCode = languageCode
    }

    public static var sampleWords: [Word] {
        [
            Word(
                text: "hello",
                translation: "你好",
                pronunciation: "həˈləʊ",
                partOfSpeech: "interjection",
                exampleSentence: "Hello, how are you today?",
                difficulty: 1,
                languageCode: "en-US"
            ),
            Word(
                text: "thank you",
                translation: "谢谢",
                pronunciation: "ˈθæŋk juː",
                partOfSpeech: "phrase",
                exampleSentence: "Thank you for your help.",
                difficulty: 1,
                languageCode: "en-US"
            ),
            Word(
                text: "goodbye",
                translation: "再见",
                pronunciation: "ˌɡʊdˈbaɪ",
                partOfSpeech: "interjection",
                exampleSentence: "Goodbye, see you tomorrow!",
                difficulty: 1,
                languageCode: "en-US"
            ),
            Word(
                text: "hola",
                translation: "你好 (西班牙语)",
                pronunciation: "ˈo.la",
                partOfSpeech: "interjection",
                exampleSentence: "Hola, ¿cómo estás?",
                difficulty: 1,
                languageCode: "es-ES"
            )
        ]
    }
}