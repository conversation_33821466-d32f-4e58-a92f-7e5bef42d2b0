import Foundation

struct ListeningExercise: Identifiable, Codable {
    let id: UUID
    let title: String
    let audioURL: String
    let transcript: String
    let questions: [ListeningQuestion]
    let difficulty: CommonDifficulty
    let category: String

    // 使用 CommonDifficulty 而不是重复定义
}

struct ListeningQuestion: Identifiable, Codable {
    let id: UUID
    let question: String
    let options: [String]
    let correctAnswer: Int
}

// 示例数据
extension ListeningExercise {
    static let sampleExercises: [ListeningExercise] = [
        ListeningExercise(
            id: UUID(),
            title: "日常对话",
            audioURL: "https://example.com/audio1.mp3",
            transcript: "A: Hello, how are you? B: I'm fine, thank you. And you? A: I'm good, thanks.",
            questions: [
                ListeningQuestion(
                    id: UUID(),
                    question: "How is person B feeling?",
                    options: ["Good", "Fine", "Bad", "Tired"],
                    correctAnswer: 1
                )
            ],
            difficulty: .easy,
            category: "Conversation"
        ),
        ListeningExercise(
            id: UUID(),
            title: "天气预报",
            audioURL: "https://example.com/audio2.mp3",
            transcript: "Today's weather will be sunny with a high of 25 degrees Celsius.",
            questions: [
                ListeningQuestion(
                    id: UUID(),
                    question: "What's the weather like today?",
                    options: ["Rainy", "Cloudy", "Sunny", "Snowy"],
                    correctAnswer: 2
                )
            ],
            difficulty: .medium,
            category: "Weather"
        )
    ]
}