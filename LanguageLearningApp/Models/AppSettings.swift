import Foundation

/// 应用设置模型
public struct AppSettings: Codable {
    /// 是否启用通知
    public var notificationsEnabled: Bool

    /// 是否启用深色模式
    public var darkModeEnabled: Bool

    /// 每日学习目标（分钟）
    public var dailyGoalMinutes: Int

    /// 首选语言
    public var preferredLanguage: String

    /// 是否启用离线模式
    public var offlineModeEnabled: Bool

    /// 是否启用自动播放音频
    public var autoPlayAudio: Bool

    /// 是否显示翻译
    public var showTranslation: Bool

    /// 字体大小
    public var fontSize: FontSize

    /// 创建默认设置
    public static var defaultSettings: AppSettings {
        return AppSettings(
            notificationsEnabled: true,
            darkModeEnabled: false,
            dailyGoalMinutes: 30,
            preferredLanguage: "zh-CN",
            offlineModeEnabled: false,
            autoPlayAudio: true,
            showTranslation: true,
            fontSize: .medium
        )
    }
}

/// 字体大小枚举
public enum FontSize: String, Codable, CaseIterable {
    case small = "小"
    case medium = "中"
    case large = "大"
    case extraLarge = "特大"

    public var scaleFactor: CGFloat {
        switch self {
        case .small: return 0.85
        case .medium: return 1.0
        case .large: return 1.2
        case .extraLarge: return 1.4
        }
    }
}
