import Foundation

/// TTS设置模型
public struct TTSSettings: Codable {
    /// 当前TTS引擎ID
    public var currentEngineId: String

    /// 默认语言代码
    public var defaultLanguageCode: String

    /// 默认声音标识符
    public var defaultVoiceIdentifier: String?

    /// 语速
    public var speechRate: Float

    /// 音量
    public var volume: Float

    /// 音高
    public var pitch: Float

    /// 是否自动播放
    public var autoPlay: Bool

    /// 创建默认设置
    public static var defaultSettings: TTSSettings {
        return TTSSettings(
            currentEngineId: "system_tts",
            defaultLanguageCode: "zh-CN",
            defaultVoiceIdentifier: nil,
            speechRate: 0.5,
            volume: 1.0,
            pitch: 1.0,
            autoPlay: true
        )
    }
}
