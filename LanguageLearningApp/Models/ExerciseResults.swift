import Foundation

/// 语法练习结果
struct GrammarExerciseResult: Identifiable, Codable {
    /// 结果ID
    let id: UUID
    /// 练习ID
    let exerciseID: UUID
    /// 用户ID
    let userID: UUID
    /// 用户答案
    let userAnswer: String
    /// 是否正确
    let isCorrect: Bool
    /// 得分
    let score: Int
    /// 最高分
    let maxScore: Int
    /// 反馈
    let feedback: String?
    /// 解释
    let explanation: String?
    /// 完成时间
    let completedAt: Date
    
    /// 计算得分百分比
    var scorePercentage: Double {
        return Double(score) / Double(maxScore) * 100.0
    }
    
    init(id: UUID = UUID(), exerciseID: UUID, userID: UUID, userAnswer: String, isCorrect: Bool, score: Int, maxScore: Int = 100, feedback: String? = nil, explanation: String? = nil, completedAt: Date = Date()) {
        self.id = id
        self.exerciseID = exerciseID
        self.userID = userID
        self.userAnswer = userAnswer
        self.isCorrect = isCorrect
        self.score = score
        self.maxScore = maxScore
        self.feedback = feedback
        self.explanation = explanation
        self.completedAt = completedAt
    }
}

/// 听力练习结果
struct ListeningExerciseResult: Identifiable, Codable {
    /// 结果ID
    let id: UUID
    /// 练习ID
    let exerciseID: UUID
    /// 用户ID
    let userID: UUID
    /// 用户答案索引
    let userAnswerIndex: Int
    /// 是否正确
    let isCorrect: Bool
    /// 得分
    let score: Int
    /// 最高分
    let maxScore: Int
    /// 反馈
    let feedback: String?
    /// 解释
    let explanation: String?
    /// 完成时间
    let completedAt: Date
    
    /// 计算得分百分比
    var scorePercentage: Double {
        return Double(score) / Double(maxScore) * 100.0
    }
    
    init(id: UUID = UUID(), exerciseID: UUID, userID: UUID, userAnswerIndex: Int, isCorrect: Bool, score: Int, maxScore: Int = 100, feedback: String? = nil, explanation: String? = nil, completedAt: Date = Date()) {
        self.id = id
        self.exerciseID = exerciseID
        self.userID = userID
        self.userAnswerIndex = userAnswerIndex
        self.isCorrect = isCorrect
        self.score = score
        self.maxScore = maxScore
        self.feedback = feedback
        self.explanation = explanation
        self.completedAt = completedAt
    }
}

/// 口语练习结果
struct SpeakingExerciseResult: Identifiable, Codable {
    /// 结果ID
    let id: UUID
    /// 练习ID
    let exerciseID: UUID
    /// 用户ID
    let userID: UUID
    /// 录音URL
    let recordingURL: URL
    /// 转写文本
    let transcription: String?
    /// 准确度评分
    let accuracyScore: Int
    /// 流利度评分
    let fluencyScore: Int
    /// 发音评分
    let pronunciationScore: Int
    /// 总分
    let overallScore: Int
    /// 最高分
    let maxScore: Int
    /// 反馈
    let feedback: String?
    /// 改进建议
    let suggestions: [String]?
    /// 完成时间
    let completedAt: Date
    
    /// 计算得分百分比
    var scorePercentage: Double {
        return Double(overallScore) / Double(maxScore) * 100.0
    }
    
    init(id: UUID = UUID(), exerciseID: UUID, userID: UUID, recordingURL: URL, transcription: String? = nil, accuracyScore: Int, fluencyScore: Int, pronunciationScore: Int, overallScore: Int, maxScore: Int = 100, feedback: String? = nil, suggestions: [String]? = nil, completedAt: Date = Date()) {
        self.id = id
        self.exerciseID = exerciseID
        self.userID = userID
        self.recordingURL = recordingURL
        self.transcription = transcription
        self.accuracyScore = accuracyScore
        self.fluencyScore = fluencyScore
        self.pronunciationScore = pronunciationScore
        self.overallScore = overallScore
        self.maxScore = maxScore
        self.feedback = feedback
        self.suggestions = suggestions
        self.completedAt = completedAt
    }
}

/// 练习会话模型
struct ExerciseResultsPracticeSession: Identifiable, Codable {
    /// 会话ID
    let id: UUID
    /// 用户ID
    let userID: UUID
    /// 练习类型
    let type: String
    /// 持续时间（分钟）
    let durationMinutes: Int
    /// 得分
    let score: Int
    /// 最高分
    let maxScore: Int
    /// 完成时间
    let completedAt: Date
    /// 创建时间
    let createdAt: Date
    
    /// 计算得分百分比
    var scorePercentage: Double {
        return Double(score) / Double(maxScore) * 100.0
    }
    
    init(id: UUID = UUID(), userID: UUID, type: String, durationMinutes: Int, score: Int, maxScore: Int = 100, completedAt: Date = Date(), createdAt: Date = Date()) {
        self.id = id
        self.userID = userID
        self.type = type
        self.durationMinutes = durationMinutes
        self.score = score
        self.maxScore = maxScore
        self.completedAt = completedAt
        self.createdAt = createdAt
    }
}
