import Foundation
import SwiftUI

/// Model representing a practice card for the swipe interface
struct PracticeCardModel: Identifiable, Hashable {
    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: PracticeCardModel, rhs: PracticeCardModel) -> Bool {
        lhs.id == rhs.id
    }
    let id: UUID
    let type: PracticeCardType
    let title: String
    let description: String
    let icon: String
    let color: Color
    let difficulty: Difficulty
    let content: PracticeCardContent

    init(
        id: UUID = UUID(),
        type: PracticeCardType,
        title: String,
        description: String,
        icon: String,
        color: Color,
        difficulty: Difficulty = .easy,
        content: PracticeCardContent
    ) {
        self.id = id
        self.type = type
        self.title = title
        self.description = description
        self.icon = icon
        self.color = color
        self.difficulty = difficulty
        self.content = content
    }

    /// Types of practice cards
    enum PracticeCardType: String, CaseIterable {
        case word
        case listening
        case speaking
        case grammar
        case writing
        case reading

        var displayName: String {
            switch self {
            case .word:
                return "Word Learning"
            case .listening:
                return "Listening Practice"
            case .speaking:
                return "Speaking Practice"
            case .grammar:
                return "Grammar Practice"
            case .writing:
                return "Writing Practice"
            case .reading:
                return "Reading Practice"
            }
        }

        var icon: String {
            switch self {
            case .word:
                return "textformat.abc"
            case .listening:
                return "ear"
            case .speaking:
                return "mic"
            case .grammar:
                return "text.book.closed"
            case .writing:
                return "pencil"
            case .reading:
                return "book"
            }
        }

        var color: Color {
            switch self {
            case .word:
                return Color.blue
            case .listening:
                return Color.green
            case .speaking:
                return Color.orange
            case .grammar:
                return Color.purple
            case .writing:
                return Color(red: 1.0, green: 0.59, blue: 0.0)  // #FF9500
            case .reading:
                return Color(red: 0.35, green: 0.34, blue: 0.84)  // #5856D6
            }
        }
    }

    /// 使用统一的难度级别定义
    typealias Difficulty = CommonDifficulty
}

/// Content for different types of practice cards
enum PracticeCardContent: Hashable {
    case word(Word)
    case listening(ListeningExercise)
    case speaking(SpeakingExercise)
    case grammar(GrammarExercise)
    case exercise(Exercise)

    // Implement Hashable conformance
    func hash(into hasher: inout Hasher) {
        switch self {
        case .word(let word):
            hasher.combine("word")
            hasher.combine(word.id)
        case .listening(let exercise):
            hasher.combine("listening")
            hasher.combine(exercise.id)
        case .speaking(let exercise):
            hasher.combine("speaking")
            hasher.combine(exercise.id)
        case .grammar(let exercise):
            hasher.combine("grammar")
            hasher.combine(exercise.id)
        case .exercise(let exercise):
            hasher.combine("exercise")
            hasher.combine(exercise.id)
        }
    }

    static func == (lhs: PracticeCardContent, rhs: PracticeCardContent) -> Bool {
        switch (lhs, rhs) {
        case (.word(let lhsWord), .word(let rhsWord)):
            return lhsWord.id == rhsWord.id
        case (.listening(let lhsExercise), .listening(let rhsExercise)):
            return lhsExercise.id == rhsExercise.id
        case (.speaking(let lhsExercise), .speaking(let rhsExercise)):
            return lhsExercise.id == rhsExercise.id
        case (.grammar(let lhsExercise), .grammar(let rhsExercise)):
            return lhsExercise.id == rhsExercise.id
        case (.exercise(let lhsExercise), .exercise(let rhsExercise)):
            return lhsExercise.id == rhsExercise.id
        default:
            return false
        }
    }
}

// MARK: - Sample Data
extension PracticeCardModel {
    static var samples: [PracticeCardModel] {
        [
            // Word learning card
            PracticeCardModel(
                type: .word,
                title: "Learn New Words",
                description: "Expand your vocabulary with new words",
                icon: PracticeCardModel.PracticeCardType.word.icon,
                color: PracticeCardModel.PracticeCardType.word.color,
                difficulty: .easy,
                content: createSampleWordContent()
            ),

            // Listening practice card
            PracticeCardModel(
                type: .listening,
                title: "Improve Listening",
                description: "Practice understanding spoken language",
                icon: PracticeCardModel.PracticeCardType.listening.icon,
                color: PracticeCardModel.PracticeCardType.listening.color,
                difficulty: .medium,
                content: createSampleListeningContent()
            ),

            // Speaking practice card
            PracticeCardModel(
                type: .speaking,
                title: "Practice Speaking",
                description: "Improve your pronunciation and fluency",
                icon: PracticeCardModel.PracticeCardType.speaking.icon,
                color: PracticeCardModel.PracticeCardType.speaking.color,
                difficulty: .medium,
                content: createSampleSpeakingContent()
            ),

            // Grammar practice card
            PracticeCardModel(
                type: .grammar,
                title: "Grammar Challenge",
                description: "Test your grammar knowledge",
                icon: PracticeCardModel.PracticeCardType.grammar.icon,
                color: PracticeCardModel.PracticeCardType.grammar.color,
                difficulty: .hard,
                content: createSampleGrammarContent()
            )
        ]
    }

    // Helper methods to create sample content
    private static func createSampleWordContent() -> PracticeCardContent {
        let sampleWord = Word(
            text: "hello",
            translation: "你好",
            pronunciation: "həˈləʊ",
            partOfSpeech: "interjection",
            exampleSentence: "Hello, how are you today?",
            difficulty: 1
        )
        return .word(sampleWord)
    }

    private static func createSampleListeningContent() -> PracticeCardContent {
        let sampleQuestion = ListeningQuestion(
            id: UUID(),
            question: "How is person B feeling?",
            options: ["Good", "Fine", "Bad", "Tired"],
            correctAnswer: 1
        )

        let sampleListening = ListeningExercise(
            id: UUID(),
            title: "日常对话",
            audioURL: "https://example.com/audio1.mp3",
            transcript: "A: Hello, how are you? B: I'm fine, thank you. And you? A: I'm good, thanks.",
            questions: [sampleQuestion],
            difficulty: .easy,
            category: "Conversation"
        )

        return .listening(sampleListening)
    }

    private static func createSampleSpeakingContent() -> PracticeCardContent {
        let sampleSpeaking = SpeakingExercise(
            id: UUID(),
            title: "自我介绍",
            prompt: "请用英语介绍你自己，包括你的名字、年龄、职业和兴趣爱好。",
            targetPhrase: "My name is... I am... years old. I work as a... I like...",
            difficulty: .easy,
            category: "Self Introduction",
            instruction: "请按照提示用英语进行自我介绍",
            exampleSentence: "My name is John. I am 25 years old. I work as a software engineer. I like reading and playing basketball."
        )

        return .speaking(sampleSpeaking)
    }

    private static func createSampleGrammarContent() -> PracticeCardContent {
        let sampleGrammar = GrammarExercise(
            id: UUID(),
            title: "动词时态",
            instruction: "选择正确的动词形式完成句子",
            question: "She ___ to school every day.",
            options: ["go", "goes", "going", "went"],
            correctAnswer: "goes",
            explanation: "当主语是第三人称单数时，动词需要加 -s 或 -es。",
            difficulty: .easy,
            category: "Verb Tenses",
            exampleSentence: "She goes to school every day.",
            context: "在英语中，第三人称单数（he, she, it）的动词需要加上-s或-es。"
        )

        return .grammar(sampleGrammar)
    }
}
