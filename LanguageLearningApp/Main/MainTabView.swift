import SwiftUI

/// 主标签视图，作为应用的主入口
struct MainTabView: View {
    @State private var selectedTab = 0
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        TabView(selection: $selectedTab) {
            // 首页
            NavigationView {
                DailyPracticeDashboardView()
            }
            .tabItem {
                Label("每日练习", systemImage: "house.fill")
            }
            .tag(0)

            // 课程
            NavigationView {
                DailyPracticeView()
            }
            .tabItem {
                Label("Practice", systemImage: "book.fill")
            }
            .tag(1)

            // 评估
            NavigationView {
                EvaluationEntryView()
            }
            .tabItem {
                Label("评估", systemImage: "chart.bar.fill")
            }
            .tag(2)

            // 词汇
            NavigationView {
                PracticeView()
            }
            .tabItem {
                Label("词汇", systemImage: "textformat.abc")
            }
            .tag(3)

            // 个人中心
            NavigationView {
                ProfileView()
            }
            .tabItem {
                Label("我的", systemImage: "person.fill")
            }
            .tag(4)
        }
        .accentColor(AppTheme.Colors.primary)
        .onAppear {
            // 设置 TabBar 的外观
            let appearance = UITabBarAppearance()
            appearance.configureWithOpaqueBackground()
            
            // 设置背景色
            appearance.backgroundColor = UIColor(AppTheme.Colors.background)
            
            // 设置选中和未选中状态的颜色
            appearance.stackedLayoutAppearance.selected.iconColor = UIColor(AppTheme.Colors.primary)
            appearance.stackedLayoutAppearance.selected.titleTextAttributes = [.foregroundColor: UIColor(AppTheme.Colors.primary)]
            appearance.stackedLayoutAppearance.normal.iconColor = UIColor(AppTheme.Colors.textTertiary)
            appearance.stackedLayoutAppearance.normal.titleTextAttributes = [.foregroundColor: UIColor(AppTheme.Colors.textTertiary)]
            
            // 应用外观
            UITabBar.appearance().standardAppearance = appearance
            if #available(iOS 15.0, *) {
                UITabBar.appearance().scrollEdgeAppearance = appearance
            }
        }
    }
}

struct MainTabView_Previews: PreviewProvider {
    static var previews: some View {
        MainTabView()
            .environmentObject(ThemeManager.shared)
    }
}
