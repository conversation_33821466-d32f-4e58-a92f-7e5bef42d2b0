import Foundation
import Combine

/// 用户服务错误
enum UserServiceError: Error {
    case networkError(Error)
    case decodingError(Error)
    case invalidResponse
    case notAuthenticated
    case serverError(String)
    case unknown
}

/// 用户服务，处理用户相关的API调用
class UserService: UserServiceProtocol {
    // 单例实例，保持向后兼容性
    static let shared = UserService(apiClient: APIClient.shared)

    private let apiClient: APIClientProtocol
    private let maxRetries = 3

    /// 是否有网络连接
    var isNetworkAvailable: Bool {
        return apiClient.isNetworkAvailable
    }

    /// 初始化方法
    /// - Parameter apiClient: API客户端
    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    /// 获取当前用户信息
    /// - Returns: 包含用户信息的发布者
    func getCurrentUser() -> AnyPublisher<User, Error> {
        // 检查是否有网络连接
        guard apiClient.isNetworkAvailable else {
            return getOfflineCurrentUser()
        }

        // 使用 APIEndpoint 枚举
        let endpoint = APIEndpoint.userProfile

        // 使用 APIClient 发送请求
        return apiClient.request(endpoint: endpoint)
            .retry(maxRetries) // 重试机制
            .tryMap { data -> User in
                // 使用 JSONDecoder 解码数据
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601

                // 尝试解码为 User
                if let user = try? decoder.decode(User.self, from: data) {
                    return user
                }

                throw UserServiceError.invalidResponse
            }
            .mapError { (error: Error) -> Error in
                // 错误处理
                if let appError = error as? AppError {
                    switch appError {
                    case .networkError(let message):
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: message]))
                    case .invalidResponse:
                        return UserServiceError.invalidResponse
                    case .decodingError(let error):
                        return UserServiceError.decodingError(error)
                    case .serverError(let message):
                        return UserServiceError.serverError(message)
                    case .unauthorized:
                        return UserServiceError.notAuthenticated
                    case .noInternetConnection:
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -2, userInfo: [NSLocalizedDescriptionKey: "无网络连接"]))
                    default:
                        return UserServiceError.unknown
                    }
                }
                return error
            }
            .catch { [weak self] (error: Error) -> AnyPublisher<User, Error> in
                // 如果网络请求失败，尝试使用离线数据
                print("网络请求失败，使用离线数据: \(error.localizedDescription)")
                return self?.getOfflineCurrentUser() ?? Fail(error: error).eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
    }

    /// 获取离线当前用户信息
    /// - Returns: 包含用户信息的发布者
    private func getOfflineCurrentUser() -> AnyPublisher<User, Error> {
        // 从本地存储获取缓存的用户信息
        // 这里简化实现，使用模拟数据
        return Future<User, Error> { promise in
            // 创建模拟用户
            let user = User(
                id: UUID(),
                username: "学习者",
                email: "<EMAIL>",
                name: "测试用户",
                avatar: nil,
                createdAt: Date().addingTimeInterval(-30*24*60*60), // 30天前
                updatedAt: Date(),
                lastLoginAt: Date(),
                isActive: true,
                settings: UserSettings.default,
                stats: UserStats.sample
            )

            promise(.success(user))
        }
        .eraseToAnyPublisher()
    }

    /// 登录
    /// - Parameters:
    ///   - username: 用户名
    ///   - password: 密码
    /// - Returns: 包含用户信息的发布者
    func login(username: String, password: String) -> AnyPublisher<User, Error> {
        // 检查是否有网络连接
        guard apiClient.isNetworkAvailable else {
            return Fail(error: UserServiceError.networkError(NSError(domain: "UserService", code: -1, userInfo: [NSLocalizedDescriptionKey: "无网络连接，无法登录"])))
                .eraseToAnyPublisher()
        }

        // 使用 APIEndpoint 枚举
        let endpoint = APIEndpoint.login(username: username, password: password)

        // 使用 APIClient 发送请求
        return apiClient.request(endpoint: endpoint)
            .retry(maxRetries) // 重试机制
            .tryMap { data -> User in
                // 使用 JSONDecoder 解码数据
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601

                // 尝试解码为 User
                if let user = try? decoder.decode(User.self, from: data) {
                    return user
                }

                throw UserServiceError.invalidResponse
            }
            .mapError { (error: Error) -> Error in
                // 错误处理
                if let appError = error as? AppError {
                    switch appError {
                    case .networkError(let message):
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: message]))
                    case .invalidResponse:
                        return UserServiceError.invalidResponse
                    case .decodingError(let error):
                        return UserServiceError.decodingError(error)
                    case .serverError(let message):
                        return UserServiceError.serverError(message)
                    case .unauthorized:
                        return UserServiceError.notAuthenticated
                    case .noInternetConnection:
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -2, userInfo: [NSLocalizedDescriptionKey: "无网络连接"]))
                    default:
                        return UserServiceError.unknown
                    }
                }
                return error
            }
            .eraseToAnyPublisher()
    }

    /// 注册
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 邮箱
    ///   - password: 密码
    /// - Returns: 包含用户信息的发布者
    func register(username: String, email: String, password: String) -> AnyPublisher<User, Error> {
        // 检查是否有网络连接
        guard apiClient.isNetworkAvailable else {
            return Fail(error: UserServiceError.networkError(NSError(domain: "UserService", code: -1, userInfo: [NSLocalizedDescriptionKey: "无网络连接，无法注册"])))
                .eraseToAnyPublisher()
        }

        // 使用 APIEndpoint 枚举
        let endpoint = APIEndpoint.register(username: username, email: email, password: password)

        // 使用 APIClient 发送请求
        return apiClient.request(endpoint: endpoint)
            .retry(maxRetries) // 重试机制
            .tryMap { data -> User in
                // 使用 JSONDecoder 解码数据
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601

                // 尝试解码为 User
                if let user = try? decoder.decode(User.self, from: data) {
                    return user
                }

                throw UserServiceError.invalidResponse
            }
            .mapError { (error: Error) -> Error in
                // 错误处理
                if let appError = error as? AppError {
                    switch appError {
                    case .networkError(let message):
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: message]))
                    case .invalidResponse:
                        return UserServiceError.invalidResponse
                    case .decodingError(let error):
                        return UserServiceError.decodingError(error)
                    case .serverError(let message):
                        return UserServiceError.serverError(message)
                    case .unauthorized:
                        return UserServiceError.notAuthenticated
                    case .noInternetConnection:
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -2, userInfo: [NSLocalizedDescriptionKey: "无网络连接"]))
                    default:
                        return UserServiceError.unknown
                    }
                }
                return error
            }
            .eraseToAnyPublisher()
    }

    /// 登出
    /// - Returns: 包含成功状态的发布者
    func logout() -> AnyPublisher<Bool, Error> {
        // 检查是否有网络连接
        guard apiClient.isNetworkAvailable else {
            // 即使没有网络连接，也可以在本地执行登出操作
            // 清除本地存储的用户信息和令牌
            UserDefaults.standard.removeObject(forKey: "authToken")
            return Just(true)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }

        // 使用 APIEndpoint 枚举
        let endpoint = APIEndpoint.logout

        // 使用 APIClient 发送请求
        return apiClient.request(endpoint: endpoint)
            .retry(maxRetries) // 重试机制
            .map { _ in
                // 清除本地存储的用户信息和令牌
                UserDefaults.standard.removeObject(forKey: "authToken")
                return true
            }
            .mapError { (error: Error) -> Error in
                // 错误处理
                if let appError = error as? AppError {
                    switch appError {
                    case .networkError(let message):
                        // 即使网络请求失败，也可以在本地执行登出操作
                        UserDefaults.standard.removeObject(forKey: "authToken")
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: message]))
                    default:
                        // 即使请求失败，也可以在本地执行登出操作
                        UserDefaults.standard.removeObject(forKey: "authToken")
                        return error
                    }
                }
                // 即使请求失败，也可以在本地执行登出操作
                UserDefaults.standard.removeObject(forKey: "authToken")
                return error
            }
            .catch { _ in
                // 即使请求失败，也可以在本地执行登出操作
                UserDefaults.standard.removeObject(forKey: "authToken")
                return Just(true)
                    .setFailureType(to: Error.self)
                    .eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
    }

    /// 更新用户资料
    /// - Parameters:
    ///   - name: 姓名
    ///   - email: 邮箱
    ///   - avatar: 头像数据
    /// - Returns: 包含更新后用户信息的发布者
    func updateUserProfile(name: String?, email: String?, avatar: Data?) -> AnyPublisher<User, Error> {
        // 检查是否有网络连接
        guard apiClient.isNetworkAvailable else {
            return Fail(error: UserServiceError.networkError(NSError(domain: "UserService", code: -1, userInfo: [NSLocalizedDescriptionKey: "无网络连接，无法更新用户资料"])))
                .eraseToAnyPublisher()
        }

        // 使用 APIEndpoint 枚举
        let endpoint = APIEndpoint.updateUserProfile(name: name, email: email, avatar: avatar)

        // 使用 APIClient 发送请求
        return apiClient.request(endpoint: endpoint)
            .retry(maxRetries) // 重试机制
            .tryMap { data -> User in
                // 使用 JSONDecoder 解码数据
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601

                // 尝试解码为 User
                if let user = try? decoder.decode(User.self, from: data) {
                    return user
                }

                throw UserServiceError.invalidResponse
            }
            .mapError { (error: Error) -> Error in
                // 错误处理
                if let appError = error as? AppError {
                    switch appError {
                    case .networkError(let message):
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: message]))
                    case .invalidResponse:
                        return UserServiceError.invalidResponse
                    case .decodingError(let error):
                        return UserServiceError.decodingError(error)
                    case .serverError(let message):
                        return UserServiceError.serverError(message)
                    case .unauthorized:
                        return UserServiceError.notAuthenticated
                    case .noInternetConnection:
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -2, userInfo: [NSLocalizedDescriptionKey: "无网络连接"]))
                    default:
                        return UserServiceError.unknown
                    }
                }
                return error
            }
            .eraseToAnyPublisher()
    }

    /// 重置密码
    /// - Parameter email: 邮箱
    /// - Returns: 包含成功状态的发布者
    func resetPassword(email: String) -> AnyPublisher<Bool, Error> {
        // 检查是否有网络连接
        guard apiClient.isNetworkAvailable else {
            return Fail(error: UserServiceError.networkError(NSError(domain: "UserService", code: -1, userInfo: [NSLocalizedDescriptionKey: "无网络连接，无法重置密码"])))
                .eraseToAnyPublisher()
        }

        // 使用 APIEndpoint 枚举
        let endpoint = APIEndpoint.resetPassword(email: email)

        // 使用 APIClient 发送请求
        return apiClient.request(endpoint: endpoint)
            .retry(maxRetries) // 重试机制
            .map { _ in true }
            .mapError { (error: Error) -> Error in
                // 错误处理
                if let appError = error as? AppError {
                    switch appError {
                    case .networkError(let message):
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: message]))
                    case .invalidResponse:
                        return UserServiceError.invalidResponse
                    case .decodingError(let error):
                        return UserServiceError.decodingError(error)
                    case .serverError(let message):
                        return UserServiceError.serverError(message)
                    case .unauthorized:
                        return UserServiceError.notAuthenticated
                    case .noInternetConnection:
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -2, userInfo: [NSLocalizedDescriptionKey: "无网络连接"]))
                    default:
                        return UserServiceError.unknown
                    }
                }
                return error
            }
            .eraseToAnyPublisher()
    }

    /// 修改密码
    /// - Parameters:
    ///   - oldPassword: 旧密码
    ///   - newPassword: 新密码
    /// - Returns: 包含成功状态的发布者
    func changePassword(oldPassword: String, newPassword: String) -> AnyPublisher<Bool, Error> {
        // 检查是否有网络连接
        guard apiClient.isNetworkAvailable else {
            return Fail(error: UserServiceError.networkError(NSError(domain: "UserService", code: -1, userInfo: [NSLocalizedDescriptionKey: "无网络连接，无法修改密码"])))
                .eraseToAnyPublisher()
        }

        // 使用 APIEndpoint 枚举
        let endpoint = APIEndpoint.changePassword(oldPassword: oldPassword, newPassword: newPassword)

        // 使用 APIClient 发送请求
        return apiClient.request(endpoint: endpoint)
            .retry(maxRetries) // 重试机制
            .map { _ in true }
            .mapError { (error: Error) -> Error in
                // 错误处理
                if let appError = error as? AppError {
                    switch appError {
                    case .networkError(let message):
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: message]))
                    case .invalidResponse:
                        return UserServiceError.invalidResponse
                    case .decodingError(let error):
                        return UserServiceError.decodingError(error)
                    case .serverError(let message):
                        return UserServiceError.serverError(message)
                    case .unauthorized:
                        return UserServiceError.notAuthenticated
                    case .noInternetConnection:
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -2, userInfo: [NSLocalizedDescriptionKey: "无网络连接"]))
                    default:
                        return UserServiceError.unknown
                    }
                }
                return error
            }
            .eraseToAnyPublisher()
    }

    /// 删除账户
    /// - Returns: 包含成功状态的发布者
    func deleteAccount() -> AnyPublisher<Bool, Error> {
        // 检查是否有网络连接
        guard apiClient.isNetworkAvailable else {
            return Fail(error: UserServiceError.networkError(NSError(domain: "UserService", code: -1, userInfo: [NSLocalizedDescriptionKey: "无网络连接，无法删除账户"])))
                .eraseToAnyPublisher()
        }

        // 使用 APIEndpoint 枚举
        let endpoint = APIEndpoint.deleteAccount

        // 使用 APIClient 发送请求
        return apiClient.request(endpoint: endpoint)
            .retry(maxRetries) // 重试机制
            .map { _ in
                // 清除本地存储的用户信息和令牌
                UserDefaults.standard.removeObject(forKey: "authToken")
                return true
            }
            .mapError { (error: Error) -> Error in
                // 错误处理
                if let appError = error as? AppError {
                    switch appError {
                    case .networkError(let message):
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: message]))
                    case .invalidResponse:
                        return UserServiceError.invalidResponse
                    case .decodingError(let error):
                        return UserServiceError.decodingError(error)
                    case .serverError(let message):
                        return UserServiceError.serverError(message)
                    case .unauthorized:
                        return UserServiceError.notAuthenticated
                    case .noInternetConnection:
                        return UserServiceError.networkError(NSError(domain: "NetworkError", code: -2, userInfo: [NSLocalizedDescriptionKey: "无网络连接"]))
                    default:
                        return UserServiceError.unknown
                    }
                }
                return error
            }
            .eraseToAnyPublisher()
    }
}
