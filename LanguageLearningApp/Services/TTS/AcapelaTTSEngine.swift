import Foundation
import AVFoundation

// TODO: Replace with actual Acapela API details
private let ACAPELA_API_KEY = "YOUR_ACAPELA_API_KEY"
private let ACAPELA_API_ENDPOINT = "https://api.acapela-group.com/your/endpoint" // Replace with actual endpoint

@MainActor
class AcapelaTTSEngine: NSObject, TTSEngine, AVAudioPlayerDelegate {
    let engineId: String = "acapela_real"
    let engineName: String = "Acapela (Real)"
    
    // Availability could depend on API key presence or a successful initial check
    var isAvailable: Bool {
        // Basic check, could be expanded (e.g., network reachability, API key format)
        return ACAPELA_API_KEY != "YOUR_ACAPELA_API_KEY" && !ACAPELA_API_KEY.isEmpty
    }
    
    private var audioPlayer: AVAudioPlayer?
    private var currentSpeakCompletion: (@Sendable (Error?) -> Void)?
    private var session: URLSession

    override init() {
        // Configure URLSession for network requests
        let configuration = URLSessionConfiguration.default
        // Potentially add headers like API key here if needed for all requests
        // configuration.httpAdditionalHeaders = ["Authorization": "Bearer \(ACAPELA_API_KEY)"]
        self.session = URLSession(configuration: configuration)
        super.init()
    }

    func speak(text: String, languageCode: String, voiceIdentifier: String?, completion: @escaping @Sendable (Error?) -> Void) async {
        guard await isAvailable else {
            completion(NSError(domain: engineId, code: -1, userInfo: [NSLocalizedDescriptionKey: "\(engineName) is not available. Check API key."]))
            return
        }

        guard let voiceId = voiceIdentifier else {
            completion(NSError(domain: engineId, code: -2, userInfo: [NSLocalizedDescriptionKey: "Voice identifier is required for Acapela TTS."]))
            return
        }

        self.currentSpeakCompletion = completion

        // 1. Construct the request to Acapela's API
        // This is a hypothetical structure; actual API will differ.
        guard var urlComponents = URLComponents(string: "\(ACAPELA_API_ENDPOINT)/tts") else {
            completion(NSError(domain: engineId, code: -3, userInfo: [NSLocalizedDescriptionKey: "Invalid API endpoint."]))
            return
        }
        
        urlComponents.queryItems = [
            URLQueryItem(name: "text", value: text),
            URLQueryItem(name: "lang", value: languageCode), // Or however Acapela specifies language
            URLQueryItem(name: "voice", value: voiceId),     // Acapela voice ID
            URLQueryItem(name: "apikey", value: ACAPELA_API_KEY), // Or in header
            URLQueryItem(name: "output", value: "audio_data_format") // e.g., mp3, wav
        ]

        guard let requestUrl = urlComponents.url else {
            completion(NSError(domain: engineId, code: -4, userInfo: [NSLocalizedDescriptionKey: "Could not create request URL."]))
            return
        }

        var request = URLRequest(url: requestUrl)
        request.httpMethod = "GET" // Or POST, depending on Acapela's API

        // 2. Perform the network request
        let task = session.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            if let error = error {
                DispatchQueue.main.async {
                    self.currentSpeakCompletion?(error)
                    self.currentSpeakCompletion = nil
                }
                return
            }

            guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
                let statusCode = (response as? HTTPURLResponse)?.statusCode ?? -1
                DispatchQueue.main.async {
                    self.currentSpeakCompletion?(NSError(domain: self.engineId, code: statusCode, userInfo: [NSLocalizedDescriptionKey: "Acapela API request failed with status \(statusCode)."]))
                    self.currentSpeakCompletion = nil
                }
                return
            }

            guard let audioData = data else {
                DispatchQueue.main.async {
                    self.currentSpeakCompletion?(NSError(domain: self.engineId, code: -5, userInfo: [NSLocalizedDescriptionKey: "No audio data received from Acapela."]))
                    self.currentSpeakCompletion = nil
                }
                return
            }

            // 3. Play the audio data
            DispatchQueue.main.async {
                do {
                    // Ensure audio session is active and configured for playback
                    try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
                    try AVAudioSession.sharedInstance().setActive(true)
                    
                    self.audioPlayer = try AVAudioPlayer(data: audioData)
                    self.audioPlayer?.delegate = self
                    self.audioPlayer?.play()
                } catch {
                    self.currentSpeakCompletion?(error)
                    self.currentSpeakCompletion = nil
                }
            }
        }
        task.resume()
    }

    func getAvailableVoices(forLanguageCode: String?) async -> [VoiceInfo] {
        // This would typically involve an API call to Acapela to fetch voices.
        // For now, returning a placeholder.
        // The actual implementation would need to:
        // 1. Make a network request to an Acapela endpoint for voices.
        // 2. Parse the response (likely JSON).
        // 3. Map the Acapela voice data to VoiceInfo structs.
        
        print("\(engineName): Fetching available voices from Acapela API (not implemented, returning placeholders).")
        
        var voices: [VoiceInfo] = []
        if forLanguageCode == nil || forLanguageCode?.starts(with: "en") == true {
            voices.append(VoiceInfo(id: "acapela.voice.sharon", name: "Sharon (Acapela EN)", languageCode: "en-US"))
            voices.append(VoiceInfo(id: "acapela.voice.ryan", name: "Ryan (Acapela EN)", languageCode: "en-US"))
        }
        if forLanguageCode == nil || forLanguageCode?.starts(with: "zh") == true {
             voices.append(VoiceInfo(id: "acapela.voice.linlin", name: "LinLin (Acapela ZH)", languageCode: "zh-CN"))
        }
        // Add more placeholder voices as needed or based on expected Acapela offerings.
        return voices
    }

    func stopSpeaking() async {
        audioPlayer?.stop()
        // If there's an ongoing URLSessionTask for fetching audio, you might want to cancel it too.
        // session.getAllTasks { tasks in tasks.forEach { $0.cancel() } }
        
        if let completion = currentSpeakCompletion {
            let error = NSError(domain: engineId, code: -99, userInfo: [NSLocalizedDescriptionKey: "Speech stopped by user."])
            completion(error)
            currentSpeakCompletion = nil
        }
        // Reset audio session if necessary
        // try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
    }

    // MARK: - AVAudioPlayerDelegate
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            if flag {
                self.currentSpeakCompletion?(nil)
            } else {
                self.currentSpeakCompletion?(NSError(domain: self.engineId, code: -6, userInfo: [NSLocalizedDescriptionKey: "Audio playback failed."]))
            }
            self.currentSpeakCompletion = nil
            // try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            self.currentSpeakCompletion?(error ?? NSError(domain: self.engineId, code: -7, userInfo: [NSLocalizedDescriptionKey: "Audio decoding error."]))
            self.currentSpeakCompletion = nil
            // try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        }
    }
}
