import Foundation
import Combine
import AVFoundation // For AVSpeechSynthesizer itself, if TTSManager needs to interact at a low level, though ideally it uses TTSEngine protocol.

@MainActor
public class TTSManager: ObservableObject, TTSManagerProtocol {
    static let shared = TTSManager()

    public enum TTSError: Error {
        case noEngineAvailable
        case unknownError
        case cancelled
    }

    public enum Status: Equatable, Sendable {
        case idle
        case initializing
    }

    @Published private(set) var availableEngines: [TTSEngine] = []
    @Published private(set) var currentEngine: TTSEngine
    @Published private(set) var isSpeaking: Bool = false

    private let defaultEngineIdKey = "selectedTTSEngineID"
    private var cancellables = Set<AnyCancellable>()
    private let storageManager: StorageManagerProtocol

    private init(storageManager: StorageManagerProtocol = StorageManager.shared) {
        self.storageManager = storageManager

        // Initialize available engines
        let systemEngine = SystemTTSEngine()
        let appleNeuralEngine = AppleNeuralTTSEngine()
        let kokoroEngine = KokoroTTSEngine()
        let acapelaPlaceholder = AcapelaTTSEngine_Placeholder()
        let resemblePlaceholder = ResembleAITTSEngine_Placeholder()
        let localLLMEngine = LocalLLMTTSEngine_Placeholder()
        let realAcapelaEngine = AcapelaTTSEngine()
        let realResembleAIEngine = ResembleAITTSEngine()

        // Store all engines
        self.availableEngines = [
            systemEngine,
            appleNeuralEngine,
            kokoroEngine,
            realAcapelaEngine,
            realResembleAIEngine,
            localLLMEngine,
            acapelaPlaceholder,
            resemblePlaceholder
        ]
        
        // Initialize currentEngine with a default before further use of self
        self.currentEngine = systemEngine 

        // Load settings from storage manager
        let settings = storageManager.loadTTSSettings() ?? TTSSettings.defaultSettings

        // Find the selected engine ID or use default (systemEngine initially)
        var determinedEngine: TTSEngine = systemEngine // Default to system engine

        if let foundEngineById = self.availableEngines.first(where: { $0.engineId == settings.currentEngineId }) {
            determinedEngine = foundEngineById
        }
        // The check for appleNeuralEngine.isAvailable needs to be async,
        // so we can't do it here directly for initial assignment.
        // We'll set a default and an async task can refine this later if needed.
        
        self.currentEngine = determinedEngine

        print("TTSManager initialized. Current engine: \(self.currentEngine.engineName)")
        // Spawn a task to asynchronously determine and set the best available engine if different from stored.
        Task {
            await self.updateEngineBasedOnAvailability()
        }
        // Load settings (this might re-set currentEngine if stored one is valid)
        // loadSettings() // loadSettings is called at the end of init by the original code, let's see if updateEngineBasedOnAvailability is enough
    }

    // New async function to update engine based on availability
    private func updateEngineBasedOnAvailability() async {
        let settings = storageManager.loadTTSSettings() ?? TTSSettings.defaultSettings
        var bestEngine: TTSEngine = self.currentEngine // Start with current

        // Prefer Apple Neural if available and no specific user choice or if user choice is unavailable
        let appleNeuralEngine = self.availableEngines.first(where: { $0.engineId == AppleNeuralTTSEngine().engineId }) as? AppleNeuralTTSEngine
        
        if await appleNeuralEngine?.isAvailable == true {
            // If current engine is just the system default (placeholder) and Apple Neural is better and available
            if self.currentEngine.engineId == SystemTTSEngine().engineId || settings.currentEngineId == SystemTTSEngine().engineId {
                 bestEngine = appleNeuralEngine!
            }
        }
        
        // If there's a user-selected engine ID, try to use that if it's available
        if let userSelectedEngine = self.availableEngines.first(where: { $0.engineId == settings.currentEngineId }) {
            if await userSelectedEngine.isAvailable { // Check availability
                bestEngine = userSelectedEngine
            } else {
                print("User selected engine \(userSelectedEngine.engineName) is not available.")
                // Fallback to Apple Neural if user's choice is not available
                if await appleNeuralEngine?.isAvailable == true {
                    bestEngine = appleNeuralEngine!
                } else {
                    // Fallback to system if Apple Neural is also not available
                    bestEngine = self.availableEngines.first(where: { $0.engineId == SystemTTSEngine().engineId }) ?? self.currentEngine // Should always find system
                }
            }
        }


        if self.currentEngine.engineId != bestEngine.engineId {
            self.currentEngine = bestEngine
            var updatedSettings = storageManager.loadTTSSettings() ?? TTSSettings.defaultSettings
            updatedSettings.currentEngineId = bestEngine.engineId
            storageManager.saveTTSSettings(updatedSettings)
            print("TTSManager: Engine updated asynchronously to \(bestEngine.engineName) based on availability.")
        }
    }

    func setCurrentEngine(engineId: String) {
        guard let newEngine = availableEngines.first(where: { $0.engineId == engineId }) else {
            print("Error: TTS Engine with ID '\(engineId)' not found.")
            return
        }
        currentEngine = newEngine

        // Update settings
        var settings = storageManager.loadTTSSettings() ?? TTSSettings.defaultSettings
        settings.currentEngineId = engineId
        storageManager.saveTTSSettings(settings)

        print("TTS Engine changed to: \(currentEngine.engineName)")
    }

    // Method to play a sample sound using the current engine
    // This demonstrates how the settings page might trigger a test sound.
    func playSample(text: String, languageCode: String, voiceIdentifier: String?, completion: @escaping (Error?) -> Void) {
        Task { // Make playSample async internally to check currentEngine.isAvailable
            print("TTSManager: Playing sample '\(text)' with engine '\(currentEngine.engineName)'")
            
            guard await currentEngine.isAvailable else { // Await the async property
                let error = NSError(domain: "TTSManager",
                                    code: -1,
                                    userInfo: [NSLocalizedDescriptionKey: "Engine '\(currentEngine.engineName)' is not available (e.g., SDK not integrated or configured)."])
                print("Error: \(error.localizedDescription)")
                completion(error)
                return
            }

            // This part needs to run on MainActor because isSpeaking is @Published
            // and currentEngine.speak is async but its completion might touch UI indirectly.
            // Since TTSManager is already @MainActor, direct assignment to isSpeaking is fine.
            // The speak method itself is async.
            self.isSpeaking = true
            await currentEngine.speak(text: text, languageCode: languageCode, voiceIdentifier: voiceIdentifier) { error in
                // Completion needs to be on main thread if it updates UI.
                // The speak completion itself should handle its MainActor needs if it updates its own engine's state.
                // This completion is for the caller of playSample.
                Task { @MainActor in
                    self.isSpeaking = false
                    completion(error)
                }
            }
        }
    }

    func stopSample() {
        Task { @MainActor in // Ensure isSpeaking is mutated on main actor
             self.isSpeaking = false
        }
        Task { // stopSpeaking itself is async
            await currentEngine.stopSpeaking()
        }
    }

    /// 播放样本（简化版本）
    /// - Parameters:
    ///   - text: 要播放的文本
    ///   - languageCode: 语言代码
    ///   - completion: 完成回调，返回错误（如果有）
    func playSample(text: String, languageCode: String, completion: @escaping (Error?) -> Void) {
        playSample(text: text, languageCode: languageCode, voiceIdentifier: nil, completion: completion)
    }

    // Function to get voices for the current engine and a specific language
    // This method is now async as getAvailableVoices on TTSEngine is async.
    func getVoicesForCurrentEngine(languageCode: String?) async -> [VoiceInfo] {
        return await currentEngine.getAvailableVoices(forLanguageCode: languageCode)
    }

    // MARK: - TTSManagerProtocol Implementation

    /// 获取可用的声音
    func getAvailableVoices(for languageCode: String) async -> [TTSVoice] {
        return await currentEngine.getAvailableVoices(forLanguageCode: languageCode).map { voiceInfo in
            return TTSVoice(
                id: voiceInfo.id,
                name: voiceInfo.name,
                languageCode: voiceInfo.languageCode
            )
        }
    }

    /// 获取可用的语言
    func getAvailableLanguages() async -> [String] {
        var languages = Set<String>()

        for engine in availableEngines {
            // Need to await here as getAvailableVoices is async
            let voices = await engine.getAvailableVoices(forLanguageCode: nil)
            for voice in voices {
                languages.insert(voice.languageCode)
            }
        }
        return Array(languages).sorted()
    }

    /// 初始化TTS引擎
    func initializeEngines() {
        // 引擎已在初始化方法中初始化
        print("Engines already initialized")
    }

    /// 保存TTS设置
    func saveSettings() {
        var settings = storageManager.loadTTSSettings() ?? TTSSettings.defaultSettings
        settings.currentEngineId = currentEngine.engineId
        storageManager.saveTTSSettings(settings)
    }

    /// 加载TTS设置
    func loadSettings() {
        let settings = storageManager.loadTTSSettings() ?? TTSSettings.defaultSettings
        // Setting currentEngine here might conflict with async update.
        // Let async update handle the definitive engine choice.
        if availableEngines.first(where: { $0.engineId == settings.currentEngineId }) != nil {
             // Do not just set it, let updateEngineBasedOnAvailability decide
             // self.currentEngine = engine 
             // Instead, we can trigger the async update if needed
             Task {
                await self.updateEngineBasedOnAvailability()
             }
        } else {
            // If no stored engine or stored engine not found, ensure async update runs
            Task {
                await self.updateEngineBasedOnAvailability()
            }
        }
    }
}
