import Foundation
import AVFoundation
// 注意：這裡假設 KokoroTTS 已經作為依賴項添加到項目中
// 如果您使用 Swift Package Manager，請確保已添加 https://github.com/mlalma/kokoro-ios

/// Kokoro TTS 引擎 - 使用 MLX-Audio 的 Swift 實現
/// 提供高質量的離線神經網絡語音合成
@MainActor // KokoroTTSEngine manages AVAudioPlayer and UI-related state.
class KokoroTTSEngine: NSObject, TTSEngine, AVAudioPlayerDelegate {
    let engineId: String = "kokoro_tts"
    let engineName: String = "Kokoro Neural TTS"

    // 檢查是否在 Apple Silicon 設備上運行
    var isAvailable: Bool {
        get async {
            #if targetEnvironment(simulator)
            // 模擬器上可能無法正常工作
            return false
            #else
            // 檢查是否為 Apple Silicon
            #if arch(arm64)
            return true
            #else
            return false
            #endif
            #endif
        }
    }

    private var audioPlayer: AVAudioPlayer?
    private var completionHandler: (@Sendable (Error?) -> Void)?
    private var isModelLoaded: Bool = false

    // 用於跟踪語音狀態
    private var isSpeaking: Bool = false

    // 模型路徑
    private let modelPath: String

    /// 初始化 Kokoro TTS 引擎
    /// - Parameter modelPath: 模型權重文件的路徑（如果為 nil，將使用默認路徑）
    init(modelPath: String? = nil) {
        // 如果提供了模型路徑，使用提供的路徑，否則使用默認路徑
        if let path = modelPath {
            self.modelPath = path
        } else {
            // 嘗試在應用程序包中查找模型
            if let bundlePath = Bundle.main.path(forResource: "kokoro_model", ofType: "bin") {
                self.modelPath = bundlePath
            } else {
                // 如果在應用程序包中找不到，使用文檔目錄中的路徑
                let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                self.modelPath = documentsDirectory.appendingPathComponent("kokoro_model.bin").path
            }
        }

        super.init()

        // 嘗試加載模型
        loadModelIfNeeded()
    }

    /// 加載模型（如果尚未加載）
    private func loadModelIfNeeded() {
        // 檢查模型文件是否存在
        if FileManager.default.fileExists(atPath: modelPath) {
            // 這裡我們將在實際使用時加載模型，以避免在初始化時消耗太多資源
            isModelLoaded = true
        } else {
            print("Kokoro TTS 模型文件不存在: \(modelPath)")
            isModelLoaded = false
        }
    }

    /// 使用 Kokoro TTS 朗讀文本
    /// - Parameters:
    ///   - text: 要朗讀的文本
    ///   - languageCode: 語言代碼 (例如 "zh-CN", "en-US")
    ///   - voiceIdentifier: 可選的特定聲音標識符
    ///   - completion: 完成回調
    func speak(text: String, languageCode: String, voiceIdentifier: String?, completion: @escaping @Sendable (Error?) -> Void) async {
        guard await self.isAvailable else {
            await MainActor.run {
                completion(NSError(domain: self.engineId, code: -1, userInfo: [NSLocalizedDescriptionKey: "Kokoro TTS 在此設備上不可用 (需要 Apple Silicon)"]))
            }
            return
        }

        guard isModelLoaded else {
            await MainActor.run {
                completion(NSError(domain: self.engineId, code: -2, userInfo: [NSLocalizedDescriptionKey: "Kokoro TTS 模型未加載"]))
            }
            return
        }
        
        await MainActor.run {
            self.completionHandler = completion
            self.isSpeaking = true
        }

        // 生成臨時文件路徑
        let tempDir = FileManager.default.temporaryDirectory
        let outputURL = tempDir.appendingPathComponent("kokoro_output_\(UUID().uuidString).wav")

        do {
            try await self.generateSpeech(text: text, languageCode: languageCode, outputPath: outputURL.path)

            // 在主線程中播放生成的音頻
            await MainActor.run {
                do {
                    if self.audioPlayer?.isPlaying == true {
                        self.audioPlayer?.stop()
                    }
                    self.audioPlayer = try AVAudioPlayer(contentsOf: outputURL)
                    self.audioPlayer?.delegate = self
                    self.audioPlayer?.play()
                } catch {
                    self.isSpeaking = false
                    self.completionHandler?(error)
                    self.completionHandler = nil
                }
            }
        } catch {
            await MainActor.run {
                self.isSpeaking = false
                self.completionHandler?(error)
                self.completionHandler = nil
            }
        }
    }

    private func generateSpeech(text: String, languageCode: String, outputPath: String) async throws {
        // 模擬處理時間
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        if !FileManager.default.fileExists(atPath: modelPath) {
            throw NSError(domain: engineId, code: -3, userInfo: [NSLocalizedDescriptionKey: "模型文件不存在"])
        }
        // For mock purposes, create a dummy file if it doesn't exist.
        // In a real scenario, the Kokoro library would create this.
        if !FileManager.default.fileExists(atPath: outputPath) {
            // Create a simple valid WAV file header and a tiny bit of silent audio data for testing.
            // This is a placeholder and not a real TTS output.
            let headerSize = 44
            var dummyWavData = Data(count: headerSize) // Placeholder for WAV header
            // Actual WAV header would be more complex. This is just to make AVAudioPlayer happy.
            // RIFF chunk descriptor
            dummyWavData[0..<4] = "RIFF".data(using: .ascii)!
            dummyWavData[4..<8] = UInt32(36).littleEndianData // ChunkSize (36 + data size, here 0 data size)
            dummyWavData[8..<12] = "WAVE".data(using: .ascii)!
            // fmt sub-chunk
            dummyWavData[12..<16] = "fmt ".data(using: .ascii)!
            dummyWavData[16..<20] = UInt32(16).littleEndianData // Subchunk1Size (16 for PCM)
            dummyWavData[20..<22] = UInt16(1).littleEndianData  // AudioFormat (1 for PCM)
            dummyWavData[22..<24] = UInt16(1).littleEndianData  // NumChannels (1 for mono)
            dummyWavData[24..<28] = UInt32(22050).littleEndianData // SampleRate
            dummyWavData[28..<32] = UInt32(22050 * 1 * 2).littleEndianData // ByteRate (SampleRate * NumChannels * BitsPerSample/8)
            dummyWavData[32..<34] = UInt16(1 * 2).littleEndianData // BlockAlign (NumChannels * BitsPerSample/8)
            dummyWavData[34..<36] = UInt16(16).littleEndianData // BitsPerSample
            // data sub-chunk
            dummyWavData[36..<40] = "data".data(using: .ascii)!
            dummyWavData[40..<44] = UInt32(0).littleEndianData // Subchunk2Size (data size, 0 for now)
            
            try dummyWavData.write(to: URL(fileURLWithPath: outputPath))
            print("Created dummy WAV file at \(outputPath) for Kokoro TTS mock.")
        }
    }

    /// 獲取可用的聲音
    /// - Parameter forLanguageCode: 可選的語言代碼過濾器
    /// - Returns: 可用聲音的數組
    func getAvailableVoices(forLanguageCode: String?) async -> [VoiceInfo] {
        // Kokoro TTS 目前只有一個聲音
        var quality: AVSpeechSynthesisVoiceQuality? = nil
        var gender: AVSpeechSynthesisVoiceGender? = nil

        if #available(iOS 13.0, *) {
            quality = .premium // 高級質量
            gender = .unspecified // 未指定性別
        }

        return [
            VoiceInfo(id: "kokoro_default",
                      name: "Kokoro Default",
                      languageCode: "mul", // 多語言
                      quality: quality,
                      gender: gender)
        ]
    }

    /// 停止當前朗讀
    func stopSpeaking() async {
        await MainActor.run {
            if self.audioPlayer?.isPlaying == true {
                self.audioPlayer?.stop()
                self.isSpeaking = false
                if let completion = self.completionHandler {
                    let error = NSError(domain: self.engineId, code: -99, userInfo: [NSLocalizedDescriptionKey: "語音被用戶停止"])
                    completion(error)
                    self.completionHandler = nil
                }
            }
        }
    }

    // MARK: - AVAudioPlayerDelegate

    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            self.isSpeaking = false
            if flag {
                self.completionHandler?(nil)
            } else {
                self.completionHandler?(NSError(domain: self.engineId, code: -6, userInfo: [NSLocalizedDescriptionKey: "Audio playback failed."]))
            }
            self.completionHandler = nil
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            self.isSpeaking = false
            self.completionHandler?(error ?? NSError(domain: self.engineId, code: -7, userInfo: [NSLocalizedDescriptionKey: "Audio decoding error."]))
            self.completionHandler = nil
        }
    }
}

// Helper extension for Int to Data conversion (for WAV header)
extension FixedWidthInteger {
    var littleEndianData: Data {
        var value = littleEndian
        return Data(bytes: &value, count: MemoryLayout<Self>.size)
    }
}
