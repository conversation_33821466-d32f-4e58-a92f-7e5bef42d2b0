import Foundation
import AVFoundation
import Combine

/// Apple Neural TTS Engine - 使用 iOS 內置的神經網絡 TTS 功能
/// 從 iOS 13 開始，Apple 提供了高質量的神經網絡聲音
@MainActor
public class AppleNeuralTTSEngine: NSObject, TTSEngine, AVSpeechSynthesizerDelegate {
    public let engineId: String = "apple_neural"
    public let engineName: String = "Apple Neural Voices"

    // 神經網絡 TTS 在 iOS 13+ 上可用
    var isAvailable: Bool {
        if #available(iOS 13.0, *) {
            return true
        }
        return false
    }

    private let synthesizer = AVSpeechSynthesizer()
    private var completionHandler: ((Error?) -> Void)?

    // 用於跟踪語音狀態
    @Published private(set) var isSpeaking: Bool = false

    override init() {
        super.init()
        synthesizer.delegate = self
    }

    /// 使用 Apple 神經網絡 TTS 朗讀文本
    /// - Parameters:
    ///   - text: 要朗讀的文本
    ///   - languageCode: 語言代碼 (例如 "zh-CN", "en-US")
    ///   - voiceIdentifier: 可選的特定聲音標識符
    ///   - completion: 完成回調
    func speak(text: String, languageCode: String, voiceIdentifier: String?, completion: @escaping (Error?) -> Void) {
        guard isAvailable else {
            completion(NSError(domain: engineId, code: -1, userInfo: [NSLocalizedDescriptionKey: "Apple Neural TTS 在此設備上不可用 (需要 iOS 13+)"]))
            return
        }

        self.completionHandler = completion
        self.isSpeaking = true

        let utterance = AVSpeechUtterance(string: text)

        // 選擇聲音 - 優先使用指定的聲音標識符，然後嘗試找到神經網絡聲音
        if let voiceId = voiceIdentifier, let voice = AVSpeechSynthesisVoice(identifier: voiceId) {
            utterance.voice = voice
        } else {
            // 嘗試找到指定語言的神經網絡聲音
            utterance.voice = findBestVoiceForLanguage(languageCode)
        }

        // 調整語速和音調以獲得更自然的效果
        utterance.rate = AVSpeechUtteranceDefaultSpeechRate * 0.5 // 較慢的語速聽起來更自然
        utterance.pitchMultiplier = 1.0 // 標準音調
        utterance.volume = 1.0 // 最大音量

        // 確保在開始新的語音之前停止任何正在進行的語音
        if synthesizer.isSpeaking {
            synthesizer.stopSpeaking(at: .immediate)
        }

        // 開始朗讀
        synthesizer.speak(utterance)
    }

    /// 獲取可用的聲音
    /// - Parameter forLanguageCode: 可選的語言代碼過濾器
    /// - Returns: 可用聲音的數組
    func getAvailableVoices(forLanguageCode: String?) -> [VoiceInfo] {
        var voices = AVSpeechSynthesisVoice.speechVoices()

        // 過濾神經網絡聲音 (如果可用)
        if #available(iOS 13.0, *) {
            voices = voices.filter { voice in
                // 在 iOS 13+ 上，我們可以檢查聲音質量
                return voice.quality == .enhanced || voice.quality == .premium
            }
        }

        // 如果提供了語言代碼，則按語言過濾
        if let langCode = forLanguageCode, !langCode.isEmpty {
            voices = voices.filter { $0.language.starts(with: langCode) }
        }

        // 將 AVSpeechSynthesisVoice 轉換為 VoiceInfo
        return voices.map {
            VoiceInfo(id: $0.identifier,
                      name: $0.name,
                      languageCode: $0.language,
                      quality: $0.quality,
                      gender: $0.gender)
        }
    }

    /// 停止當前朗讀
    func stopSpeaking() {
        if synthesizer.isSpeaking {
            synthesizer.stopSpeaking(at: .immediate)
            isSpeaking = false

            // 通知取消
            if let completion = completionHandler {
                let error = NSError(domain: engineId, code: -99, userInfo: [NSLocalizedDescriptionKey: "語音被用戶停止"])
                completion(error)
                completionHandler = nil
            }
        }
    }

    // MARK: - 輔助方法

    /// 為指定語言找到最佳的神經網絡聲音
    /// - Parameter languageCode: 語言代碼
    /// - Returns: 最佳的聲音
    private func findBestVoiceForLanguage(_ languageCode: String) -> AVSpeechSynthesisVoice {
        var bestVoice: AVSpeechSynthesisVoice? = nil

        // 獲取指定語言的所有聲音
        let voices = AVSpeechSynthesisVoice.speechVoices().filter { $0.language.starts(with: languageCode) }

        if #available(iOS 13.0, *) {
            // 首先嘗試找到高質量的神經網絡聲音
            if let premiumVoice = voices.first(where: { $0.quality == .premium }) {
                bestVoice = premiumVoice
            } else if let enhancedVoice = voices.first(where: { $0.quality == .enhanced }) {
                bestVoice = enhancedVoice
            }
        }

        // 如果沒有找到高質量聲音，則使用默認聲音
        if let voice = bestVoice {
            return voice
        } else {
            // 確保返回一個非可選類型
            return AVSpeechSynthesisVoice(language: languageCode)!
        }
    }

    // MARK: - AVSpeechSynthesizerDelegate

    nonisolated public func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didStart utterance: AVSpeechUtterance) {
        Task { @MainActor in self.isSpeaking = true }
    }

    nonisolated public func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        Task { @MainActor in
            self.isSpeaking = false
            self.completionHandler?(nil)
            self.completionHandler = nil
        }
    }

    nonisolated public func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        Task { @MainActor in
            self.isSpeaking = false
            let error = NSError(domain: self.engineId, code: -2, userInfo: [NSLocalizedDescriptionKey: "語音被取消"])
            self.completionHandler?(error)
            self.completionHandler = nil
        }
    }

    nonisolated public func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didPause utterance: AVSpeechUtterance) {
        Task { @MainActor in self.isSpeaking = false }
    }

    nonisolated public func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didContinue utterance: AVSpeechUtterance) {
        Task { @MainActor in self.isSpeaking = true }
    }

    nonisolated public func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, willSpeakRangeOfSpeechString characterRange: NSRange, utterance: AVSpeechUtterance) {
        // Can be used for subtitle highlighting, etc.
        // If this needs to update @MainActor properties, wrap in Task { @MainActor ... }
    }
}

// MARK: - 常用神經網絡聲音標識符

extension AppleNeuralTTSEngine {
    /// 常用的 Apple 神經網絡聲音標識符
    struct NeuralVoices {
        // 中文聲音
        static let chineseFemale = "com.apple.voice.premium.zh-CN.meijia" // 美佳 (女聲)
        static let chineseMale = "com.apple.voice.premium.zh-CN.tiantian" // 天天 (男聲)

        // 英文聲音
        static let englishFemaleSiri = "com.apple.voice.premium.en-US.Siri" // Siri (女聲)
        static let englishFemale = "com.apple.voice.premium.en-US.Samantha" // Samantha (女聲)
        static let englishMale = "com.apple.voice.premium.en-US.Alex" // Alex (男聲)

        // 日文聲音
        static let japaneseFemale = "com.apple.voice.premium.ja-JP.kyoko" // 京子 (女聲)

        // 韓文聲音
        static let koreanFemale = "com.apple.voice.premium.ko-KR.yuna" // 유나 (女聲)

        // 獲取指定語言的默認神經網絡聲音
        static func defaultVoice(for languageCode: String) -> String? {
            switch languageCode {
            case _ where languageCode.starts(with: "zh"):
                return chineseFemale
            case _ where languageCode.starts(with: "en"):
                return englishFemale
            case _ where languageCode.starts(with: "ja"):
                return japaneseFemale
            case _ where languageCode.starts(with: "ko"):
                return koreanFemale
            default:
                return nil
            }
        }
    }
}