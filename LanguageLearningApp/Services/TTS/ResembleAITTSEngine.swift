import Foundation
import AVFoundation

// TODO: Replace with actual Resemble AI API details
private let RESEMBLE_API_TOKEN = "YOUR_RESEMBLE_API_TOKEN"
// Resemble AI often uses project and voice UUIDs. These might be configurable or fetched.
// For simplicity, let's assume a default voice UID might be used or passed in voiceIdentifier.
// private let RESEMBLE_PROJECT_UID = "YOUR_PROJECT_UID"
// private let RESEMBLE_DEFAULT_VOICE_UID = "YOUR_DEFAULT_VOICE_UID"
private let RESEMBLE_API_ENDPOINT_TTS = "https://app.resemble.ai/api/v2/projects/{project_uuid}/clips" // Placeholder, needs project_uuid
private let RESEMBLE_API_ENDPOINT_VOICES = "https://app.resemble.ai/api/v2/voices" // Placeholder

@MainActor // ResembleAITTSEngine manages AVAudioPlayer and UI-related state.
class ResembleAITTSEngine: NSObject, TTSEngine, AVAudioPlayerDelegate {
    let engineId: String = "resembleai_real"
    let engineName: String = "Resemble AI (Real)"

    var isAvailable: Bool {
        return RESEMBLE_API_TOKEN != "YOUR_RESEMBLE_API_TOKEN" && !RESEMBLE_API_TOKEN.isEmpty
    }

    private var audioPlayer: AVAudioPlayer?
    private var currentSpeakCompletion: (@Sendable (Error?) -> Void)?
    private let session: URLSession // Make session non-mutable if possible, or ensure thread-safe access

    override init() {
        let configuration = URLSessionConfiguration.default
        configuration.httpAdditionalHeaders = [
            "Authorization": "Token \(RESEMBLE_API_TOKEN)", // Common for Resemble AI
            "Content-Type": "application/json"
        ]
        self.session = URLSession(configuration: configuration)
        super.init()
    }

    func speak(text: String, languageCode: String, voiceIdentifier: String?, completion: @escaping @Sendable (Error?) -> Void) async {
        guard await isAvailable else {
            await MainActor.run { // Ensure completion is called on main actor if it interacts with UI
                completion(NSError(domain: self.engineId, code: -1, userInfo: [NSLocalizedDescriptionKey: "\(self.engineName) is not available. Check API token."]))
            }
            return
        }

        // Resemble AI usually requires a voice UUID. This could come from voiceIdentifier.
        // It also often requires a project UUID. This might need to be configured globally or per request.
        // For this example, let's assume voiceIdentifier is the Resemble voice_uuid.
        // And project_uuid needs to be configured (e.g. a constant or fetched).
        // Let's use a placeholder project_uuid for the URL construction.
        let projectUUID = "YOUR_PROJECT_UID_CONFIGURED_ELSEWHERE" // This needs to be set

        guard let voiceUUID = voiceIdentifier else {
            completion(NSError(domain: self.engineId, code: -2, userInfo: [NSLocalizedDescriptionKey: "Voice identifier (voice_uuid) is required for Resemble AI TTS."]))
            return
        }
        
        self.currentSpeakCompletion = completion

        // 1. Construct the request to Resemble AI's API
        //    This typically involves a POST request to create a "clip".
        //    The API might return a URL to the audio file, or the audio data directly.
        //    Let's assume it returns a URL to the audio file.
        
        let ttsEndpoint = RESEMBLE_API_ENDPOINT_TTS.replacingOccurrences(of: "{project_uuid}", with: projectUUID)
        guard let requestUrl = URL(string: ttsEndpoint) else {
            completion(NSError(domain: self.engineId, code: -3, userInfo: [NSLocalizedDescriptionKey: "Invalid Resemble AI API endpoint."]))
            return
        }

        var request = URLRequest(url: requestUrl)
        request.httpMethod = "POST"
        
        let body: [String: Any] = [
            "voice_uuid": voiceUUID,
            "data": text,
            "title": "LanguageLearningApp Speech", // Optional title
            "is_public": false,
            "is_archived": false
            // "callback_uri": "YOUR_CALLBACK_URL" // If using callbacks for long synthesis
        ]

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: body, options: [])
        } catch {
            completion(error)
            return
        }

        // 2. Perform the network request to create the clip
        let task = session.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            if let error = error {
                self.completeWithError(error)
                return
            }

            guard let httpResponse = response as? HTTPURLResponse else {
                self.completeWithError(NSError(domain: self.engineId, code: -10, userInfo: [NSLocalizedDescriptionKey: "Invalid response from server."]))
                return
            }
            
            // Resemble API for clip creation usually returns 201 Created or 200 OK
            guard (200...299).contains(httpResponse.statusCode) else {
                var errorMessage = "Resemble AI clip creation failed with status \(httpResponse.statusCode)."
                if let responseData = data, let errorDetail = String(data: responseData, encoding: .utf8) {
                    errorMessage += " Details: \(errorDetail)"
                }
                self.completeWithError(NSError(domain: self.engineId, code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage]))
                return
            }

            guard let responseData = data else {
                self.completeWithError(NSError(domain: self.engineId, code: -5, userInfo: [NSLocalizedDescriptionKey: "No data received from Resemble AI clip creation."]))
                return
            }

            // Parse the response to get the audio URL (assuming 'url' or 'audio_src' field)
            do {
                if let jsonResponse = try JSONSerialization.jsonObject(with: responseData, options: []) as? [String: Any],
                   let clipInfo = jsonResponse["item"] as? [String: Any], // Resemble often wraps in 'item' or 'clip'
                   let audioUrlString = clipInfo["audio_src"] as? String, // Or "url", "link" etc.
                   let audioUrl = URL(string: audioUrlString) {
                    Task { // Wrap the async call in a Task
                        await self.fetchAndPlayAudio(from: audioUrl)
                    }
                } else {
                    self.completeWithError(NSError(domain: self.engineId, code: -8, userInfo: [NSLocalizedDescriptionKey: "Could not parse audio URL from Resemble AI response."]))
                }
            } catch {
                self.completeWithError(error)
            }
        }
        task.resume()
    }
    
    private func fetchAndPlayAudio(from url: URL) {
        // Create a new URLSession task to download the audio
        // Use a session without the default Resemble AI headers for fetching the audio file
        let audioDownloadSession = URLSession(configuration: .default) // This session is local to the method, which is fine.
        let downloadTask = audioDownloadSession.dataTask(with: url) { [weak self] audioData, audioResponse, audioError in
            guard let self = self else { return }

            if let audioError = audioError {
                self.completeWithError(audioError)
                return
            }
            
            guard let httpAudioResponse = audioResponse as? HTTPURLResponse, httpAudioResponse.statusCode == 200 else {
                let statusCode = (audioResponse as? HTTPURLResponse)?.statusCode ?? -1
                self.completeWithError(NSError(domain: self.engineId, code: statusCode, userInfo: [NSLocalizedDescriptionKey: "Failed to download audio from Resemble AI with status \(statusCode)."]))
                return
            }

            guard let receivedAudioData = audioData else {
                self.completeWithError(NSError(domain: self.engineId, code: -9, userInfo: [NSLocalizedDescriptionKey: "No audio data downloaded from Resemble AI."]))
                return
            }

            Task { @MainActor in // Switch to MainActor for UI and audio player operations
                do {
                    try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
                    try AVAudioSession.sharedInstance().setActive(true)
                    
                    self.audioPlayer = try AVAudioPlayer(data: receivedAudioData)
                    self.audioPlayer?.delegate = self // self is Sendable, delegate assignment is fine
                    self.audioPlayer?.play()
                } catch {
                    self.completeWithError(error) // completeWithError already dispatches to main
                }
            }
        }
        downloadTask.resume()
    }

    private func completeWithError(_ error: Error) {
        Task { @MainActor in // Ensure completion is called on main actor
            self.currentSpeakCompletion?(error)
            self.currentSpeakCompletion = nil
        }
    }

    func getAvailableVoices(forLanguageCode: String?) async -> [VoiceInfo] {
        // This would typically involve an API call to Resemble AI to fetch voices.
        // Resemble AI voices are often specific to a project or account.
        // For now, returning a placeholder.
        // Actual implementation:
        // 1. Make a GET request to RESEMBLE_API_ENDPOINT_VOICES (or similar).
        // 2. Parse the JSON response.
        // 3. Map Resemble voice data to VoiceInfo structs. (Note: Resemble might not have BCP-47 directly, might need mapping)
        
        print("\(self.engineName): Fetching available voices from Resemble AI API (not implemented, returning placeholders).")
        
        // Placeholder voices - Resemble voice IDs are UUIDs.
        // Language codes here are illustrative; Resemble's voice metadata might be different.
        var voices: [VoiceInfo] = []
        if forLanguageCode == nil || forLanguageCode?.starts(with: "en") == true {
            voices.append(VoiceInfo(id: "your-resemble-english-voice-uuid-1", name: "Resemble Voice EN 1", languageCode: "en-US"))
            voices.append(VoiceInfo(id: "your-resemble-english-voice-uuid-2", name: "Resemble Voice EN 2", languageCode: "en-US"))
        }
        // Add more based on what Resemble offers or how their voices are structured.
        return voices
    }

    func stopSpeaking() async {
        await MainActor.run { // audioPlayer and currentSpeakCompletion accessed on MainActor
            audioPlayer?.stop()
            if let completion = currentSpeakCompletion {
                let error = NSError(domain: self.engineId, code: -99, userInfo: [NSLocalizedDescriptionKey: "Speech stopped by user."])
                completion(error)
                currentSpeakCompletion = nil
            }
        }
        // Cancel ongoing URLSession tasks if any (clip creation, audio download)
        // session.getAllTasks { tasks in tasks.forEach { $0.cancel() } }
        // Also cancel tasks from audioDownloadSession if it's a class member
    }

    // MARK: - AVAudioPlayerDelegate
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            if flag {
                self.currentSpeakCompletion?(nil)
            } else {
                self.currentSpeakCompletion?(NSError(domain: self.engineId, code: -6, userInfo: [NSLocalizedDescriptionKey: "Audio playback failed."]))
            }
            self.currentSpeakCompletion = nil
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            self.currentSpeakCompletion?(error ?? NSError(domain: self.engineId, code: -7, userInfo: [NSLocalizedDescriptionKey: "Audio decoding error."]))
            self.currentSpeakCompletion = nil
        }
    }
}
