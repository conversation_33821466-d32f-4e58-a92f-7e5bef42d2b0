import AVFoundation
import Combine

@MainActor // SystemTTSEngine interacts with AVSpeechSynthesizer, which should be on the main thread.
class SystemTTSEngine: NSObject, TTSEngine, AVSpeechSynthesizerDelegate {
    let engineId: String = "system_default"
    let engineName: String = "System Default (iOS)"
    var isAvailable: Bool = true // System TTS is always available. Make async if it involves checks.

    private let synthesizer = AVSpeechSynthesizer()
    private var completionHandler: (@Sendable (Error?) -> Void)?
    
    // To keep track of speaking state for the delegate methods if needed by UI
    // @Published var isSpeaking: Bool = false

    override init() {
        super.init()
        synthesizer.delegate = self
    }

    func speak(text: String, languageCode: String, voiceIdentifier: String?, completion: @escaping @Sendable (Error?) -> Void) async {
        await MainActor.run { // Ensure synthesizer and completionHandler are accessed on main actor
            self.completionHandler = completion
            // self.isSpeaking = true // Update speaking state
            
            let utterance = AVSpeechUtterance(string: text)

            if let voiceId = voiceIdentifier {
                utterance.voice = AVSpeechSynthesisVoice(identifier: voiceId)
            } else {
                // Fallback to language code if no specific voice ID is provided
                utterance.voice = AVSpeechSynthesisVoice(language: languageCode)
            }
            
            // You might want to set default rate/pitch if not specified or configurable elsewhere
            // utterance.rate = AVSpeechUtteranceDefaultSpeechRate
            // utterance.pitchMultiplier = 1.0

            // Ensure any previous speech is stopped before starting a new one if overlapping is not desired.
            if synthesizer.isSpeaking {
                synthesizer.stopSpeaking(at: .immediate)
            }
            
            synthesizer.speak(utterance)
        }
    }

    func getAvailableVoices(forLanguageCode: String?) async -> [VoiceInfo] {
        // This can be computationally intensive, consider if it needs to be off the main thread
        // For now, assume it's quick enough or AVSpeechSynthesisVoice.speechVoices() is safe.
        var voices = AVSpeechSynthesisVoice.speechVoices()
        if let langCode = forLanguageCode, !langCode.isEmpty {
            voices = voices.filter { $0.language.starts(with: langCode) }
        }
        
        return voices.map {
            VoiceInfo(id: $0.identifier, 
                      name: $0.name, 
                      languageCode: $0.language, 
                      quality: $0.quality, 
                      gender: $0.gender)
        }
    }

    func stopSpeaking() async {
        await MainActor.run { // Ensure synthesizer and completionHandler are accessed on main actor
            if synthesizer.isSpeaking {
                synthesizer.stopSpeaking(at: .immediate)
                // self.isSpeaking = false // Update speaking state
                // No explicit completion here as per AVSpeechSynthesizerDelegate protocol for stop
                // but you might want to call the current completionHandler with a cancellation error
                // self.completionHandler?(TTSError.cancelled) // Define TTSError if needed
                // If stopping should trigger completion, do it here:
                // let error = NSError(domain: engineId, code: -99, userInfo: [NSLocalizedDescriptionKey: "Speech stopped by user."])
                // self.completionHandler?(error)
                // self.completionHandler = nil
            }
        }
    }

    // MARK: - AVSpeechSynthesizerDelegate
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didStart utterance: AVSpeechUtterance) {
        // Task { @MainActor in ... } // Body if needed
    }

    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        Task { @MainActor in
            // self.isSpeaking = false
            self.completionHandler?(nil)
            self.completionHandler = nil
        }
    }

    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didPause utterance: AVSpeechUtterance) {
        // Task { @MainActor in ... } // Body if needed
    }

    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didContinue utterance: AVSpeechUtterance) {
        // Task { @MainActor in ... } // Body if needed
    }

    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        Task { @MainActor in
            // self.isSpeaking = false
            self.completionHandler?(TTSError.cancelled) // Or nil if cancel is not an error
            self.completionHandler = nil
        }
    }
    
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, willSpeakRangeOfSpeechString characterRange: NSRange, utterance: AVSpeechUtterance) {
        // Task { @MainActor in ... } // Body if needed
    }
}

// Optional: Define custom errors if you want to pass specific TTS errors
enum TTSError: Error {
    case cancelled
    case engineError(String)
} 