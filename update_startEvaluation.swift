#!/usr/bin/env swift

import Foundation

let filePath = "LanguageLearningApp/Features/Evaluation/Services/EvaluationService.swift"
let fileContent = try String(contentsOfFile: filePath, encoding: .utf8)

let oldCode = """
                // 尝试解码为 EvaluationResponse
                if let response = try? decoder.decode(EvaluationResponse.self, from: data) {
                    print("成功解码为EvaluationResponse")
                    return response.data
                }

                // 尝试直接解码为 Evaluation
                if let evaluation = try? decoder.decode(Evaluation.self, from: data) {
                    print("成功直接解码为Evaluation")
                    return evaluation
                }
"""

let newCode = """
                // 尝试解码为 EvaluationAPIResponse
                if let apiResponse = try? decoder.decode(EvaluationAPIResponse.self, from: data) {
                    print("成功解码为EvaluationAPIResponse")
                    if let apiEvaluation = apiResponse.data {
                        let evaluation = apiEvaluation.toEvaluation()
                        print("评估包含 \\(evaluation.sections.count) 个部分，总共 \\(evaluation.totalQuestions) 个问题")
                        return evaluation
                    } else {
                        print("EvaluationAPIResponse中data字段为nil")
                    }
                } else {
                    print("无法解码为EvaluationAPIResponse")
                }
                
                // 尝试解码为 EvaluationResponse
                if let response = try? decoder.decode(EvaluationResponse.self, from: data) {
                    print("成功解码为EvaluationResponse")
                    return response.data
                }

                // 尝试直接解码为 Evaluation
                if let evaluation = try? decoder.decode(Evaluation.self, from: data) {
                    print("成功直接解码为Evaluation")
                    return evaluation
                }
"""

let updatedContent = fileContent.replacingOccurrences(of: oldCode, with: newCode)

try updatedContent.write(toFile: filePath, atomically: true, encoding: .utf8)
print("File updated successfully")
