# 手动调整指南

如果自动脚本修复后仍然有编译错误，请尝试以下手动调整：

## 1. 确保正确导入所需模块

在有错误的文件顶部添加必要的导入语句，例如：

```swift
import Foundation
import Combine
import SwiftUI
```

## 2. 解决 PracticeSession 歧义

如果 `PracticeSession` 仍然有歧义，请在使用时明确指定命名空间：

```swift
// 修改前
func getPracticeHistory(userID: UUID) -> AnyPublisher<[PracticeSession], Error>

// 修改后
func getPracticeHistory(userID: UUID) -> AnyPublisher<[Features.DailyPractice.Models.PracticeSession], Error>
```

或者在文件顶部添加类型别名：

```swift
typealias PracticeSession = Features.DailyPractice.Models.PracticeSession
```

## 3. 解决 APIClientProtocol 歧义

类似地，明确指定 APIClientProtocol 的命名空间：

```swift
// 修改前
private let apiClient: APIClientProtocol

// 修改后
private let apiClient: API.APIClientProtocol
```

或添加类型别名：

```swift
typealias APIClientProtocol = API.APIClientProtocol
```

## 4. 修复协议一致性问题

确保 PracticeRepository 实现了 RepositoryProtocol 所需的所有方法：

```swift
extension PracticeRepository: RepositoryProtocol {
    // 实现所有必需的方法
    public func fetch() -> AnyPublisher<[PracticeSession], Error> {
        // 实现代码
    }
    
    public func save(_ item: PracticeSession) -> AnyPublisher<PracticeSession, Error> {
        // 实现代码
    }
}
```

## 5. 修复闭包类型注解

为所有闭包参数添加明确的类型注解：

```swift
// 修改前
func someFunction(completion: @escaping () {
    // ...
}

// 修改后
func someFunction(completion: @escaping (Result<SomeType, Error>) -> Void) {
    // ...
}
```

## 6. 修复 .stringsdata 文件冲突

如果在清理派生数据并重新构建后仍出现 .stringsdata 文件冲突，请尝试：

1. 确保 target 的 "Copy Bundle Resources" 阶段没有重复的资源文件
2. 删除整个 DerivedData 目录（可能需要管理员权限）:
   ```
   sudo rm -rf ~/Library/Developer/Xcode/DerivedData/LanguageLearningApp-*
   ```

## 7. 检查项目结构

确保项目文件结构符合预期：

1. 只在 Features/DailyPractice/Models 中定义 PracticeSession
2. 只在 API 目录中定义 APIClientProtocol
3. 不同模块之间使用明确的引用路径

如果仍然有问题，可能需要考虑将项目转换为模块化结构，使用 Swift Package Manager 更好地管理依赖。 