#!/bin/bash

echo "🔍 深度代码分析 - 识别重构点..."

# 创建分析报告目录
mkdir -p analysis_reports
REPORT_DIR="analysis_reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "📊 生成分析报告到: $REPORT_DIR"

# 1. 分析重复服务文件
echo "🔍 1. 分析重复服务文件..."
echo "=== 重复服务文件分析 ===" > "$REPORT_DIR/duplicate_services_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/duplicate_services_$TIMESTAMP.txt"

echo "旧服务文件 (需要删除):" >> "$REPORT_DIR/duplicate_services_$TIMESTAMP.txt"
find LanguageLearningApp/Services -name "*Service.swift" -type f >> "$REPORT_DIR/duplicate_services_$TIMESTAMP.txt" 2>/dev/null || echo "Services目录不存在或为空" >> "$REPORT_DIR/duplicate_services_$TIMESTAMP.txt"

echo "" >> "$REPORT_DIR/duplicate_services_$TIMESTAMP.txt"
echo "新服务文件 (Features中的Manager):" >> "$REPORT_DIR/duplicate_services_$TIMESTAMP.txt"
find LanguageLearningApp/Features -name "*Manager.swift" -type f >> "$REPORT_DIR/duplicate_services_$TIMESTAMP.txt" 2>/dev/null || echo "Features目录中没有Manager文件" >> "$REPORT_DIR/duplicate_services_$TIMESTAMP.txt"

# 2. 分析单例模式使用
echo "🔍 2. 分析单例模式使用..."
echo "=== 单例模式使用分析 ===" > "$REPORT_DIR/singleton_usage_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/singleton_usage_$TIMESTAMP.txt"

echo "发现的单例使用:" >> "$REPORT_DIR/singleton_usage_$TIMESTAMP.txt"
grep -r "\.shared" LanguageLearningApp --include="*.swift" | head -20 >> "$REPORT_DIR/singleton_usage_$TIMESTAMP.txt" 2>/dev/null

echo "" >> "$REPORT_DIR/singleton_usage_$TIMESTAMP.txt"
echo "静态单例定义:" >> "$REPORT_DIR/singleton_usage_$TIMESTAMP.txt"
grep -r "static let shared" LanguageLearningApp --include="*.swift" >> "$REPORT_DIR/singleton_usage_$TIMESTAMP.txt" 2>/dev/null

# 3. 分析状态管理模式
echo "🔍 3. 分析状态管理模式..."
echo "=== 状态管理模式分析 ===" > "$REPORT_DIR/state_management_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/state_management_$TIMESTAMP.txt"

echo "@StateObject 使用:" >> "$REPORT_DIR/state_management_$TIMESTAMP.txt"
grep -r "@StateObject" LanguageLearningApp --include="*.swift" | wc -l >> "$REPORT_DIR/state_management_$TIMESTAMP.txt"
grep -r "@StateObject" LanguageLearningApp --include="*.swift" | head -10 >> "$REPORT_DIR/state_management_$TIMESTAMP.txt" 2>/dev/null

echo "" >> "$REPORT_DIR/state_management_$TIMESTAMP.txt"
echo "@ObservedObject 使用:" >> "$REPORT_DIR/state_management_$TIMESTAMP.txt"
grep -r "@ObservedObject" LanguageLearningApp --include="*.swift" | wc -l >> "$REPORT_DIR/state_management_$TIMESTAMP.txt"
grep -r "@ObservedObject" LanguageLearningApp --include="*.swift" | head -10 >> "$REPORT_DIR/state_management_$TIMESTAMP.txt" 2>/dev/null

echo "" >> "$REPORT_DIR/state_management_$TIMESTAMP.txt"
echo "@EnvironmentObject 使用:" >> "$REPORT_DIR/state_management_$TIMESTAMP.txt"
grep -r "@EnvironmentObject" LanguageLearningApp --include="*.swift" | wc -l >> "$REPORT_DIR/state_management_$TIMESTAMP.txt"
grep -r "@EnvironmentObject" LanguageLearningApp --include="*.swift" | head -10 >> "$REPORT_DIR/state_management_$TIMESTAMP.txt" 2>/dev/null

# 4. 分析异步模式使用
echo "🔍 4. 分析异步模式使用..."
echo "=== 异步模式使用分析 ===" > "$REPORT_DIR/async_patterns_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/async_patterns_$TIMESTAMP.txt"

echo "Combine Publisher 使用:" >> "$REPORT_DIR/async_patterns_$TIMESTAMP.txt"
grep -r "AnyPublisher" LanguageLearningApp --include="*.swift" | wc -l >> "$REPORT_DIR/async_patterns_$TIMESTAMP.txt"
grep -r "AnyPublisher" LanguageLearningApp --include="*.swift" | head -10 >> "$REPORT_DIR/async_patterns_$TIMESTAMP.txt" 2>/dev/null

echo "" >> "$REPORT_DIR/async_patterns_$TIMESTAMP.txt"
echo "async/await 使用:" >> "$REPORT_DIR/async_patterns_$TIMESTAMP.txt"
grep -r "async func\|await " LanguageLearningApp --include="*.swift" | wc -l >> "$REPORT_DIR/async_patterns_$TIMESTAMP.txt"
grep -r "async func" LanguageLearningApp --include="*.swift" | head -10 >> "$REPORT_DIR/async_patterns_$TIMESTAMP.txt" 2>/dev/null

# 5. 分析错误处理模式
echo "🔍 5. 分析错误处理模式..."
echo "=== 错误处理模式分析 ===" > "$REPORT_DIR/error_handling_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/error_handling_$TIMESTAMP.txt"

echo "错误类型定义:" >> "$REPORT_DIR/error_handling_$TIMESTAMP.txt"
grep -r "enum.*Error" LanguageLearningApp --include="*.swift" >> "$REPORT_DIR/error_handling_$TIMESTAMP.txt" 2>/dev/null

echo "" >> "$REPORT_DIR/error_handling_$TIMESTAMP.txt"
echo "错误处理使用:" >> "$REPORT_DIR/error_handling_$TIMESTAMP.txt"
grep -r "catch\|throw\|throws" LanguageLearningApp --include="*.swift" | wc -l >> "$REPORT_DIR/error_handling_$TIMESTAMP.txt"

# 6. 分析测试覆盖情况
echo "🔍 6. 分析测试覆盖情况..."
echo "=== 测试覆盖情况分析 ===" > "$REPORT_DIR/test_coverage_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/test_coverage_$TIMESTAMP.txt"

echo "测试文件统计:" >> "$REPORT_DIR/test_coverage_$TIMESTAMP.txt"
find LanguageLearningAppTests -name "*.swift" -type f | wc -l >> "$REPORT_DIR/test_coverage_$TIMESTAMP.txt"
echo "测试文件列表:" >> "$REPORT_DIR/test_coverage_$TIMESTAMP.txt"
find LanguageLearningAppTests -name "*.swift" -type f >> "$REPORT_DIR/test_coverage_$TIMESTAMP.txt" 2>/dev/null

echo "" >> "$REPORT_DIR/test_coverage_$TIMESTAMP.txt"
echo "Mock 实现:" >> "$REPORT_DIR/test_coverage_$TIMESTAMP.txt"
find LanguageLearningAppTests -name "*Mock*.swift" -type f >> "$REPORT_DIR/test_coverage_$TIMESTAMP.txt" 2>/dev/null

# 7. 分析依赖注入使用
echo "🔍 7. 分析依赖注入使用..."
echo "=== 依赖注入使用分析 ===" > "$REPORT_DIR/dependency_injection_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/dependency_injection_$TIMESTAMP.txt"

echo "DependencyContainer 使用:" >> "$REPORT_DIR/dependency_injection_$TIMESTAMP.txt"
grep -r "DependencyContainer" LanguageLearningApp --include="*.swift" >> "$REPORT_DIR/dependency_injection_$TIMESTAMP.txt" 2>/dev/null

echo "" >> "$REPORT_DIR/dependency_injection_$TIMESTAMP.txt"
echo "协议定义 (Protocol):" >> "$REPORT_DIR/dependency_injection_$TIMESTAMP.txt"
grep -r "protocol.*Protocol" LanguageLearningApp --include="*.swift" >> "$REPORT_DIR/dependency_injection_$TIMESTAMP.txt" 2>/dev/null

# 8. 生成重构优先级报告
echo "🔍 8. 生成重构优先级报告..."
echo "=== 重构优先级报告 ===" > "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"

echo "🔴 高优先级 - 架构统一化:" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "1. 删除重复服务文件 (Services/ 目录)" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "2. 移除单例模式，统一使用依赖注入" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "3. 统一状态管理模式" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "4. 统一异步模式 (async/await)" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"

echo "🟡 中优先级 - 代码质量:" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "1. 完善测试覆盖率" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "2. 统一错误处理机制" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "3. 性能优化" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"

echo "🟢 低优先级 - 可扩展性:" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "1. 模块化增强" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "2. 插件化架构" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"
echo "3. 多平台支持" >> "$REPORT_DIR/refactoring_priorities_$TIMESTAMP.txt"

# 9. 生成具体行动计划
echo "🔍 9. 生成具体行动计划..."
echo "=== 具体行动计划 ===" > "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"

echo "第一步: 清理重复文件" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "bash cleanup_old_files.sh" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"

echo "第二步: 更新依赖注入" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "1. 创建 ViewModelFactory" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "2. 更新 DependencyRegistry" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "3. 修改主应用入口" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"

echo "第三步: 统一状态管理" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "1. 重构所有 ViewModel 注入方式" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "2. 统一使用 @EnvironmentObject" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "3. 移除直接实例化" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"

echo "" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"
echo "详细实施步骤请参考: refactoring-implementation-plan.md" >> "$REPORT_DIR/action_plan_$TIMESTAMP.txt"

# 10. 生成总结报告
echo "📊 生成总结报告..."
echo "=== 深度重构分析总结 ===" > "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "分析时间: $(date)" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"

echo "📁 生成的分析报告:" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
ls -la "$REPORT_DIR"/*_$TIMESTAMP.txt >> "$REPORT_DIR/summary_$TIMESTAMP.txt"

echo "" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "🎯 关键发现:" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "1. 发现重复服务文件需要清理" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "2. 单例模式使用过多，需要统一到依赖注入" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "3. 状态管理模式混乱，需要标准化" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "4. 异步模式混合使用，需要统一到 async/await" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "5. 测试覆盖率不足，需要大幅提升" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"

echo "" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "📋 下一步行动:" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "1. 执行 cleanup_old_files.sh 清理重复文件" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "2. 按照 refactoring-implementation-plan.md 执行重构" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"
echo "3. 定期运行此分析脚本监控进度" >> "$REPORT_DIR/summary_$TIMESTAMP.txt"

echo "✅ 深度代码分析完成！"
echo "📊 分析报告保存在: $REPORT_DIR/"
echo "📋 查看总结报告: cat $REPORT_DIR/summary_$TIMESTAMP.txt"
echo ""
echo "🚀 下一步: 执行 bash cleanup_old_files.sh 开始重构"
