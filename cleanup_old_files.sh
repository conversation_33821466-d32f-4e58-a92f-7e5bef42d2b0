#!/bin/bash

echo "🧹 深度代码级重构分析和清理..."

# 创建备份目录
echo "📦 创建备份目录..."
mkdir -p backups/deep_refactor_$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backups/deep_refactor_$(date +%Y%m%d_%H%M%S)"

# 需要删除的旧文件和重复文件
OLD_FILES=(
    # 旧的测试文件
    "LanguageLearningApp/Views/ErrorTestView.swift"
    "LanguageLearningApp/Views/TestErrorView.swift"

    # 旧的脚本文件
    "fix_duplicate_files.sh"
    "fix_compilation_errors.sh"
    "fix_stringsdata_conflicts.sh"
    "xcode_deep_clean.sh"
    "fix_swiftybeaver_dependency.sh"

    # 旧的文档文件
    "refactoring-plan.md"
    "refactoring-progress-analysis.md"

    # TTS 相关的 README（如果不需要）
    "LanguageLearningApp/Services/TTS/README_KOKORO_INTEGRATION.md"

    # 可能存在的重复 Logger
    "LanguageLearningApp/Utilities/SimpleLogger.swift"

    # 需要重构的旧服务文件
    "LanguageLearningApp/Services/UserService.swift"
    "LanguageLearningApp/Services/LessonService.swift"
    "LanguageLearningApp/Services/EvaluationService.swift"
    "LanguageLearningApp/Services/PersonalizedLearningService.swift"
)

# 备份并删除旧文件
echo "🗑️  删除旧文件..."
for file in "${OLD_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  删除: $file"
        # 备份文件
        mkdir -p "$BACKUP_DIR/$(dirname "$file")"
        cp "$file" "$BACKUP_DIR/$file" 2>/dev/null || true
        # 删除文件
        rm -f "$file"
    else
        echo "  跳过: $file (文件不存在)"
    fi
done

# 清理空目录
echo "📁 清理空目录..."
find LanguageLearningApp -type d -empty -delete 2>/dev/null || true

# 清理 Xcode 缓存
echo "🧽 清理 Xcode 缓存..."
rm -rf ~/Library/Developer/Xcode/DerivedData/LanguageLearningApp-* 2>/dev/null || true

echo "✅ 旧文件清理完成！"
echo "📦 备份文件保存在: $BACKUP_DIR"

# 检查是否有其他可能的重复文件
echo ""
echo "🔍 检查可能的重复文件..."

# 查找可能的重复模型定义
echo "检查重复的模型定义..."
find LanguageLearningApp -name "*.swift" -exec grep -l "struct User\|class User" {} \; 2>/dev/null | head -5
find LanguageLearningApp -name "*.swift" -exec grep -l "struct PracticeSession\|class PracticeSession" {} \; 2>/dev/null | head -5
find LanguageLearningApp -name "*.swift" -exec grep -l "struct Evaluation\|class Evaluation" {} \; 2>/dev/null | head -5

echo ""
echo "🎯 下一步：统一错误处理"
echo "运行: bash unify_error_handling.sh"
