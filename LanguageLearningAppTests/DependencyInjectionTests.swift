import XCTest
@testable import LanguageLearningApp

/// 依赖注入系统测试
@MainActor
final class DependencyInjectionTests: XCTestCase {
    
    override func setUp() {
        super.setUp()
        // 注册依赖项
        DependencyRegistry.registerDependencies()
    }
    
    override func tearDown() {
        super.tearDown()
        // 清理容器（如果有清理方法的话）
    }
    
    /// 测试基础服务注册
    func testBasicServiceRegistration() {
        let container = DependencyContainer.shared
        
        // 测试网络服务
        XCTAssertNoThrow(try container.resolve(NetworkServiceProtocol.self))
        
        // 测试个性化学习服务
        XCTAssertNoThrow(try container.resolve(PersonalizedLearningServiceProtocol.self))
        
        // 测试每日练习统计服务
        XCTAssertNoThrow(try container.resolve(DailyPracticeStatsServiceProtocol.self))
    }
    
    /// 测试管理器注册
    func testManagerRegistration() {
        let container = DependencyContainer.shared
        
        // 测试用户管理器
        XCTAssertNoThrow(try container.resolve(UserManager.self))
        
        // 测试评估管理器
        XCTAssertNoThrow(try container.resolve(EvaluationManager.self))
        
        // 测试词汇管理器
        XCTAssertNoThrow(try container.resolve(VocabularyManager.self))
        
        // 测试练习管理器
        XCTAssertNoThrow(try container.resolve(PracticeManager.self))
        
        // 测试课程管理器
        XCTAssertNoThrow(try container.resolve(LessonManager.self))
    }
    
    /// 测试协议注册
    func testProtocolRegistration() {
        let container = DependencyContainer.shared
        
        // 测试用户管理器协议
        XCTAssertNoThrow(try container.resolve((any UserManagerProtocol).self))
        
        // 测试错误管理器协议
        XCTAssertNoThrow(try container.resolve((any ErrorManagerProtocol).self))
        
        // 测试本地化管理器协议
        XCTAssertNoThrow(try container.resolve((any LocalizationManagerProtocol).self))
        
        // 测试成就管理器协议
        XCTAssertNoThrow(try container.resolve((any AchievementManagerProtocol).self))
        
        // 测试评估管理器协议
        XCTAssertNoThrow(try container.resolve((any EvaluationManagerProtocol).self))
        
        // 测试词汇管理器协议
        XCTAssertNoThrow(try container.resolve((any VocabularyManagerProtocol).self))
        
        // 测试课程管理器协议
        XCTAssertNoThrow(try container.resolve((any LessonManagerProtocol).self))
        
        // 测试练习管理器协议
        XCTAssertNoThrow(try container.resolve((any PracticeManagerProtocol).self))
    }
    
    /// 测试 ViewModelFactory 创建
    func testViewModelFactoryCreation() {
        // 创建 ViewModelFactory
        let factory = DefaultViewModelFactory()
        
        // 测试创建各种 ViewModel
        XCTAssertNoThrow(factory.makeWordLearningViewModel())
        XCTAssertNoThrow(factory.makeAchievementViewModel())
        XCTAssertNoThrow(factory.makeDailyPracticeViewModel())
        XCTAssertNoThrow(factory.makePersonalizedPracticeViewModel(practiceId: nil))
        XCTAssertNoThrow(factory.makeProgressTrackingViewModel())
        XCTAssertNoThrow(factory.makeEvaluationViewModel())
        XCTAssertNoThrow(factory.makeEvaluationEntryViewModel())
        XCTAssertNoThrow(factory.makeDailyPracticeDashboardViewModel())
    }
    
    /// 测试单例模式
    func testSingletonPattern() {
        let container = DependencyContainer.shared
        
        // 测试用户管理器单例
        let userManager1 = try! container.resolve(UserManager.self)
        let userManager2 = try! container.resolve(UserManager.self)
        XCTAssertTrue(userManager1 === userManager2, "UserManager 应该是单例")
        
        // 测试评估管理器单例
        let evaluationManager1 = try! container.resolve(EvaluationManager.self)
        let evaluationManager2 = try! container.resolve(EvaluationManager.self)
        XCTAssertTrue(evaluationManager1 === evaluationManager2, "EvaluationManager 应该是单例")
    }
    
    /// 测试协议和具体类型的一致性
    func testProtocolConsistency() {
        let container = DependencyContainer.shared
        
        // 测试用户管理器协议和具体类型的一致性
        let userManager = try! container.resolve(UserManager.self)
        let userManagerProtocol = try! container.resolve((any UserManagerProtocol).self)
        XCTAssertTrue(userManager === (userManagerProtocol as! UserManager), "协议和具体类型应该返回同一个实例")
        
        // 测试评估管理器协议和具体类型的一致性
        let evaluationManager = try! container.resolve(EvaluationManager.self)
        let evaluationManagerProtocol = try! container.resolve((any EvaluationManagerProtocol).self)
        XCTAssertTrue(evaluationManager === (evaluationManagerProtocol as! EvaluationManager), "协议和具体类型应该返回同一个实例")
    }
    
    /// 测试错误处理
    func testErrorHandling() {
        let container = DependencyContainer.shared
        
        // 测试解析不存在的依赖项
        XCTAssertThrowsError(try container.resolve(String.self)) { error in
            // 应该抛出依赖项未注册的错误
            XCTAssertTrue(error.localizedDescription.contains("依赖项未注册"))
        }
    }
}
