import XCTest
import Combine
@testable import LanguageLearningApp

class DailyPracticeDashboardViewModelTests: XCTestCase {

    // 模拟服务
    var mockUserManager: UserManager!
    var mockEvaluationService: MockEvaluationService!
    var mockLearningPathService: MockPersonalizedLearningService!
    var mockPracticeManager: PracticeManager!

    // 视图模型
    var viewModel: DailyPracticeDashboardViewModel!

    // Combine 取消令牌
    var cancellables: Set<AnyCancellable>!

    override func setUp() {
        super.setUp()

        // 初始化模拟服务
        mockUserManager = UserManager.shared
        mockEvaluationService = MockEvaluationService()
        mockLearningPathService = MockPersonalizedLearningService()
        mockPracticeManager = PracticeManager.shared

        // 初始化视图模型，注入模拟服务
        viewModel = DailyPracticeDashboardViewModel(
            practiceManager: mockPracticeManager,
            evaluationService: mockEvaluationService,
            userManager: mockUserManager,
            learningPathService: mockLearningPathService
        )

        cancellables = []
    }

    override func tearDown() {
        mockUserManager = nil
        mockEvaluationService = nil
        mockLearningPathService = nil
        mockPracticeManager = nil
        viewModel = nil
        cancellables = nil
        super.tearDown()
    }

    // MARK: - Tests

    func testLoadUserInfo_SetsUserNameAndWelcomeMessage() {
        // 准备
        let expectation = XCTestExpectation(description: "Load user info")

        // 监听属性变化
        viewModel.$userName
            .dropFirst() // 跳过初始值
            .sink { _ in
                expectation.fulfill()
            }
            .store(in: &cancellables)

        // 执行
        viewModel.loadDashboardData()

        // 验证
        wait(for: [expectation], timeout: 1.0)
        XCTAssertFalse(viewModel.welcomeMessage.isEmpty, "欢迎消息不应为空")
    }

    func testLoadDailyGoals_SetsProgressValues() {
        // 准备
        let expectation = XCTestExpectation(description: "Load daily goals")

        // 监听属性变化
        viewModel.$minutesLearned
            .dropFirst() // 跳过初始值
            .sink { _ in
                expectation.fulfill()
            }
            .store(in: &cancellables)

        // 执行
        viewModel.loadDashboardData()

        // 验证
        wait(for: [expectation], timeout: 1.0)
        XCTAssertGreaterThan(viewModel.minutesLearned, 0, "学习分钟数应该大于0")
        XCTAssertGreaterThan(viewModel.dailyGoalMinutes, 0, "每日目标分钟数应该大于0")
        XCTAssertGreaterThanOrEqual(viewModel.practicesCompleted, 0, "完成练习数应该大于等于0")
        XCTAssertGreaterThan(viewModel.dailyGoalPractices, 0, "每日目标练习数应该大于0")
    }

    func testLoadRecommendedPractices_SetsRecommendedPractices() {
        // 准备
        let expectation = XCTestExpectation(description: "Load recommended practices")

        // 监听属性变化
        viewModel.$recommendedPractices
            .dropFirst() // 跳过初始值
            .sink { practices in
                if !practices.isEmpty {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)

        // 执行
        viewModel.loadDashboardData()

        // 验证
        wait(for: [expectation], timeout: 1.0)
        XCTAssertGreaterThan(viewModel.recommendedPractices.count, 0, "应该有推荐练习")
    }
}

// MARK: - 模拟服务实现

/// 模拟评估服务
class MockEvaluationService: EvaluationServiceProtocol {
    var mockAvailableEvaluations: [Evaluation] = []
    var mockEvaluationResults: [EvaluationResult] = []
    var mockError: Error?

    func getAvailableEvaluations() -> AnyPublisher<[Evaluation], Error> {
        if let error = mockError {
            return Fail(error: error).eraseToAnyPublisher()
        }

        return Just(mockAvailableEvaluations)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }

    func getUserEvaluationHistory() -> AnyPublisher<[EvaluationResult], Error> {
        if let error = mockError {
            return Fail(error: error).eraseToAnyPublisher()
        }

        return Just(mockEvaluationResults)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }

    // 实现其他必要的协议方法，但在测试中不使用
    func startEvaluation(id: UUID) -> AnyPublisher<Evaluation, Error> {
        fatalError("Not implemented for tests")
    }

    func submitAnswer(id: UUID, questionId: UUID, answer: String) -> AnyPublisher<Evaluation, Error> {
        fatalError("Not implemented for tests")
    }

    func completeEvaluation(id: UUID) -> AnyPublisher<Evaluation, Error> {
        fatalError("Not implemented for tests")
    }

    func getEvaluationResults(id: UUID) -> AnyPublisher<EvaluationResult, Error> {
        fatalError("Not implemented for tests")
    }

    var isNetworkAvailable: Bool = true
}

/// 模拟个性化学习服务
class MockPersonalizedLearningService: PersonalizedLearningServiceProtocol {
    var mockLearningPath: PersonalizedLearningPath?
    var mockError: Error?

    func getCurrentLearningPath() -> AnyPublisher<PersonalizedLearningPath, Error> {
        if let error = mockError {
            return Fail(error: error).eraseToAnyPublisher()
        }

        if let path = mockLearningPath {
            return Just(path)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }

        // 默认返回一个模拟的学习路径
        let defaultPath = PersonalizedLearningPath(
            id: UUID(),
            userId: UUID(),
            currentLevel: "初级 (A1)",
            targetLevel: "中级 (B1)",
            progress: 0.35,
            createdAt: Date(),
            updatedAt: Date()
        )

        return Just(defaultPath)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }

    // 实现其他必要的协议方法，但在测试中不使用
    func initiatePersonalizedLearning() -> AnyPublisher<UUID, Error> {
        fatalError("Not implemented for tests")
    }

    func getPersonalizedLearningStatus() -> AnyPublisher<Any, Error> {
        fatalError("Not implemented for tests")
    }

    func getNextExercise(pathId: UUID) -> AnyPublisher<Any, Error> {
        fatalError("Not implemented for tests")
    }

    func completeExercise(pathId: UUID, lessonId: UUID) -> AnyPublisher<Bool, Error> {
        fatalError("Not implemented for tests")
    }

    func submitPracticeAnswer(practiceId: UUID, exerciseId: UUID, answer: String) -> AnyPublisher<[String: Any], Error> {
        fatalError("Not implemented for tests")
    }

    func getPracticeHistory() -> AnyPublisher<[PracticeSession], Error> {
        fatalError("Not implemented for tests")
    }

    func getLearningStatus() -> AnyPublisher<[String: Any], Error> {
        fatalError("Not implemented for tests")
    }

    var isNetworkAvailable: Bool = true
}


