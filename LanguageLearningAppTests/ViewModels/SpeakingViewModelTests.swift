import XCTest
import Combine
import Speech
@testable import LanguageLearningApp

class SpeakingViewModelTests: XCTestCase {
    
    // 模拟服务
    var mockNetworkService: MockNetworkService!
    var mockErrorManager: MockErrorManager!
    var mockUserManager: MockUserManager!
    
    // 视图模型
    var viewModel: SpeakingViewModel!
    
    override func setUp() {
        super.setUp()
        
        // 初始化模拟服务
        mockNetworkService = MockNetworkService()
        mockErrorManager = MockErrorManager()
        mockUserManager = MockUserManager()
        
        // 初始化视图模型，注入模拟服务
        viewModel = SpeakingViewModel(
            networkService: mockNetworkService,
            errorManager: mockErrorManager,
            userManager: mockUserManager
        )
    }
    
    override func tearDown() {
        mockNetworkService = nil
        mockErrorManager = nil
        mockUserManager = nil
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - Tests
    
    func testLoadSpeakingExercisesFromAPI_Success() async {
        // 准备
        let testExercises = [
            SpeakingExercise(
                id: UUID(),
                title: "测试练习1",
                targetPhrase: "你好，我是测试",
                difficulty: .easy,
                category: "问候",
                instructions: "请说出这个句子",
                audioURL: URL(string: "https://example.com/audio1.mp3")!,
                transcript: "你好，我是测试",
                createdAt: Date()
            ),
            SpeakingExercise(
                id: UUID(),
                title: "测试练习2",
                targetPhrase: "今天天气很好",
                difficulty: .medium,
                category: "天气",
                instructions: "请说出这个句子",
                audioURL: URL(string: "https://example.com/audio2.mp3")!,
                transcript: "今天天气很好",
                createdAt: Date()
            )
        ]
        
        // 设置模拟响应
        mockNetworkService.mockResponse = testExercises
        
        // 执行
        await viewModel.loadSpeakingExercisesFromAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertEqual(viewModel.exercises.count, 2, "应该加载2个练习")
        XCTAssertEqual(viewModel.exercises[0].title, "测试练习1", "第一个练习标题应该匹配")
        XCTAssertEqual(viewModel.exercises[1].title, "测试练习2", "第二个练习标题应该匹配")
        XCTAssertEqual(viewModel.currentExercise?.title, "测试练习1", "当前练习应该是第一个练习")
        XCTAssertEqual(viewModel.transcribedText, "", "转录文本应该为空")
        XCTAssertFalse(viewModel.showFeedback, "不应该显示反馈")
    }
    
    func testLoadSpeakingExercisesFromAPI_Error() async {
        // 准备
        let testError = NSError(domain: "TestError", code: -1, userInfo: [NSLocalizedDescriptionKey: "测试错误"])
        
        // 设置模拟响应
        mockNetworkService.mockError = testError
        
        // 执行
        await viewModel.loadSpeakingExercisesFromAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertEqual(mockErrorManager.lastErrorType, .networkError("加载口语练习失败，请检查网络连接"), "应该显示网络错误")
    }
    
    func testSubmitSpeakingAnswerToAPI_Success() async {
        // 准备
        let testExercise = SpeakingExercise(
            id: UUID(),
            title: "测试练习",
            targetPhrase: "你好，我是测试",
            difficulty: .easy,
            category: "问候",
            instructions: "请说出这个句子",
            audioURL: URL(string: "https://example.com/audio.mp3")!,
            transcript: "你好，我是测试",
            createdAt: Date()
        )
        
        let testResponse = APIResponse(
            success: true,
            message: "提交成功",
            data: nil,
            error: nil,
            accuracy: 0.85,
            points: 17
        )
        
        // 设置模拟用户
        let testUser = User(
            id: UUID(),
            username: "测试用户",
            email: "<EMAIL>",
            name: "测试用户",
            avatar: nil,
            createdAt: Date(),
            updatedAt: Date(),
            lastLoginAt: Date(),
            isActive: true,
            settings: UserSettings.default,
            stats: UserStats(
                streakDays: 5,
                vocabularyCount: 10,
                listeningExerciseCount: 5,
                speakingExerciseCount: 3,
                points: 100,
                completedChallenges: 2,
                helpedUsers: 1,
                lastLoginDate: Date()
            )
        )
        mockUserManager.mockUser = testUser
        
        // 设置模拟响应
        mockNetworkService.mockResponse = testResponse
        
        // 设置视图模型状态
        viewModel.exercises = [testExercise]
        viewModel.currentExercise = testExercise
        
        // 创建测试录音URL
        let recordingURL = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("test_recording.m4a")
        
        // 执行
        await viewModel.submitSpeakingAnswerToAPI(recordingURL: recordingURL)
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertEqual(viewModel.feedback, "发音准确度: 85%", "反馈信息应该匹配")
        XCTAssertTrue(viewModel.showFeedback, "应该显示反馈")
        XCTAssertEqual(mockUserManager.updatedSpeakingExerciseCount, 4, "口语练习计数应该增加1")
        XCTAssertEqual(mockUserManager.updatedPoints, 117, "积分应该增加17")
    }
}

// MARK: - 模拟服务实现

/// 模拟错误管理器
class MockErrorManager: ErrorManager {
    var lastErrorType: AppError?
    
    override func showError(_ error: AppError) {
        lastErrorType = error
    }
}

/// 模拟用户管理器
class MockUserManager: UserManager {
    var mockUser: User?
    var updatedVocabularyCount: Int?
    var updatedSpeakingExerciseCount: Int?
    var updatedPoints: Int?
    
    override var currentUser: User? {
        return mockUser
    }
    
    override func updateUserVocabularyCount(_ count: Int) {
        updatedVocabularyCount = count
    }
    
    override func updateUserSpeakingExerciseCount(_ count: Int) {
        updatedSpeakingExerciseCount = count
    }
    
    override func updateUserPoints(_ points: Int) {
        updatedPoints = points
    }
}
