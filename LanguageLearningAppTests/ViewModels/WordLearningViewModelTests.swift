import XCTest
import Combine
@testable import LanguageLearningApp

class WordLearningViewModelTests: XCTestCase {
    
    // 模拟服务
    var mockNetworkService: MockNetworkService!
    var mockUserManager: MockUserManager!
    
    // 视图模型
    var viewModel: WordLearningViewModel!
    
    override func setUp() {
        super.setUp()
        
        // 初始化模拟服务
        mockNetworkService = MockNetworkService()
        mockUserManager = MockUserManager()
        
        // 初始化视图模型，注入模拟服务
        viewModel = WordLearningViewModel(
            networkService: mockNetworkService,
            userManager: mockUserManager
        )
    }
    
    override func tearDown() {
        mockNetworkService = nil
        mockUserManager = nil
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - Tests
    
    func testLoadWordsFromAPI_Success() async {
        // 准备
        let testWords = [
            Word(
                id: UUID(),
                text: "测试单词1",
                translation: "Test Word 1",
                pronunciation: "ceshi danci 1",
                partOfSpeech: "名词",
                difficulty: "初级",
                category: "常用词",
                exampleSentence: "这是一个测试单词。",
                exampleTranslation: "This is a test word.",
                isLearned: false,
                lastReviewed: nil
            ),
            Word(
                id: UUID(),
                text: "测试单词2",
                translation: "Test Word 2",
                pronunciation: "ceshi danci 2",
                partOfSpeech: "动词",
                difficulty: "初级",
                category: "常用词",
                exampleSentence: "这是另一个测试单词。",
                exampleTranslation: "This is another test word.",
                isLearned: false,
                lastReviewed: nil
            )
        ]
        
        // 设置模拟响应
        mockNetworkService.mockResponse = testWords
        
        // 执行
        await viewModel.loadWordsFromAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertEqual(viewModel.words.count, 2, "应该加载2个单词")
        XCTAssertEqual(viewModel.words[0].text, "测试单词1", "第一个单词文本应该匹配")
        XCTAssertEqual(viewModel.words[1].text, "测试单词2", "第二个单词文本应该匹配")
        XCTAssertEqual(viewModel.currentWord?.text, "测试单词1", "当前单词应该是第一个单词")
        XCTAssertEqual(viewModel.currentIndex, 0, "当前索引应该是0")
        XCTAssertFalse(viewModel.isShowingTranslation, "不应该显示翻译")
        XCTAssertFalse(viewModel.isShowingExample, "不应该显示例句")
    }
    
    func testLoadWordsFromAPI_Error() async {
        // 准备
        let testError = NSError(domain: "TestError", code: -1, userInfo: [NSLocalizedDescriptionKey: "测试错误"])
        
        // 设置模拟响应
        mockNetworkService.mockError = testError
        
        // 执行
        await viewModel.loadWordsFromAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证 - 应该保留示例单词
        XCTAssertFalse(viewModel.words.isEmpty, "应该保留示例单词")
    }
    
    func testMarkAsLearnedToAPI_Success() async {
        // 准备
        let testWord = Word(
            id: UUID(),
            text: "测试单词",
            translation: "Test Word",
            pronunciation: "ceshi danci",
            partOfSpeech: "名词",
            difficulty: "初级",
            category: "常用词",
            exampleSentence: "这是一个测试单词。",
            exampleTranslation: "This is a test word.",
            isLearned: false,
            lastReviewed: nil
        )
        
        // 设置模拟用户
        let testUser = User(
            id: UUID(),
            username: "测试用户",
            email: "<EMAIL>",
            name: "测试用户",
            avatar: nil,
            createdAt: Date(),
            updatedAt: Date(),
            lastLoginAt: Date(),
            isActive: true,
            settings: UserSettings.default,
            stats: UserStats(
                streakDays: 5,
                vocabularyCount: 10,
                listeningExerciseCount: 5,
                speakingExerciseCount: 3,
                points: 100,
                completedChallenges: 2,
                helpedUsers: 1,
                lastLoginDate: Date()
            )
        )
        mockUserManager.mockUser = testUser
        
        // 设置视图模型状态
        viewModel.words = [testWord]
        viewModel.currentWord = testWord
        viewModel.currentIndex = 0
        
        // 执行
        await viewModel.markAsLearnedToAPI()
        
        // 等待主线程更新
        let expectation = XCTestExpectation(description: "Wait for main thread update")
        DispatchQueue.main.async {
            expectation.fulfill()
        }
        await fulfillment(of: [expectation], timeout: 1.0)
        
        // 验证
        XCTAssertEqual(mockUserManager.updatedVocabularyCount, 11, "词汇计数应该增加1")
    }
}

// MARK: - 模拟服务实现

/// 模拟网络服务
class MockNetworkService: NetworkServiceProtocol {
    var mockResponse: Any?
    var mockError: Error?
    
    func request<T: Decodable>(_ endpoint: APIEndpoint) async throws -> T {
        if let error = mockError {
            throw error
        }
        
        if let response = mockResponse as? T {
            return response
        } else if let response = mockResponse {
            // 尝试将响应转换为请求的类型
            let data = try JSONSerialization.data(withJSONObject: response, options: [])
            let decoder = JSONDecoder()
            return try decoder.decode(T.self, from: data)
        }
        
        throw NSError(domain: "MockNetworkService", code: -1, userInfo: [NSLocalizedDescriptionKey: "没有模拟响应"])
    }
    
    func requestNoResponse(_ endpoint: APIEndpoint) async throws {
        if let error = mockError {
            throw error
        }
    }
    
    func requestRawData(_ endpoint: APIEndpoint) async throws -> Data {
        if let error = mockError {
            throw error
        }
        
        if let response = mockResponse {
            return try JSONSerialization.data(withJSONObject: response, options: [])
        }
        
        return Data()
    }
    
    func isNetworkAvailable() -> Bool {
        return true
    }
}

/// 模拟用户管理器
class MockUserManager: UserManager {
    var mockUser: User?
    var updatedVocabularyCount: Int?
    
    override var currentUser: User? {
        return mockUser
    }
    
    override func updateUserVocabularyCount(_ count: Int) {
        updatedVocabularyCount = count
    }
}
