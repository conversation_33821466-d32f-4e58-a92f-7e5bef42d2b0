import XCTest
import SwiftUI
@testable import LanguageLearningApp

/// 错误处理测试
class ErrorHandlingTests: XCTestCase {
    
    var errorManager: ErrorManager!
    
    override func setUp() {
        super.setUp()
        errorManager = ErrorManager.shared
        errorManager.clearError()
        errorManager.clearErrorLog()
    }
    
    override func tearDown() {
        errorManager.clearError()
        errorManager.clearErrorLog()
        super.tearDown()
    }
    
    // MARK: - 基础错误处理测试
    
    func testShowError() {
        // Given
        let testError = AppError.customError("测试错误")
        
        // When
        errorManager.showError(testError)
        
        // Then
        XCTAssertEqual(errorManager.currentError?.localizedDescription, "测试错误")
        XCTAssertTrue(errorManager.showError)
        XCTAssertEqual(errorManager.errorLog.count, 1)
    }
    
    func testClearError() {
        // Given
        let testError = AppError.customError("测试错误")
        errorManager.showError(testError)
        
        // When
        errorManager.clearError()
        
        // Then
        XCTAssertNil(errorManager.currentError)
        XCTAssertFalse(errorManager.showError)
    }
    
    func testShowErrorWithMessage() {
        // Given
        let message = "自定义错误消息"
        
        // When
        errorManager.showError(message: message, severity: .warning)
        
        // Then
        XCTAssertEqual(errorManager.currentError?.localizedDescription, message)
        XCTAssertTrue(errorManager.showError)
        XCTAssertEqual(errorManager.currentError?.severity, .warning)
    }
    
    // MARK: - 错误日志测试
    
    func testErrorLogging() {
        // Given
        let error1 = AppError.networkError("网络错误")
        let error2 = AppError.authenticationFailed("认证失败")
        
        // When
        errorManager.showError(error1)
        errorManager.showError(error2)
        
        // Then
        XCTAssertEqual(errorManager.errorLog.count, 2)
        XCTAssertEqual(errorManager.errorLog[0].error.localizedDescription, error1.localizedDescription)
        XCTAssertEqual(errorManager.errorLog[1].error.localizedDescription, error2.localizedDescription)
    }
    
    func testErrorLogLimit() {
        // Given - 创建超过100个错误
        for i in 1...105 {
            let error = AppError.customError("错误 \(i)")
            errorManager.showError(error)
        }
        
        // Then - 应该只保留最新的100个
        XCTAssertEqual(errorManager.errorLog.count, 100)
        XCTAssertEqual(errorManager.errorLog.last?.error.localizedDescription, "错误 105")
    }
    
    func testClearErrorLog() {
        // Given
        errorManager.showError(.customError("测试错误1"))
        errorManager.showError(.customError("测试错误2"))
        
        // When
        errorManager.clearErrorLog()
        
        // Then
        XCTAssertEqual(errorManager.errorLog.count, 0)
    }
    
    // MARK: - 错误类型测试
    
    func testNetworkErrors() {
        let errors: [AppError] = [
            .networkError("网络连接失败"),
            .serverError("服务器错误"),
            .requestTimeout,
            .noInternetConnection,
            .apiError("API错误")
        ]
        
        for error in errors {
            errorManager.showError(error)
            XCTAssertNotNil(errorManager.currentError)
            XCTAssertTrue(errorManager.showError)
            errorManager.clearError()
        }
    }
    
    func testAuthenticationErrors() {
        let errors: [AppError] = [
            .authenticationFailed("认证失败"),
            .userNotFound,
            .invalidCredentials,
            .registrationFailed("注册失败")
        ]
        
        for error in errors {
            errorManager.showError(error)
            XCTAssertNotNil(errorManager.currentError)
            XCTAssertTrue(errorManager.showError)
            XCTAssertEqual(errorManager.currentError?.severity, .error)
            errorManager.clearError()
        }
    }
    
    func testDataErrors() {
        let errors: [AppError] = [
            .dataLoadFailed("数据加载失败"),
            .dataSaveFailed("数据保存失败"),
            .dataNotFound,
            .invalidData("无效数据")
        ]
        
        for error in errors {
            errorManager.showError(error)
            XCTAssertNotNil(errorManager.currentError)
            XCTAssertTrue(errorManager.showError)
            errorManager.clearError()
        }
    }
    
    // MARK: - 错误导出测试
    
    func testExportErrorLog() {
        // Given
        errorManager.showError(.networkError("网络错误"))
        errorManager.showError(.authenticationFailed("认证失败"))
        
        // When
        let exportData = errorManager.exportErrorLog()
        
        // Then
        XCTAssertNotNil(exportData)
        
        if let data = exportData,
           let content = String(data: data, encoding: .utf8) {
            XCTAssertTrue(content.contains("Time,Severity,Error"))
            XCTAssertTrue(content.contains("网络错误"))
            XCTAssertTrue(content.contains("认证失败"))
        }
    }
    
    // MARK: - 错误严重程度测试
    
    func testErrorSeverity() {
        // 测试不同严重程度的错误
        let infoError = AppError.operationCancelled
        let warningError = AppError.networkError("网络警告")
        let errorError = AppError.authenticationFailed("认证错误")
        
        XCTAssertEqual(infoError.severity, .info)
        XCTAssertEqual(warningError.severity, .warning)
        XCTAssertEqual(errorError.severity, .error)
    }
}
