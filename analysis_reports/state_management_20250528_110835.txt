=== 状态管理模式分析 ===

@StateObject 使用:
      98
LanguageLearningApp/LanguageLearningApp.swift:    @StateObject private var userManager: UserManager = UserManager.shared
LanguageLearningApp/LanguageLearningApp.swift:    @StateObject private var achievementManager: AchievementManager = AchievementManager.shared
LanguageLearningApp/LanguageLearningApp.swift:    @StateObject private var lessonManager: LessonManager = LessonManager.shared
LanguageLearningApp/LanguageLearningApp.swift:    @StateObject private var errorManager: ErrorManager = ErrorManager.shared
LanguageLearningApp/LanguageLearningApp.swift:    @StateObject private var localizationManager = LocalizationManager.shared
LanguageLearningApp/Features/Settings/Views/ThemeSettingsView.swift:    @StateObject private var themeManager = ThemeManager.shared
LanguageLearningApp/Features/Settings/Views/ThemeSettingsView.swift:    @StateObject private var localizationManager = LocalizationManager.shared
LanguageLearningApp/Features/DailyPractice/Views/DailyPracticeDashboardView.swift:    @StateObject private var viewModel = PersonalizedPracticeViewModel()
LanguageLearningApp/Features/DailyPractice/Views/DailyPracticeDashboardView.swift:    @StateObject private var progressViewModel = ProgressTrackingViewModel()
LanguageLearningApp/Features/DailyPractice/Views/DailyPracticeDashboardView.swift:    @StateObject private var localizationManager = LocalizationManager.shared

@ObservedObject 使用:
      27
LanguageLearningApp/Features/DailyPractice/Views/PracticeSessionView.swift:    @ObservedObject var viewModel: PersonalizedPracticeViewModel
LanguageLearningApp/Features/DailyPractice/Views/PracticeCompletionView.swift:    @ObservedObject var viewModel: PersonalizedPracticeViewModel
LanguageLearningApp/Features/DailyPractice/Views/ProgressTrackingView.swift:    @ObservedObject var viewModel: ProgressTrackingViewModel
LanguageLearningApp/Features/Evaluation/Views/EvaluationResultView.swift:    @ObservedObject var viewModel: EvaluationResultViewModel
LanguageLearningApp/Features/Evaluation/Views/EvaluationIntroView.swift:    @ObservedObject var viewModel: EvaluationViewModel
LanguageLearningApp/Features/Evaluation/Views/EvaluationQuestionView.swift:    @ObservedObject var viewModel: EvaluationViewModel
LanguageLearningApp/Views/Settings/TTSSettingsView.swift:    @ObservedObject private var ttsManager = TTSManager.shared
LanguageLearningApp/Views/ProfileView.swift:    @ObservedObject var userManager = UserManager.shared
LanguageLearningApp/Views/ProfileView.swift:    @ObservedObject var achievementManager = AchievementManager.shared
LanguageLearningApp/Views/WordLearningView.swift:    @ObservedObject var viewModel: WordLearningViewModel

@EnvironmentObject 使用:
      26
LanguageLearningApp/Main/MainTabView.swift:    @EnvironmentObject private var themeManager: ThemeManager
LanguageLearningApp/Views/Settings/AccountSettingsView.swift:    @EnvironmentObject private var userManager: UserManager
LanguageLearningApp/Views/Settings/AccountSettingsView.swift:    @EnvironmentObject private var userManager: UserManager
LanguageLearningApp/Views/Settings/NotificationSettingsView.swift:    @EnvironmentObject private var userManager: UserManager
LanguageLearningApp/Views/Settings/LanguageSettingsView.swift:    @EnvironmentObject private var userManager: UserManager
LanguageLearningApp/Views/ProfileView.swift:    @EnvironmentObject private var errorManager: ErrorManager
LanguageLearningApp/Views/WordLearningView.swift:    @EnvironmentObject private var errorManager: ErrorManager
LanguageLearningApp/Views/WordLearningView.swift:    @EnvironmentObject private var errorManager: ErrorManager
LanguageLearningApp/Views/DailyPracticeView.swift:    @EnvironmentObject private var errorManager: ErrorManager
LanguageLearningApp/Views/SpeakingView.swift:    @EnvironmentObject private var errorManager: ErrorManager
