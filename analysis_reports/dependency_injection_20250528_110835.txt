=== 依赖注入使用分析 ===

DependencyContainer 使用:
LanguageLearningApp/ViewModels/AchievementViewModel.swift:        networkService: NetworkServiceProtocol = DependencyContainer.shared.resolve(NetworkServiceProtocol.self),
LanguageLearningApp/ViewModels/AchievementViewModel.swift:        userManager: UserManager = DependencyContainer.shared.resolve(UserManager.self)
LanguageLearningApp/ViewModels/AchievementManager.swift:        userManager: any UserManagerProtocol = DependencyContainer.shared.resolve(UserManager.self),
LanguageLearningApp/ViewModels/AchievementManager.swift:        lessonManager: any LessonManagerProtocol = DependencyContainer.shared.resolve(LessonManager.self),
LanguageLearningApp/ViewModels/AchievementManager.swift:        storageManager: StorageManagerProtocol = DependencyContainer.shared.resolve(StorageManagerProtocol.self),
LanguageLearningApp/ViewModels/AchievementManager.swift:        networkService: NetworkServiceProtocol = DependencyContainer.shared.resolve(NetworkServiceProtocol.self),
LanguageLearningApp/ViewModels/DailyPracticeViewModel.swift:        practiceManager: PracticeManager = DependencyContainer.shared.resolve(PracticeManager.self)
LanguageLearningApp/DI/DependencyContainer.swift:public class DependencyContainer {
LanguageLearningApp/DI/DependencyContainer.swift:    public static let shared = DependencyContainer()
LanguageLearningApp/DI/DependencyContainer.swift:public extension DependencyContainer {
LanguageLearningApp/DI/DependencyContainer.swift:    private let container: DependencyContainer
LanguageLearningApp/DI/DependencyContainer.swift:    public init(container: DependencyContainer = .shared) {
LanguageLearningApp/DI/DependencyContainer.swift:    private let container: DependencyContainer
LanguageLearningApp/DI/DependencyContainer.swift:    public init(container: DependencyContainer = .shared) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(NetworkServiceProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(NetworkProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            NetworkAdapter(networkService: DependencyContainer.shared.resolve(NetworkServiceProtocol.self))
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(APIClientProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            APIClient(networkService: DependencyContainer.shared.resolve(NetworkServiceProtocol.self))
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(StorageManagerProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(PersonalizedLearningServiceProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(DailyPracticeStatsServiceProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(EvaluationLocalDataSource.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(EvaluationRemoteDataSource.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let apiClient = DependencyContainer.shared.resolve(APIClientProtocol.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(AnyEvaluationRepository.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let remoteDataSource = DependencyContainer.shared.resolve(EvaluationRemoteDataSource.self)
LanguageLearningApp/DI/DependencyRegistry.swift:            let localDataSource = DependencyContainer.shared.resolve(EvaluationLocalDataSource.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton((any EvaluationRepositoryProtocol).self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            return DependencyContainer.shared.resolve(AnyEvaluationRepository.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(EvaluationManager.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let repository = DependencyContainer.shared.resolve((any EvaluationRepositoryProtocol).self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(APIDataSourceManager.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(LessonLocalDataSourceProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(LessonRemoteDataSourceProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let apiClient = DependencyContainer.shared.resolve(APIClientProtocol.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(LessonRepositoryProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(LessonManager.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let repository = DependencyContainer.shared.resolve(LessonRepositoryProtocol.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(VocabularyLocalDataSourceProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(VocabularyRemoteDataSourceProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let apiClient = DependencyContainer.shared.resolve(APIClientProtocol.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(VocabularyRepositoryProtocol.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let remoteDataSource = DependencyContainer.shared.resolve(VocabularyRemoteDataSourceProtocol.self)
LanguageLearningApp/DI/DependencyRegistry.swift:            let localDataSource = DependencyContainer.shared.resolve(VocabularyLocalDataSourceProtocol.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(VocabularyManager.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let repository = DependencyContainer.shared.resolve(VocabularyRepositoryProtocol.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(PracticeLocalDataSource.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(PracticeRemoteDataSource.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let apiClient = DependencyContainer.shared.resolve(APIClientProtocol.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(AnyPracticeRepository.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let localDataSource = DependencyContainer.shared.resolve(PracticeLocalDataSource.self)
LanguageLearningApp/DI/DependencyRegistry.swift:            let remoteDataSource = DependencyContainer.shared.resolve(PracticeRemoteDataSource.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(PracticeManager.self) {
LanguageLearningApp/DI/DependencyRegistry.swift:            let repository = DependencyContainer.shared.resolve(AnyPracticeRepository.self)
LanguageLearningApp/DI/DependencyRegistry.swift:        DependencyContainer.shared.registerSingleton(UserManager.self) {
LanguageLearningApp/Features/DailyPractice/ViewModels/DailyPracticeDashboardViewModel.swift:        practiceManager: PracticeManager = DependencyContainer.shared.resolve(PracticeManager.self),
LanguageLearningApp/Features/DailyPractice/ViewModels/DailyPracticeDashboardViewModel.swift:        evaluationManager: EvaluationManager = DependencyContainer.shared.resolve(EvaluationManager.self),
LanguageLearningApp/Features/DailyPractice/ViewModels/DailyPracticeDashboardViewModel.swift:        userManager: UserManager = DependencyContainer.shared.resolve(UserManager.self),
LanguageLearningApp/Features/DailyPractice/ViewModels/DailyPracticeDashboardViewModel.swift:        learningPathService: PersonalizedLearningServiceProtocol = DependencyContainer.shared.resolve(PersonalizedLearningServiceProtocol.self),
LanguageLearningApp/Features/DailyPractice/ViewModels/DailyPracticeDashboardViewModel.swift:        statsService: DailyPracticeStatsServiceProtocol = DependencyContainer.shared.resolve(DailyPracticeStatsServiceProtocol.self)
LanguageLearningApp/Features/DailyPractice/ViewModels/ProgressTrackingViewModel.swift:    init(personalizedLearningService: PersonalizedLearningServiceProtocol = DependencyContainer.shared.resolve(PersonalizedLearningServiceProtocol.self)) {
LanguageLearningApp/Features/DailyPractice/ViewModels/PersonalizedPracticeViewModel.swift:    init(practiceId: UUID? = nil, personalizedLearningService: PersonalizedLearningServiceProtocol = DependencyContainer.shared.resolve(PersonalizedLearningServiceProtocol.self)) {
LanguageLearningApp/Features/DailyPractice/Services/PracticeRemoteDataSource.swift:    public static let shared = PracticeRemoteDataSource(apiClient: DependencyContainer.shared.resolve(APIClientProtocol.self))
LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationViewModel.swift:    init(evaluationManager: EvaluationManager = DependencyContainer.shared.resolve(EvaluationManager.self)) {
LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationResultViewModel.swift:        self.evaluationManager = DependencyContainer.shared.resolve(EvaluationManager.self)
LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationResultViewModel.swift:        self.personalizedLearningService = DependencyContainer.shared.resolve(PersonalizedLearningServiceProtocol.self)
LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationResultViewModel.swift:        self.evaluationManager = DependencyContainer.shared.resolve(EvaluationManager.self)
LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationResultViewModel.swift:        self.personalizedLearningService = DependencyContainer.shared.resolve(PersonalizedLearningServiceProtocol.self)
LanguageLearningApp/Features/Evaluation/ViewModels/EvaluationEntryViewModel.swift:    init(evaluationManager: EvaluationManager = DependencyContainer.shared.resolve(EvaluationManager.self)) {
LanguageLearningApp/Features/Evaluation/Views/EvaluationResultLoadingView.swift:        evaluationManager: DependencyContainer.shared.resolve(EvaluationManager.self)
LanguageLearningApp/Features/Evaluation/Views/StartNewEvaluationView.swift:        evaluationManager: DependencyContainer.shared.resolve(EvaluationManager.self)
LanguageLearningApp/Features/Evaluation/Services/EvaluationRemoteDataSource.swift:    public static let shared = EvaluationRemoteDataSource(apiClient: DependencyContainer.shared.resolve(APIClientProtocol.self))

协议定义 (Protocol):
LanguageLearningApp/Features/DailyPractice/Repositories/PracticeRepository.swift:public protocol PracticeRepositoryProtocol: RepositoryProtocol where T == PracticeSession, ID == UUID {
LanguageLearningApp/Features/DailyPractice/Services/PersonalizedLearningServiceProtocol.swift:protocol PersonalizedLearningServiceProtocol {
LanguageLearningApp/Features/DailyPractice/Services/DailyPracticeStatsService.swift:public protocol DailyPracticeStatsServiceProtocol {
LanguageLearningApp/Features/User/Repositories/UserRepository.swift:public protocol UserRepositoryProtocol: RepositoryProtocol where T == User, ID == UUID {
LanguageLearningApp/Features/User/Services/UserRemoteDataSourceProtocol.swift:public protocol UserRemoteDataSourceProtocol {
LanguageLearningApp/Features/User/Services/UserLocalDataSourceProtocol.swift:public protocol UserLocalDataSourceProtocol {
LanguageLearningApp/Features/Lessons/DataSources/LessonRemoteDataSource.swift:public protocol LessonRemoteDataSourceProtocol {
LanguageLearningApp/Features/Lessons/DataSources/LessonLocalDataSource.swift:public protocol LessonLocalDataSourceProtocol {
LanguageLearningApp/Features/Lessons/Repositories/LessonRepository.swift:public protocol LessonRepositoryProtocol {
LanguageLearningApp/Features/Evaluation/Repositories/EvaluationRepository.swift:public protocol EvaluationRepositoryProtocol: RepositoryProtocol where T == Evaluation, ID == UUID {
LanguageLearningApp/Features/Evaluation/Services/EvaluationLocalDataSourceProtocol.swift:public protocol EvaluationLocalDataSourceProtocol {
LanguageLearningApp/Features/Evaluation/Services/EvaluationRemoteDataSourceProtocol.swift:public protocol EvaluationRemoteDataSourceProtocol {
LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyDataSourceProtocols.swift:public protocol VocabularyRemoteDataSourceProtocol {
LanguageLearningApp/Features/Vocabulary/DataSources/VocabularyDataSourceProtocols.swift:public protocol VocabularyLocalDataSourceProtocol {
LanguageLearningApp/Features/Vocabulary/Repositories/VocabularyRepository.swift:public protocol VocabularyRepositoryProtocol {
LanguageLearningApp/API/NetworkTypes.swift:public protocol APIEndpointProtocol {
LanguageLearningApp/API/NetworkTypes.swift:public protocol NetworkProtocol {
LanguageLearningApp/API/NetworkTypes.swift:public protocol APIClientProtocol {
LanguageLearningApp/API/NetworkServiceProtocol.swift:public protocol NetworkServiceProtocol {
LanguageLearningApp/Protocols/UserManagerProtocol.swift:public protocol UserManagerProtocol: ObservableObject {
LanguageLearningApp/Protocols/AchievementManagerProtocol.swift:protocol AchievementManagerProtocol: ObservableObject {
LanguageLearningApp/Protocols/RepositoryProtocol.swift:public protocol RepositoryProtocol<T, ID> {
LanguageLearningApp/Protocols/RepositoryProtocol.swift:public protocol LocalDataSourceProtocol<T, ID> {
LanguageLearningApp/Protocols/RepositoryProtocol.swift:public protocol RemoteDataSourceProtocol<T, ID> {
LanguageLearningApp/Protocols/ErrorManagerProtocol.swift:protocol ErrorManagerProtocol: ObservableObject {
LanguageLearningApp/Protocols/LessonManagerProtocol.swift:protocol LessonManagerProtocol: ObservableObject {
LanguageLearningApp/Protocols/TTSManagerProtocol.swift:protocol TTSManagerProtocol: ObservableObject {
LanguageLearningApp/Protocols/StorageManagerProtocol.swift:public protocol StorageManagerProtocol {
LanguageLearningApp/Protocols/PracticeManagerProtocol.swift:public protocol PracticeManagerProtocol: ObservableObject {
