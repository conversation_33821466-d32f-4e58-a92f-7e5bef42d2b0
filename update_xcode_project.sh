#!/bin/bash

# 清理派生数据
echo "正在清理 Xcode 派生数据..."
rm -rf ~/Library/Developer/Xcode/DerivedData/LanguageLearningApp-*

echo "完成！"
echo "请在 Xcode 中执行以下操作："
echo "1. 关闭并重新打开项目"
echo "2. 选择项目文件 -> TARGETS -> Build Phases -> Compile Sources"
echo "3. 确认以下文件已经不在编译列表中："
echo "   - LanguageLearningApp/App/ContentView.swift"
echo "   - LanguageLearningApp/ViewModels/PracticeManager.swift"
echo "   - LanguageLearningApp/ViewModels/UserManager.swift"
echo "4. 选择 Product -> Clean Build Folder"
echo "5. 重新构建项目" 